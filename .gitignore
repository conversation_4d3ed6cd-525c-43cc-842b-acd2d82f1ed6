# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.


# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
next_public_key
tooling-build-logs/
__Cherries__/
_FUTURE_/
tooling-build-logs-old/
__pycache__

gen-scripts/
packages/tooling/env-manager/env-to-gh-scoped-env.ts
packages/tooling/env-manager/env-to-github.ts
packages/tooling/env-manager/prefix-map.json
packages/tooling/env-manager/run-env-to-gh.ts
repo-scripts/.env-repo-discord
packages/db/
_DEV_LOG
