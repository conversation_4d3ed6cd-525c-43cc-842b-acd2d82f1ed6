# STATUS: working
name: CI Pipeline Dev

on:
  push:
    branches: ["chore/github-actions"]
  pull_request:
    branches: ["chore/github-actions"]

jobs:
  home-base:
    runs-on: self-hosted
    environment: dev-turbo

    services: # Define services directly in GHA
      postgres:
        image: postgres:15-alpine # Or your preferred version
        env:
          POSTGRES_DB: test_db
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432 # Map to host port for easier access from runner
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:


      # New Step: Generate .env-prod and .env-prod-redis files from secrets
      - name: Generate .env files from GitHub Secrets
        run: |

          # --- Paths for .env files ---
          # Assuming your docker-compose.yml is in the repository root.
          # If your apps/trans_api/.env-prod is literally in apps/trans_api,
          # and .env-prod-redis is in the repo root (same as docker-compose.yml).
          TRANS_API_ENV_FILE="${GITHUB_WORKSPACE}/apps/trans_api/.env-prod"
          REDIS_ENV_FILE="${GITHUB_WORKSPACE}/.env-prod-redis"
          
          # IMPORTANT: Make sure the paths above match your actual project structure
          # and where docker-compose.yml expects them relative to its location.
          # For example, if docker-compose.yml is in repo-root/docker/, then
          # REDIS_ENV_FILE would be `${GITHUB_WORKSPACE}/docker/.env-prod-redis`.

          echo "Generating ${TRANS_API_ENV_FILE}..."
          {
            echo "REDIS_HOST=redis" # Hardcoded value
            echo "REDIS_DB=0"       # Hardcoded value
            echo "REDIS_PORT=6379"  # Hardcoded value
            echo "REDIS_USERNAME=${{ secrets.REDIS_USERNAME }}" # Assuming this is a secret
            echo "REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}" # Assuming this is a secret
            echo "CLERK_ISSUER=${{ secrets.CLERK_ISSUER }}"
            echo "CLERK_JWKS_URL=${{ secrets.CLERK_JWKS_URL }}"
            echo "CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}"
            # Using printf for multi-line CLERK_PEM_PUBLIC_KEY
            printf "CLERK_PEM_PUBLIC_KEY=%s\n" "${{ secrets.CLERK_PEM_PUBLIC_KEY }}"
            echo "LOGTAIL_SOURCE_TOKEN=${{ secrets.LOGTAIL_SOURCE_TOKEN }}"
          } > "${TRANS_API_ENV_FILE}"

          echo "Generating ${REDIS_ENV_FILE}..."
          {
            echo "REDIS_PORT=6379"
            echo "REDIS_DB=0"
            echo "REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL,CONFIG"
            echo "REDIS_HOST=redis"
            echo "REDIS_USERNAME=${{ secrets.REDIS_USERNAME }}"
            echo "REDIS_PASSWORD=${{ secrets.REDIS_PASSWORD }}"
            echo "REDIS_PASSWORD_OLDSTYLE=${{ secrets.REDIS_PASSWORD_OLDSTYLE }}"
            echo "REDIS_ARGS=${{ secrets.REDIS_ARGS }}"
            echo "REDIS_URI=${{ secrets.REDIS_URI }}"
          } > "${REDIS_ENV_FILE}"
          
          echo "Generated .env files from GitHub Secrets."
          ls -l "${TRANS_API_ENV_FILE}" "${REDIS_ENV_FILE}" # Verify creation
          cat "${TRANS_API_ENV_FILE}" # For debugging, remove in production
          cat "${REDIS_ENV_FILE}"     # For debugging, remove in production

    
      - name: Check shell-utils.sh integrity
        run: |
          set -x # Enable tracing for this step too
          cd "$GITHUB_WORKSPACE"
          echo "Checking syntax of shell-utils.sh..."

          # Test if bash can parse the script without executing it
          bash -n ./repo-scripts/shell-utils.sh
          if [ $? -ne 0 ]; then
            echo "ERROR: Syntax error found in shell-utils.sh. Please fix it."
            exit 1 # Fail the workflow if syntax is bad
          fi
          echo "shell-utils.sh syntax OK."

          echo "Checking readability and hidden characters in shell-utils.sh..."
          # List file details to check permissions
          ls -l ./repo-scripts/shell-utils.sh
          # Use 'cat -vE' to reveal non-printable characters and end-of-line markers
          cat -vE ./repo-scripts/shell-utils.sh

        shell: /usr/bin/bash -e {0}

      - name: Checkout
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false
          standalone: true

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: "pnpm"

      - name: Install dependencies
        run: pnpm install
        env:
          PNPM_HOME: ~/.pnpm-store



      - name: Source Environment Setup and Run TurboRepo Build Task
        run: |
          set -x

          # Assign GIT_REPO_DIR directly in the shell, ensuring $GITHUB_WORKSPACE expands
          export GIT_REPO_DIR="$GITHUB_WORKSPACE" 

          cd "$GITHUB_WORKSPACE"
          source ./repo-scripts/repo-env-setup.sh
          make all-prod         # This should now find the .env files generated above

        env:
          PNPM_HOME: /home/<USER>/setup-pnpm/node_modules/.bin
          # Ensure GIT_REPO_DIR is correctly set for your scripts
          # GIT_REPO_DIR: "$GITHUB_WORKSPACE"  ### ?????


      - name: Wait for Redis - localhost 6379 [ X sleep attempts for 2s ]
        run: |
          for i in {1..5}; do
            if nc -z localhost 6379; then
              echo "Redis is accepting connections!"
              break
            else
              echo "Waiting for Redis to open port 6379..."
              sleep 2
            fi
          done

      - name: Test Redis
        run: |
          redis-cli -h localhost -p 6379 -u angels -a "$REDIS_PASSWORD" ping

      - name: Wait for DB to be ready
        run: |
          for i in {1..10}; do
            pg_isready -h localhost -p 5432 -U test_user && break
            echo "Waiting for Postgres..."
            sleep 3
          done

      # Run Python Tests (using a test DB)
      - name: Run Pytest
        run: |
          # Ensure your FastAPI test config points to the GHA service host
          export DATABASE_URL="postgresql://test_user:test_password@localhost:5432/test_db"
          make test-trans_api # Or pnpm test --filter=tooling-trans_api
