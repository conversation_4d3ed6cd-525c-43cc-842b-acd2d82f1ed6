#!/bin/bash

# Check if a filename is provided as an argument
if [ -z "$1" ]; then
  echo "💥 Error: No file provided"
  exit 1
fi

# Check if the provided file exists
if [ ! -f "$1" ]; then
  echo "💥 Error: File '$1' not found"
  exit 1
fi

# Source the file to load the environment variables
source "$1"

# Now you can run any other commands that rely on the environment variables
echo "🌲 Environment variables from '$1' loaded successfully."

# Example command that needs those env vars
# ./your-command-here
