name: Redis Test Workflow

on:
  push:
    branches: 
      - never

# jobs:
#   build:
#     runs-on: self-hosted

#     services:
#       redis:
#         image: redis:latest
#         ports:
#           - 6379:6379
#         options: >-
#           --requirepass Thiscoulddo
#         env:
#           REDIS_PASSWORD: Thiscoulddo
    
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4

#       - name: Wait for Redis to start
#         run: |
#           sleep 10  # Give Redis time to start

#       - name: Test Redis connection
#         run: |
#           redis-cli -h 127.0.0.1 -p 6379 -a Thiscoulddo PING


jobs:
  # Label of the container job
  container-job:
    # Containers must run in Linux based operating systems
    runs-on: self-hosted
    # runs-on: ubuntu-latest
    # Docker Hub image that `container-job` executes in
    container: node:20-bookworm-slim

    # Service containers to run with `container-job`
    services:
      # Label used to access the service container
      redis:
        # Docker Hub image
        image: redis
        # Set health checks to wait until redis has started
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      # Downloads a copy of the code in your repository before running CI tests
      - name: Check out repository code
        uses: actions/checkout@v4

      # Performs a clean installation of all dependencies in the `package.json` file
      # For more information, see https://docs.npmjs.com/cli/ci.html
      - name: Install dependencies
        run: npm ci

      - name: Connect to Redis
        # Runs a script that creates a Redis client, populates
        # the client with data, and retrieves data
        run: node client.js
        # Environment variable used by the `client.js` script to create a new Redis client.
        env:
          # The hostname used to communicate with the Redis service container
          REDIS_HOST: redis
          # The default Redis port
          REDIS_PORT: 6379
