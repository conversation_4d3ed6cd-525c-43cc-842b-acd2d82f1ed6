name: Docker Image for CI

on:
  push:
    branches: [never-branch]
  pull_request:
    branches: [never-branch]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm install

      - name: Run Makefile
        run: make web-prod
