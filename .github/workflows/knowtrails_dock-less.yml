# STATUS: working
name: CI Pipeline Dev

on:
  push:
    branches: ["chore/github-actions"]
  pull_request:
    branches: ["chore/github-actions"]

jobs:

  piecemeal:
    runs-on: self-hosted
    # runs-on: ubuntu-latest
    environment: dev-turbo

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # - name: Debug file structure
      #   run: |
      #     echo "Working Directory: $(pwd)"
      #     ls -la
      #     echo "---"
      #     find . -type f

      # - name: Cache pnpm store
      #   uses: actions/cache@v4
      #   with:
      #     path: ~/.pnpm-store
      #     key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
      #     restore-keys: |
      #       ${{ runner.os }}-pnpm-

      - name: <PERSON>ache TurboRepo
        uses: actions/cache@v4
        with:
          path: apps/web/.turbo
          # key: ${{ runner.os }}-turbo-${{ hashFiles('**/package.json') }}
          key: ${{ runner.os }}-turbo-${{ hashFiles('**/pnpm-lock.yaml', '**/turbo.json') }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false
          standalone: true

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: "pnpm"

      - name: Install dependencies
        run: pnpm install
        env:
          PNPM_HOME: ~/.pnpm-store

      # # Run lint
      # - name: Lint web project with TurboRepo
      #   run: pnpm turbo run lint --filter=web

      # Run build
      - name: Build web project with TurboRepo
        env:
          NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: ${{ vars.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY }}
        run: pnpm turbo run build --filter=web

      # Optional Python setup if you want to run Python code/tests here
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"

      - name: Install uv
        run: python -m pip install uv

      # Install python dependencies
      - name: Install python dependencies
        working-directory: apps/trans_api
        run: uv sync --frozen

      # TESTING SECTION =====================================================

      # - name: Wait for Postgres
      #   run: |
      #     until pg_isready -h postgres -p 5432; do
      #       echo "Waiting for postgres..."; sleep 2;
      #     done

      # - name: TESTING Python BE
      #   run: uv run pytest  # or uvicorn main:app --reload etc.

      # - name: TESTING web FE
      #   env:
      #     DATABASE_URL: ******************************************/test_db
      #   run: turbo run test --filter=web

      # DEPLOYMENT SECTION ==================================================

      # # write SSH_PRIVATE_KEY into ~/.ssh/id_rsa with proper permissions
      # # run ssh-keyscan for known_hosts, as mentioned before.
      # - name: Set up SSH
      #   run: |
      #     mkdir -p ~/.ssh
      #     echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
      #     chmod 600 ~/.ssh/id_rsa
      #     ssh-keyscan ${{ secrets.VM_HOST }} >> ~/.ssh/known_hosts

      # # Missing SSH agent forwarding --- not sure about this one
      # # If you plan to clone private repos or need agent forwarding inside the VM, add:
      # # - name: Start ssh-agent
      # #   run: eval "$(ssh-agent -s)"

      # - name: Deploy to VM
      #   run: |
      #     ssh -o StrictHostKeyChecking=no user@your-vm-ip "cd /path/to/project && git pull && npm install && npm run start"
      #   env:
      #     SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}

  redis-test:
    runs-on: ubuntu-latest
    environment: dev-turbo

    env:
      REDIS_PASSWORD: ${{ secrets.REDIS_PASSWORD }}

    steps:
      - name: Start Redis in the background
        run: |
          docker run -d --name redis-test \
            -p 6379:6379 \
            redis \
            redis-server --requirepass "$REDIS_PASSWORD"

      # - name: Wait for Redis to be ready [ sleep for 5s ]
      #   run: |
      #     sleep 5

      - name: Wait for Redis - localhost 6379 [ X sleep attempts for 2s ]
        run: |
          for i in {1..5}; do
            if nc -z localhost 6379; then
              echo "Redis is accepting connections!"
              break
            else
              echo "Waiting for Redis to open port 6379..."
              sleep 2
            fi
          done

      - name: Test Redis
        run: |
          redis-cli -h localhost -p 6379 -a "$REDIS_PASSWORD" ping
          
      - name: Stop Redis server
        run: |
          docker stop redis-test

