#!/bin/bash

# Setup Git Hooks for KnowTrails Project
# This script sets up pre-commit hooks to run tests before commits

echo "🔧 Setting up Git hooks for KnowTrails..."

# Create hooks directory if it doesn't exist
mkdir -p .git/hooks

# Create pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash

echo "🧪 Running pre-commit tests..."

# Run quick test suite (skip the slower wisdom algorithm tests)
cd apps/discord-trans
if ! hatch run test tests/test_game_commands.py tests/test_search_commands.py -q; then
    echo "❌ Core tests failed. Commit aborted."
    exit 1
fi

cd ../../packages/discord-trans-db
if ! hatch run test -q; then
    echo "❌ Database tests failed. Commit aborted."
    exit 1
fi

cd ../tran-hits
if ! hatch run test:test -q; then
    echo "❌ Search tests failed. Commit aborted."
    exit 1
fi

echo "✅ Pre-commit tests passed!"
exit 0
EOF

# Make the hook executable
chmod +x .git/hooks/pre-commit

echo "✅ Git hooks setup complete!"
echo ""
echo "📋 What was configured:"
echo "   • Pre-commit hook: Runs core tests before each commit"
echo "   • <PERSON>ps wisdom algorithm tests for speed"
echo "   • Prevents commits if core functionality is broken"
echo ""
echo "🚀 To bypass hooks (emergency commits only):"
echo "   git commit --no-verify -m 'Emergency commit'"
