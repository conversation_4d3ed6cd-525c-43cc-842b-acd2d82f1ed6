import datetime
import os

import typer
from PIL import Image, UnidentifiedImageError

app = typer.Typer()


@app.command()
def shrink(
    source_dir: str = typer.Argument(
        ..., help="Directory containing images to shrink."
    ),
    percentage: int = typer.Argument(
        ...,
        min=1,
        max=99,
        help="Percentage to shrink images by (e.g., 50 for 50% reduction).",
    ),
):
    """
    Shrinks images in a source directory by a given percentage and saves them
    to a timestamped 'shrink' subdirectory.
    """
    if not os.path.isdir(source_dir):
        typer.echo(f"Error: Source directory '{source_dir}' not found.")
        raise typer.Exit(code=1)

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir_name = f"{timestamp}_shrink"
    output_dir = os.path.join(source_dir, output_dir_name)

    try:
        os.makedirs(output_dir, exist_ok=True)
        typer.echo(f"Created output directory: {output_dir}")
    except OSError as e:
        typer.echo(f"Error creating output directory '{output_dir}': {e}")
        raise typer.Exit(code=1)

    shrink_factor = percentage / 100.0
    processed_count = 0
    failed_count = 0

    typer.echo(f"Scanning directory: {source_dir}")

    for idx, filename in enumerate(os.listdir(source_dir), 1):
        file_path = os.path.join(source_dir, filename)

        if not os.path.isfile(file_path):
            continue  # Skip directories and other non-files

        try:
            # Attempt to open as an image
            with Image.open(file_path) as img:
                original_width, original_height = img.size
                new_width = int(original_width * shrink_factor)
                new_height = int(original_height * shrink_factor)

                # Ensure dimensions are at least 1x1
                new_width = max(1, new_width)
                new_height = max(1, new_height)

                # Resize the image
                # Use LANCZOS filter for good quality downsampling
                resized_img = img.resize(
                    (new_width, new_height), Image.Resampling.LANCZOS
                )

                # Determine output path and format
                img_format = img.format
                save_kwargs = {}
                if img_format in ["PNG", "GIF"]:
                    # Copy transparency info if it exists
                    if "transparency" in img.info:
                        save_kwargs["transparency"] = img.info["transparency"]
                    # For PNG, ensure alpha channel is kept if present
                    if img_format == "PNG" and img.mode in ("RGBA", "LA"):
                        save_kwargs["keep_alpha"] = True

                # Build new filename: {timestamp}_{index:03d}.{ext}
                ext = (
                    os.path.splitext(filename)[1].lstrip(".")
                    or img_format.lower()
                    or "png"
                )
                new_filename = f"{timestamp}_{idx:03d}.{ext}"
                output_path = os.path.join(output_dir, new_filename)

                # Save the resized image
                resized_img.save(output_path, format=img_format, **save_kwargs)
                typer.echo(
                    f"Shrunk '{filename}' -> '{new_filename}' ({original_width}x{original_height} -> {new_width}x{new_height})"
                )
                processed_count += 1

        except UnidentifiedImageError:
            # Not an image file, skip
            typer.echo(f"Skipping '{filename}': Not a recognized image file.")
        except Exception as e:
            typer.echo(f"Error processing '{filename}': {e}")
            failed_count += 1

    typer.echo("\n--- Summary ---")
    typer.echo(f"Processed {processed_count} image(s).")
    if failed_count > 0:
        typer.echo(f"Failed to process {failed_count} file(s).")
    typer.echo("---------------")


if __name__ == "__main__":
    app()
