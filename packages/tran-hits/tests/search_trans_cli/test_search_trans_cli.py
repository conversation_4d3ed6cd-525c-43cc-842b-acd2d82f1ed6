import json
import os
import subprocess

import pytest

# from binary_trans.trans_prepper import Hit

TRANS_PILE_DIR = os.path.join(os.path.dirname(__file__), "../trans_pile")


def phrase_in_slurp(transcript_path, phrase):
    with open(transcript_path) as f:
        transcript = json.load(f)
    # If transcript is a dict, get the 'transcript' key
    if isinstance(transcript, dict) and "transcript" in transcript:
        transcript = transcript["transcript"]
    slurp = " ".join([line["text"] for line in transcript])
    return phrase.lower() in slurp.lower()


@pytest.mark.parametrize(
    "video_id,phrase,should_find",
    [
        ("36GT2zI8lVA", "magnets together", True),
        ("36GT2zI8lVA", "want to know is", True),
        ("36GT2zI8lVA", "notfoundphrase", False),
    ],
)
def test_search_trans_cli_phrase(video_id, phrase, should_find):
    transcript_path = os.path.join(TRANS_PILE_DIR, f"{video_id}.json")
    result = subprocess.run(
        [
            "python",
            "-m",
            "search_trans_cli",
            "search",
            phrase,
            transcript_path,
        ],
        capture_output=True,
        text=True,
    )
    output = result.stdout
    if should_find:
        output_json = json.loads(output)
        # print(phrase)
        # print(output_json["text"])
        # Check that the phrase is in the slurp, not just the returned line

        assert phrase_in_slurp(transcript_path, phrase)
        assert "error" not in output_json

    else:
        assert "not found" in output.lower()
