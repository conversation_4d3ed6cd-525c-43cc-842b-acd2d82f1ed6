import pytest
from binary_trans.trans_prepper import TranscriptPrepper

# hatch run test:test -- -v
#  4534  hatch env find test
#  4535  source /home/<USER>/.local/share/hatch/env/pip-compile/tran-hits/x2LyRFsO/test/bin/activate && pip install -e .
#  4536  hatch run test pip install -e .


@pytest.fixture
def sample_transcript():
    return {
        "version": 1,
        "transcript": [
            {"start": 0, "text": "Hello"},
            {"start": 6, "text": "world!"},
            {"start": 13, "text": "Bye."},
        ],
    }


@pytest.fixture
def feynman_transcript():
    return {
        "version": 1,
        "video_id": "PsgBtOVzHKI",
        "transcript": [
            {
                "text": "you take any crazy idea uh well I don't",
                "start": 0.12,
                "duration": 4.96,
            },
            {
                "text": "know it's hard to make up a very crazy",
                "start": 3.199,
                "duration": 3.361,
            },
            {
                "text": "one they witches or something like that",
                "start": 5.08,
                "duration": 2.64,
            },
            {
                "text": "you tell about what people used to",
                "start": 6.56,
                "duration": 3.039,
            },
            {
                "text": "believe in witches and of course nobody",
                "start": 7.72,
                "duration": 3.4,
            },
            {
                "text": "believes in witches now and you say how",
                "start": 9.599,
                "duration": 2.721,
            },
            {
                "text": "could they believe in witches then you",
                "start": 11.12,
                "duration": 3.32,
            },
            {
                "text": "turn around you say let's see what",
                "start": 12.32,
                "duration": 3.879,
            },
            {
                "text": "witches do we believe in now what",
                "start": 14.44,
                "duration": 3.4,
            },
            {
                "text": "ceremonies do we do every morning we",
                "start": 16.199,
                "duration": 4.201,
            },
            {
                "text": "brush our teeth what is the evidence",
                "start": 17.84,
                "duration": 3.96,
            },
            {
                "text": "that the brushing of teeth does us any",
                "start": 20.4,
                "duration": 3.6,
            },
            {
                "text": "good in cavity so you start wondering",
                "start": 21.8,
                "duration": 6.639,
            },
            {
                "text": "are we all imagine in the the as the",
                "start": 24.0,
                "duration": 7.039,
            },
            {
                "text": "Earth turns on the orbit there's a Edge",
                "start": 28.439,
                "duration": 4.441,
            },
            {
                "text": "between light and dark and along that",
                "start": 31.039,
                "duration": 5.121,
            },
            {
                "text": "edge all the people along that edge and",
                "start": 32.88,
                "duration": 5.24,
            },
            {"text": "we doing the same", "start": 36.16, "duration": 4.52},
            {
                "text": "ritual for no good reason just like in",
                "start": 38.12,
                "duration": 4.959,
            },
            {
                "text": "the Middle Ages they had other rituals",
                "start": 40.68,
                "duration": 4.68,
            },
            {
                "text": "and you try to Picture This Perpetual",
                "start": 43.079,
                "duration": 4.8,
            },
            {
                "text": "line of toothbrushers going around the",
                "start": 45.36,
                "duration": 4.719,
            },
            {
                "text": "earth it's to take the world from",
                "start": 47.879,
                "duration": 4.241,
            },
            {
                "text": "another point of view now it may be ...",
                "start": 50.079,
                "duration": 3.721,
            },
        ],
    }


def test_build_char_index_map(sample_transcript):
    trans_map = TranscriptPrepper(sample_transcript["transcript"])
    # The code adds +1 for each line (space), so expected values are:
    assert trans_map.cc_map == [0, 6, 13, 18]


def test_build_char_index_map_feynman(feynman_transcript):
    trans_map = TranscriptPrepper(feynman_transcript["transcript"])
    # The code adds +1 for each line (space), so expected values are:
    expected = [0]
    count = 0
    for line in feynman_transcript["transcript"]:
        count += len(line["text"]) + 1
        expected.append(count)
    assert trans_map.cc_map == expected


def test_find_line_index(sample_transcript):
    trans_map = TranscriptPrepper(sample_transcript["transcript"])

    # Print trans_map map for debugging
    print(trans_map.cc_map)
    # Character offset 0 → line 0
    assert trans_map.find_line_index(0) == 0
    # Offset 6 → line 1 (inside " world!")
    assert trans_map.find_line_index(6) == 1
    # Offset 15 → line 2 (" Bye.")
    assert trans_map.find_line_index(15) == 2


def test_get_hit_context(sample_transcript):
    trans_map = TranscriptPrepper(sample_transcript["transcript"])
    hit = trans_map.get_hit_context(6)
    assert hit.start == 6
    assert hit.text == sample_transcript["transcript"][1]["text"]
    # context before and after should be lines 0 and 2 respectively
    # assert hit.context_before == sample_transcript["transcript"][0]
    # assert hit.context_after == sample_transcript["transcript"][2]


def test_required_fields_v1(sample_transcript):
    # Validate top-level required fields
    assert "version" in sample_transcript
    assert sample_transcript["version"] == 1
    assert "transcript" in sample_transcript
    # Validate required fields in each transcript line
    for line in sample_transcript["transcript"]:
        assert "start" in line
        assert "text" in line


def test_TranscriptPrepper_version_check():
    # Should not raise for version 1
    TranscriptPrepper([{"start": 0, "text": "foo"}], version=1)
    # Should raise for unsupported version
    with pytest.raises(ValueError, match="Unsupported transcript version: 2"):
        TranscriptPrepper([{"start": 0, "text": "foo"}], version=2)


# Example test using the Feynman transcript
def test_feynman_transcript_char_index_map(feynman_transcript):
    trans_map = TranscriptPrepper(feynman_transcript["transcript"])
    # Just check the char_index_map is the correct length (segments + 1)
    assert len(trans_map.cc_map) == len(feynman_transcript["transcript"]) + 1
    # Optionally, check the first and last values
    assert trans_map.cc_map[0] == 0
    assert trans_map.cc_map[-1] == sum(
        len(line["text"]) + 1 for line in feynman_transcript["transcript"]
    )


def test_binary_search_trans_mcc_location(feynman_transcript):
    from binary_trans.search import binary_search_trans_match

    transcript = feynman_transcript["transcript"]
    trans_map = TranscriptPrepper(transcript)
    # Let's pick a character offset (mcc) inside the second line
    # For example, offset 10 should be in line 1 (second segment)
    mcc = 10
    idx = binary_search_trans_match(
        trans_map.cc_map, 0, len(trans_map.cc_map) - 1, mcc
    )
    assert idx == 0
    # Also test a boundary: offset at the start of line 3
    mcc = trans_map.cc_map[2]
    idx = binary_search_trans_match(
        trans_map.cc_map, 0, len(trans_map.cc_map) - 1, mcc
    )
    assert idx == 2
    # And at the end
    mcc = trans_map.cc_map[-2] + 1
    idx = binary_search_trans_match(
        trans_map.cc_map, 0, len(trans_map.cc_map) - 1, mcc
    )
    assert idx == len(trans_map.cc_map) - 2


def test_feynman_cc_map_first_line(feynman_transcript):
    trans_map = TranscriptPrepper(feynman_transcript["transcript"])
    # First line: cc_map[0] = 0, cc_map[1] = len(text) + 1
    first_text = feynman_transcript["transcript"][0]["text"]
    assert trans_map.cc_map[0] == 0
    assert trans_map.cc_map[1] == len(first_text) + 1


def test_feynman_cc_map_increments(feynman_transcript):
    trans_map = TranscriptPrepper(feynman_transcript["transcript"])
    cc_map = trans_map.cc_map
    transcript = feynman_transcript["transcript"]
    # Check that each increment is len(text) + 1
    for i, line in enumerate(transcript):
        expected = cc_map[i] + len(line["text"]) + 1
        assert cc_map[i + 1] == expected
