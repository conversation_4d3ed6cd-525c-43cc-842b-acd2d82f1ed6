import pytest
from binary_trans.search import binary_search_trans_match


@pytest.fixture
def sample_accumulated_chars():
    # example: 3 lines of lengths 5, 10, 7 → cumulative offsets: 0,5,15,22
    return [0, 5, 15, 22]


def test_exact_match_low(sample_accumulated_chars):
    # mcc matches exactly arr[0]
    assert binary_search_trans_match(sample_accumulated_chars, 0, 3, 0) == 0


def test_exact_match_mid(sample_accumulated_chars):
    # mcc matches arr[1]
    assert binary_search_trans_match(sample_accumulated_chars, 0, 3, 5) == 1


def test_exact_match_high(sample_accumulated_chars):
    # mcc matches arr[3]
    assert binary_search_trans_match(sample_accumulated_chars, 0, 3, 22) == 3


def test_between_bounds(sample_accumulated_chars):
    # mcc falls between arr[1] and arr[2], expect index 1
    assert binary_search_trans_match(sample_accumulated_chars, 0, 3, 6) == 1


def test_out_of_bounds_high(sample_accumulated_chars):
    # mcc above last index, expect -1 or error behavior
    result = binary_search_trans_match(sample_accumulated_chars, 0, 3, 30)
    assert result == -1 or (result >= 0 and result <= 3)


def test_out_of_bounds_low(sample_accumulated_chars):
    # mcc below 0, expect -1 or error behavior
    result = binary_search_trans_match(sample_accumulated_chars, 0, 3, -1)
    assert result == -1 or (result >= 0 and result <= 3)
