import json
from re import Pattern

import typer
from binary_trans.search_utils import (
    search_tran_slurp,
)
from binary_trans.trans_prepper import TranscriptPrepper
from utils.gen import build_regex_word_boundary, make_slug

app = typer.Typer()


def make_redis_key(phrase: str, transcript_path: str) -> str:
    # get the filename less extension from the transcript_path
    video_id = transcript_path.split("/")[-1].split(".")[0]
    search_slug = make_slug(phrase)
    # for redis or db, hash key of source and phrase
    return hash(video_id + search_slug)


@app.command()
def find(mcc: int, transcript_path: str) -> dict:
    """
    Find the line for a matched character count (mcc) in the given transcript.
    """
    with open(transcript_path) as f:
        transcript = json.load(f)

    # If transcript is a dict, get the 'transcript' key
    if isinstance(transcript, dict) and "transcript" in transcript:
        transcript_lines = [line["text"] for line in transcript["transcript"]]
    else:
        transcript_lines = [line["text"] for line in transcript]

    # This command is not refactored to use the shared search utility, as it is for mcc lookup
    # If you want to refactor this as well, please clarify
    result = {}
    return result


@app.command()
def search(
    phrase: str,
    transcript_path: str,
    load_redis: bool = typer.Option(
        False, "--load-redis", help="Store result in Redis"
    ),
    load_pg: bool = typer.Option(
        False, "--load-pg", help="Store result in Postgres"
    ),
) -> None:
    """
    Search for a phrase in the transcript and return the hit context for the first match.
    Optionally store the result in Redis or Postgres.
    """
    with open(transcript_path) as f:
        transcript = json.load(f)

    # If transcript is a dict, get the 'transcript' key
    if isinstance(transcript, dict) and "transcript" in transcript:
        transcript_lines = transcript["transcript"]  # list of dicts
    else:
        transcript_lines = transcript  # list of dicts

    trans_prepper = TranscriptPrepper(transcript_lines)
    regex_pattern: Pattern[str] = build_regex_word_boundary([phrase])
    results = search_tran_slurp(trans_prepper, regex_pattern)

    if not results:
        typer.echo(
            json.dumps({"error": f"Phrase '{phrase}' not found."}, indent=2)
        )
    else:
        typer.echo(json.dumps(results, indent=2))
    ### ===============================================================
    ### Use shared search utility Case B that AI went and mangled !!
    #
    # matches = search_phrase_in_transcript(
    #     transcript_lines, phrase, context=1, regex=False, ignore_case=True
    # )
    #
    # results = []
    # for idx, matched_line, context_lines in matches:
    #     result = {
    #         "line_number": idx,
    #         "matched_line": matched_line,
    #         "context": context_lines,
    #     }
    #     results.append(result)
    #     typer.echo(json.dumps(result, indent=2))
    #
    # if not results:
    #     typer.echo(
    #         json.dumps({"error": f"Phrase '{phrase}' not found."}, indent=2)
    #     )
    # # ===============================================================

    if load_pg or load_redis:
        key = make_redis_key(phrase, transcript_path)
        if load_redis:
            typer.echo(
                # put into redis with key "sr_disk_v1:cache_id" with value results
                f"[INFO] Would store result in Redis at {key} (not implemented)"
            )

        if load_pg:
            typer.echo(
                f"[INFO] Would store result in Postgres at {key} (not implemented)"
            )
    return


if __name__ == "__main__":
    app()
