"""
binary_trans/search.py

Binary search utility to find the transcript line index
corresponding to a given matched character count (mcc).

Parameters:
arr: List of integers representing the accumulated character counts for each trans line.
low: Lower bound of the search range.
high: Upper bound of the search range.
mcc: Target matched character count to search for.

Returns:
Index in arr where mcc is located, or -1 if not found.

Details:
The array contains accumulated character counts for the start of each line in
a transcript file.
Note: arr[0] has the value 0 as the start of the first line.

"""

import logging
import os
from typing import List, Optional

BINARY_SEARCH_TRANS_LOG_LEVEL = os.environ.get(
    "BINARY_SEARCH_TRANS_LOG_LEVEL", logging.WARNING
)
logger = logging.getLogger(__name__)
logger.setLevel(int(BINARY_SEARCH_TRANS_LOG_LEVEL))
logger.setLevel(10)


def binary_search_trans_match(
    arr: List[int], low: int, high: int, mcc: int
) -> int:
    SEARCH_ERROR = -1

    def log_found(index: int, reason: str):
        logger.debug(
            "FOUND target of: %d at %d value of %s %s",
            mcc,
            index,
            arr[index],
            reason,
        )
        return index

    def check_boundaries() -> Optional[int]:
        """Check mcc match at beginning the line (range)
        Returns the index of matched line or None.
        """
        if arr[low] == mcc:
            return log_found(low, "Beginning of low line")
        if high < len(arr) and arr[high] == mcc:
            return log_found(high, "Beginning of high line")
        return None

    def handle_small_range():
        logger.debug(" *** handle_small_range *** low: %d high: %d", low, high)
        if arr[low] > mcc:
            logger.error("Error: mcc match char count should > low")
            return SEARCH_ERROR
        if arr[low] == mcc:
            return log_found(low, "--- Beginning of low line")
        if arr[low + 1] == mcc:
            return log_found(low + 1, "--- Beginning of low +1 line")
        if arr[low + 1] > mcc:
            return log_found(low, "... arr[low+1] > mcc")
        if arr[high] > mcc:
            return log_found(high - 1, "... arr[high] > mcc")
        if high + 1 < len(arr) and arr[high + 1] > mcc:
            return log_found(high, "... arr[high+1] > mcc")
        return None

    def recursive_search():
        logger.debug("recursive_search")
        mid = (high + low) // 2
        logger.info("%d | %d : %d : %d", mcc, low, mid, high)

        if arr[mid] == mcc:
            return log_found(mid, "Beginning of mid line")
        if arr[mid] > mcc:
            logger.debug("reducing high to mid as [arr[mid] > mcc]")
            return binary_search_trans_match(arr, low, mid, mcc)
        logger.debug("increasing low to mid as [arr[mid] <= mcc]")
        return binary_search_trans_match(arr, mid, high, mcc)

    # I have chosen to fail gracefully and swallow this match attempt
    # instead of bring the whole system down KAREN .. check yo logs
    if high < low:
        logger.error("Error: unexpected low > high for this implementation?")
        return -1
        # raise RuntimeWarning(
        #     "Error: unexpected low > high for this implementation?"
        # )

    if mcc < arr[0]:
        logger.error("Error: mcc < arr[0]")
        return -1
        # raise RuntimeWarning("Error: mcc < arr[0]")

    if mcc > arr[-1]:
        logger.error("Error: mcc > arr[-1]")
        return -1
        # raise RuntimeWarning("Error: mcc > arr[-1]")

    logger.info("%d | %d : -- : %d", mcc, low, high)

    result = check_boundaries()
    if result is not None:
        return result

    if high - low <= 2:
        result = handle_small_range()
        if result is not None:
            return result

    return recursive_search()
