"""
search_utils.py

Shared transcript search logic for CLI and Discord bot.
"""

import logging
import re
from typing import List, Tu<PERSON>, cast

from binary_trans.search import binary_search_trans_match
from binary_trans.trans_prepper import Hit, TranscriptPrepper

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def search_tran_slurp(
    trans_prepper: TranscriptPrepper,
    reg_str: re.Pattern,
):
    hits: list[Hit] = []
    for m in reg_str.finditer(trans_prepper.slurp):
        line_found = binary_search_trans_match(
            trans_prepper.cc_map, 0, len(trans_prepper.cc_map), m.start()
        )

        # if line_found < 0:
        #     logger.error(f"corruption when reviewing {video_id}")
        #     continue

        # print(f"***** DEBUG: line_found: {line_found}")
        # print(f"***** DEBUG: trans_prepper.cc_map: {trans_prepper.cc_map}")
        # print(f"***** DEBUG: trans_prepper.trans: {trans_prepper.trans}")
        # print(f"***** DEBUG: trans_prepper.slurp: {trans_prepper.slurp}")

        # map_line = trans_prepper.cc_map[line_found]
        # trans_line = trans_prepper.trans[line_found]
        hit: Hit = trans_prepper.trans[line_found]
        # print(f"***** DEBUG: map_line: {map_line}")
        # print(f"***** DEBUG: trans_line: {trans_line}")
        # print(f"***** DEBUG: hit: {hit}")

        # text = trans_prepper.trans[line_found]["text"]
        # start = trans_prepper.trans[line_found]["start"]
        # print(f"***** DEBUG: start: {start}")
        # print(f"***** DEBUG: text: {text}")

        # hit = Hit(
        #     start=trans_prepper.cc_map[line_found].start,
        #     # duration=trans[line_found].duration,
        #     text=trans_prepper.cc_map[line_found].text,
        # )
        hits.append(hit)

    return hits
