from dataclasses import dataclass
from typing import cast

from .search import binary_search_trans_match

# class MediaType(Enum):
#     AUDIO = "audio"
#     VIDEO = "youtube"
#
# class MediaSource:
#     def __init__(self, kind: MediaType, source: str):
#         self.kind = kind
#         self.source = source


# class SearchHit(BaseModel):
#     start: Optional[float] = Field(default=None)
#     duration: Optional[float] = Field(default=None)
#     text: Optional[str] = Field(default=None)


@dataclass
class Hit:
    start: float
    text: str


class TranscriptPrepper:
    def __init__(
        self,
        transcript: list[dict[str, object]],
        version: int = 1,
    ):
        if version != 1:
            raise ValueError(f"Unsupported transcript version: {version}")
        self.trans = transcript
        self.slurp: str = ""
        self.cc_map = self._build_char_index_map()

    def _build_char_index_map(self) -> list[int]:
        count = 0
        acc = [0]
        for line in self.trans:
            count += len(line.get("text", "")) + 1
            self.slurp += cast(str, line.get("text", "")) + " "
            acc.append(count)
        return acc

    def find_line_index(self, mcc: int) -> int:
        return binary_search_trans_match(
            self.cc_map, 0, len(self.cc_map) - 1, mcc
        )

    def get_hit_context(self, mcc: int) -> Hit:
        idx = self.find_line_index(mcc)
        line = self.trans[idx]
        return Hit(
            start=line["start"],
            text=line["text"],
        )
