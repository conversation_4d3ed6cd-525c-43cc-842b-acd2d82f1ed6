"""Utility file to help unclutter code"""

import base64
import logging
import os
import re
import time
from datetime import datetime, timezone

# from typing import Optional, Pattern

# from PIL import Image # type: ignore

YT_TRANS_UTIL_LOG_LEVEL = os.environ.get(
    "YT_TRANS_UTIL_LOG_LEVEL", logging.WARNING
)
logger = logging.getLogger(__name__)
logger.setLevel(int(YT_TRANS_UTIL_LOG_LEVEL))


def pic_to_base64(image_path: str) -> str:
    """
    Convert an image file to a base64 encoded string.

    Args:
        image_path: The path to an image file (e.g. .png, .jpg)

    Returns:
        A base64 encoded string representation of the image.
    """
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
    base64_encoded = base64.b64encode(image_data).decode("utf-8")
    return base64_encoded


# <svg
#   xmlns="http://www.w3.org/2000/svg"
#   viewBox="0 0 16 16">
#   <text x="0" y="14"> </text>
# </svg>

# data:image/svg+xml,%3Csvg%20xmlns=
# 'http://www.w3.org/2000/svg'%20viewBox='0%200%2016%2016'%3E%3Ctext%20x=
# '0'%20y='14'%3E %3C/text%3E%3C/svg%3E
# <link rel="icon" href="data:image/svg+xml,%3Csvg%20xmlns=
# 'http://www.w3.org/2000/svg'%20viewBox='0%200%2016%2016'%3E%3Ctext%20x=
# '0'%20y='14'%3E %3C/text%3E%3C/svg%3E" type="image/svg+xml" />

# def iconify(filename: str) -> None:
#     """
#     Create a Windows icon file (.ico) from a given image file.

#     filename: str
#         Path to an image file (e.g. .png, .jpg)

#     The resulting icon file will be saved as "logo.ico" in the current directory.

#     Note: This function requires the Pillow library to be installed.

#     """

#     img = Image.open(filename)
#     # Optionally, you may specify the icon sizes you want:
#     # icon_sizes = [(16,16), (32, 32), (48, 48), (64,64)]
#     icon_sizes = [(32, 32)]
#     img.save("logo.ico", sizes=icon_sizes)


def get_current_datetime() -> str:
    """
    Return the current date and time as a string in the format MM/DD/YYYY, HH:MM:SS.
    """
    now = datetime.now()
    return now.strftime("%m/%d/%Y, %H:%M:%S")


def date_to_UTC(date_str: str) -> int:
    """
    Convert a given date string to a Unix timestamp in UTC.

    Args:
        date_str: string
            A date string in ISO format (e.g. "2023-02-16T14:30:00Z")

    Returns:
        int
            The given date as a Unix timestamp in UTC
    """

    date_obj = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
    date = int(date_obj.timestamp())
    return date


def now_UTC_sec():
    utc_now = datetime.now(timezone.utc)
    timestamp = int(utc_now.timestamp())
    return timestamp


def now_ISO_8601_UTC():
    now = datetime.now(timezone.utc)
    iso_format = now.strftime("%Y-%m-%dT%H:%M:%SZ")
    # print(iso_format)  # e.g., 2025-06-01T13:45:12Z
    return iso_format


def now_UTC_sec2() -> int:
    import time

    return int(time.time())


def get_chronological_time(num_digits: int) -> int:
    # Get the current time in seconds since the epoch
    current_time = int(time.time())
    # Extract the last few digits
    chopped_time = current_time % (10**num_digits)
    return chopped_time


def now_formatted():
    now = datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S %Z")


def now_UTC_formatted():
    now = datetime.now(timezone.utc)
    return now.strftime("%Y-%m-%d %H:%M:%S %Z")


def build_regex_word_boundary(phrases: list[str]):
    regStr = ""
    for idx, phrase in enumerate(phrases):
        if idx == (len(phrases) - 1):
            regStr += r"(\b" + re.escape(phrase) + r"\b)"
        else:
            regStr += r"(\b" + re.escape(phrase) + r"\b)|"
    regSearch1 = re.compile(regStr, re.I)
    return regSearch1


def make_slug(phrase):
    words = phrase.split()
    slug = "-".join(words)
    return slug


def is_valid_url(url: str) -> bool:
    """Check if a given string is a valid URL."""

    url_regex = re.compile(
        r"^(?:http|ftp)s?://"  # http:// or https://
        # domain...
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|"
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )
    return bool(url_regex.match(url))


def seconds_to_hhmmss(seconds: int) -> str:
    """
    Convert a given number of seconds to a string in the format HH:MM:SS.

    :param seconds: The number of seconds to convert.
    :return: A string in the format HH:MM:SS.
    """
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


if __name__ == "__main__":
    # print(now_UTC_sec())
    pass
