[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "tran-hits"
dynamic = ["version"]
description = ''
readme = "README.md"
requires-python = ">=3.8"
license = "MIT"
keywords = []
authors = [
  { name = "Ideas", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
dependencies = []

[project.urls]
Documentation = "https://github.com/Ideas/tran-hits#readme"
Issues = "https://github.com/Ideas/tran-hits/issues"
Source = "https://github.com/Ideas/tran-hits"


[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "typer>=0.9.0"
]
dev = [
    "pytest>=8.0.0",
    "typer>=0.9.0"
]

[tool.hatch.version]
path = "src/tran_hits/__about__.py"

[tool.hatch.build.targets.wheel]
packages = ["src/tran_hits", "src/binary_trans", "src/search_trans_cli"]

[tool.hatch.env]
requires = [
    "hatch-pip-compile"
]

[tool.hatch.envs.types]
extra-dependencies = [
  "mypy>=1.0.0",
]

[tool.hatch.envs.types.scripts]
dev-editable = "pip install -e ."
check = "mypy --install-types --non-interactive {args:src/tran_hits tests}"

[tool.coverage.run]
source_pkgs = ["tran_hits", "tests"]
branch = true
parallel = true
omit = [
  "src/tran_hits/__about__.py",
]

[tool.coverage.paths]
tran_hits = ["src/tran_hits", "*/tran-hits/src/tran_hits"]
tests = ["tests", "*/tran-hits/tests"]

[tool.coverage.report]
exclude_lines = [
  "no cov",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
]

[tool.hatch.envs.test]
features = ["test"]          # This links to [project.optional-dependencies].test
type = "pip-compile"
pip-compile-resolver = "uv"  # Use uv for locking

[tool.hatch.envs.test.scripts]
dev-editable = "pip install -e ."
# test = "pytest tests"                                                     # Defines the 'hatch run test test' command
# test = "cd $(git rev-parse --show-toplevel)/apps/tran-hits && pytest"     # for a more portable approach (if you always want to run from the script's directory)
test = "pytest {args}"
# test = "python -m pytest"                                                 # let use Python to resolve the path

# hatch run test:dev-editable
# hatch run types:dev-editable