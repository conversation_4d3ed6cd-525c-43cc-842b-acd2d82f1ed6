[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "trans-db"
dynamic = ["version"]
description = 'Database layer for transcript management'
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
keywords = ["database", "transcript", "sqlalchemy"]
authors = [{ name = "Ideas", email = "<EMAIL>" }]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: Implementation :: CPython",
]
dependencies = [
  "sqlalchemy>=2.0.0",
  "alembic>=1.12.0",
  "asyncpg>=0.28.0",                                                # For PostgreSQL
  "aiosqlite>=0.19.0",                                              # For SQLite testing
  "greenlet>=2.0.0",                                                # Required for async SQLAlchemy
  "pydantic>=2.0.0",
  "numpy>=1.24.0",                                                  # For efficient array operations
  "tran-hits @ file:///home/<USER>/repo/knowtrails/apps/tran-hits", # Local package for transcript preprocessing
]

# ValueError: 
# Dependency #8 (it was tran-hits) of project.dependencies
# cannot be a direct reference unless 
# field allow-direct-references is set to `true`
[tool.hatch.metadata]
allow-direct-references = true

[project.optional-dependencies]
build = [
  "uv>=0.1.0", # Modern Python packaging tools
]

[project.urls]
Documentation = "https://github.com/Ideas/trans-db#readme"
Issues = "https://github.com/Ideas/trans-db/issues"
Source = "https://github.com/Ideas/trans-db"

[tool.hatch.version]
path = "src/trans_db/__about__.py"

[tool.hatch.build.targets.wheel]
packages = ["src/trans_db"]

[tool.hatch.env]
requires = ["hatch-pip-compile"]

[tool.hatch.envs.types]
extra-dependencies = ["mypy>=1.0.0"]
[tool.hatch.envs.types.scripts]
check = "mypy --install-types --non-interactive {args:src/trans_db tests}"

[tool.hatch.envs.test]
dependencies = ["pytest", "pytest-cov", "pytest-asyncio", "aiosqlite"]
install-mode = "editable"

[tool.hatch.envs.test.scripts]
test = "pytest {args:tests}"
test-cov = "pytest --cov {args:src/trans_db}"
cov-report = ["coverage combine", "coverage report"]
cov = ["test-cov", "cov-report"]

[tool.hatch.envs.pip-compile]
dependencies = ["pip-tools"]

[tool.hatch.envs.pip-compile.scripts]
requirements = [
  "pip-compile --upgrade --generate-hashes --output-file=requirements.txt pyproject.toml",
  "pip-compile --upgrade --generate-hashes --output-file=requirements-dev.txt --extra=build pyproject.toml",
]

[tool.coverage.run]
source_pkgs = ["trans_db", "tests"]
branch = true
parallel = true
omit = ["src/trans_db/__about__.py"]

[tool.coverage.paths]
trans_db = ["src/trans_db", "*/trans-db/src/trans_db"]
tests = ["tests", "*/trans-db/tests"]

[tool.coverage.report]
exclude_lines = ["no cov", "if __name__ == .__main__.:", "if TYPE_CHECKING:"]
