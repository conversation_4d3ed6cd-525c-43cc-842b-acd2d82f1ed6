import json
import os

from sqlalchemy import create_engine
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import sessionmaker
from trans_db.models.base import Base

# from trans-db.trans_db.models.base import Base
# from ..trans_db.models.user import User
# from .discord_user_details import DiscordUserDetails
from .utils import add_or_update_discord_user

# Define the path to the profiles.json file
PROFILES_JSON_PATH = (
    "apps/discord-trans/src/discord_trans/user_card/profiles.json"
)

# Define the database URL (you might want to make this configurable)
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./test.db")


def migrate_profiles_to_db():
    engine = create_engine(DATABASE_URL)
    Base.metadata.create_all(engine)  # Ensure tables are created

    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        with open(PROFILES_JSON_PATH, "r") as f:
            profiles_data = json.load(f)

        for profile in profiles_data:
            username = profile.get("username")
            if not username:
                print(f"Skipping profile due to missing username: {profile}")
                continue

            add_or_update_discord_user(
                session=session,
                username=username,
                skill_level=profile.get("skill_level"),
                temperament=profile.get("temperament"),
                humor=profile.get("humor"),
                trust_level=profile.get("trust_level"),
                preferred_topics=profile.get("preferred_topics"),
                communication_style=profile.get("communication_style"),
                sentiment_tendency=profile.get("sentiment_tendency"),
                profile_image_url=None,  # Not in profiles.json
                sound_clip_url=None,  # Not in profiles.json
            )

        session.commit()
        print("Migration completed successfully!")

    except FileNotFoundError:
        print(f"Error: profiles.json not found at {PROFILES_JSON_PATH}")
        session.rollback()
    except IntegrityError as e:
        print(f"Database integrity error: {e}")
        session.rollback()
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        session.rollback()
    finally:
        session.close()


if __name__ == "__main__":
    migrate_profiles_to_db()
