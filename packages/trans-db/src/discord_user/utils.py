import uuid

from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from trans_db.models.user import User

from .discord_user_details import DiscordUserDetails


def add_or_update_discord_user(
    session: Session,
    username: str,
    skill_level: str = None,
    temperament: str = None,
    humor: str = None,
    trust_level: str = None,
    preferred_topics: list[str] = None,
    communication_style: str = None,
    sentiment_tendency: str = None,
    profile_image_url: str = None,
    sound_clip_url: str = None,
) -> tuple[User, DiscordUserDetails]:
    """
    Adds a new user and their Discord details, or updates existing ones.
    """
    user_email = f"{username.lower()}@example.com"

    user = session.query(User).filter_by(email=user_email).first()

    if not user:
        user = User(name=username, email=user_email)
        session.add(user)
        session.flush()  # Flush to get the user.id for the foreign key
        print(f"Created new user: {user.name} ({user.email})")
    else:
        print(f"User already exists: {user.name} ({user.email})")

    discord_details = (
        session.query(DiscordUserDetails).filter_by(user_id=user.id).first()
    )

    if not discord_details:
        discord_details = DiscordUserDetails(
            user_id=user.id,
            skill_level=skill_level,
            temperament=temperament,
            humor=humor,
            trust_level=trust_level,
            preferred_topics=preferred_topics,
            communication_style=communication_style,
            sentiment_tendency=sentiment_tendency,
            profile_image_url=profile_image_url,
            sound_clip_url=sound_clip_url,
        )
        session.add(discord_details)
        print(f"Created Discord details for user: {user.name}")
    else:
        # Update existing details
        discord_details.skill_level = skill_level
        discord_details.temperament = temperament
        discord_details.humor = humor
        discord_details.trust_level = trust_level
        discord_details.preferred_topics = preferred_topics
        discord_details.communication_style = communication_style
        discord_details.sentiment_tendency = sentiment_tendency
        discord_details.profile_image_url = profile_image_url
        discord_details.sound_clip_url = sound_clip_url
        print(f"Updated Discord details for user: {user.name}")

    return user, discord_details
