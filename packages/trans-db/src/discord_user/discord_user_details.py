import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>
from sqlalchemy.orm import Mapped, mapped_column

from ..trans_db.models.base import Base


class DiscordUserDetails(Base):
    __tablename__ = "discord_user_details"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: str(uuid.uuid4())
    )
    user_id: Mapped[str] = mapped_column(
        String, ForeignKey("users.id"), unique=True
    )
    skill_level: Mapped[str] = mapped_column(String, nullable=True)
    temperament: Mapped[str] = mapped_column(String, nullable=True)
    humor: Mapped[str] = mapped_column(String, nullable=True)
    trust_level: Mapped[str] = mapped_column(String, nullable=True)
    preferred_topics: Mapped[list[str]] = mapped_column(JSONB, nullable=True)
    communication_style: Mapped[str] = mapped_column(String, nullable=True)
    sentiment_tendency: Mapped[str] = mapped_column(String, nullable=True)
    profile_image_url: Mapped[str] = mapped_column(String, nullable=True)
    sound_clip_url: Mapped[str] = mapped_column(String, nullable=True)
