"""Discord-specific database service layer."""

import logging
import uuid
from typing import Any, Dict, List, Optional, Tu<PERSON>

from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ..models import SocialAccount, User
from .db_service import DBService

logger = logging.getLogger(__name__)


class DiscordService(DBService):
    """Service class for Discord-specific database operations."""

    async def get_or_create_user_by_discord_id(
        self, discord_id: str, discord_username: str
    ) -> Tuple[User, SocialAccount]:
        """
        Get or create a user by their Discord ID.
        Returns the User and their Discord SocialAccount.
        """
        # First, try to find existing Discord social account
        result = await self.db.execute(
            select(SocialAccount)
            .filter(SocialAccount.provider == "discord")
            .filter(SocialAccount.provider_user_id == discord_id)
        )
        discord_account = result.scalars().first()

        if discord_account:
            # Load the associated user
            result = await self.db.execute(
                select(User).filter(User.id == discord_account.user_id)
            )
            user = result.scalars().first()
            if user:
                # Update username if it changed
                if discord_account.provider_username != discord_username:
                    discord_account.provider_username = discord_username
                    await self.db.commit()
                return user, discord_account

        # Create new user and Discord account
        user = User(
            id=str(uuid.uuid4()),
            email=f"{discord_username.lower()}@discord.placeholder",  # Placeholder email
            name=discord_username,
        )
        self.db.add(user)
        await self.db.flush()  # Get the user ID

        discord_account = SocialAccount(
            id=str(uuid.uuid4()),
            user_id=user.id,
            provider="discord",
            provider_user_id=discord_id,
            provider_username=discord_username,
            provider_data={},
        )
        self.db.add(discord_account)

        try:
            await self.db.commit()
            await self.db.refresh(user)
            await self.db.refresh(discord_account)
            logger.info(
                f"Created new user and Discord account for {discord_username} ({discord_id})"
            )
        except IntegrityError:
            await self.db.rollback()
            # Handle race condition - try to get existing account
            result = await self.db.execute(
                select(SocialAccount)
                .filter(SocialAccount.provider == "discord")
                .filter(SocialAccount.provider_user_id == discord_id)
            )
            discord_account = result.scalars().first()
            if discord_account:
                result = await self.db.execute(
                    select(User).filter(User.id == discord_account.user_id)
                )
                user = result.scalars().first()
                if user:
                    return user, discord_account
            raise RuntimeError(
                f"Failed to get or create Discord user: {discord_id}"
            )

        return user, discord_account

    async def update_discord_profile(
        self, discord_id: str, profile_updates: Dict[str, Any]
    ) -> Optional[SocialAccount]:
        """Update Discord user profile data."""
        result = await self.db.execute(
            select(SocialAccount)
            .filter(SocialAccount.provider == "discord")
            .filter(SocialAccount.provider_user_id == discord_id)
        )
        discord_account = result.scalars().first()

        if not discord_account:
            logger.warning(f"Discord account not found for ID: {discord_id}")
            return None

        # Update provider_data with new profile information
        current_data = discord_account.provider_data or {}
        current_data.update(profile_updates)
        discord_account.provider_data = current_data

        await self.db.commit()
        await self.db.refresh(discord_account)
        logger.info(f"Updated Discord profile for {discord_id}")
        return discord_account

    async def get_discord_profile(
        self, discord_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get Discord user profile data."""
        result = await self.db.execute(
            select(SocialAccount)
            .filter(SocialAccount.provider == "discord")
            .filter(SocialAccount.provider_user_id == discord_id)
        )
        discord_account = result.scalars().first()

        if not discord_account:
            return None

        return discord_account.provider_data or {}

    async def log_discord_search(
        self, discord_id: str, search_term: str
    ) -> bool:
        """Log a Discord user's search activity."""
        # Get the user first
        result = await self.db.execute(
            select(SocialAccount)
            .filter(SocialAccount.provider == "discord")
            .filter(SocialAccount.provider_user_id == discord_id)
        )
        discord_account = result.scalars().first()

        if not discord_account:
            logger.warning(
                f"Cannot log search - Discord account not found: {discord_id}"
            )
            return False

        # For now, store search history in provider_data
        # Later, you might want a separate SearchHistory table
        current_data = discord_account.provider_data or {}
        searches = current_data.get("search_history", [])
        searches.append(
            {
                "term": search_term,
                "timestamp": str(
                    uuid.uuid4()
                ),  # You might want actual timestamp
            }
        )

        # Keep only last 100 searches
        if len(searches) > 100:
            searches = searches[-100:]

        current_data["search_history"] = searches
        discord_account.provider_data = current_data

        await self.db.commit()
        logger.info(
            f"Logged search for Discord user {discord_id}: {search_term}"
        )
        return True

    async def migrate_from_json_profile_data(
        self, json_profile_data: List[Dict[str, Any]]
    ) -> int:
        """
        Migrate Discord users from JSON profile data to new SQLAlchemy models.
        Takes a list of dictionaries with Discord user profile information.
        Returns number of users migrated.
        """
        migrated_count = 0

        for user_profile in json_profile_data:
            discord_id = user_profile.get("discord_id")
            discord_username = user_profile.get("discord_username")

            if not discord_id or not discord_username:
                logger.warning(f"Skipping invalid user record: {user_profile}")
                continue

            try:
                # Create or get user
                (
                    user,
                    discord_account,
                ) = await self.get_or_create_user_by_discord_id(
                    discord_id, discord_username
                )

                # Migrate profile data
                profile_data = {
                    "bot_profile_name": user_profile.get("bot_profile_name"),
                    "skill_level": user_profile.get("skill_level", "unknown"),
                    "temperament": user_profile.get("temperament", "unknown"),
                    "humor": user_profile.get("humor", "unknown"),
                    "trust_level": user_profile.get("trust_level", "unknown"),
                    "preferred_topics": user_profile.get(
                        "preferred_topics", "[]"
                    ),
                    "communication_style": user_profile.get(
                        "communication_style", "neutral"
                    ),
                    "sentiment_tendency": user_profile.get(
                        "sentiment_tendency", "neutral"
                    ),
                    "knowtrail_user_id": user_profile.get("knowtrail_user_id"),
                }

                # Remove None values
                profile_data = {
                    k: v for k, v in profile_data.items() if v is not None
                }

                await self.update_discord_profile(discord_id, profile_data)
                migrated_count += 1

            except Exception as e:
                logger.error(
                    f"Failed to migrate Discord user {discord_id}: {e}"
                )
                continue

        logger.info(f"Successfully migrated {migrated_count} Discord users")
        return migrated_count
