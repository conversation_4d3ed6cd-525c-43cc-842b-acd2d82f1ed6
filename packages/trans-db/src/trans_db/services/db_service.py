"""Database service layer for transcript management."""

import logging
import uuid
from typing import Any, Dict, List, Optional, cast

from binary_trans.trans_prepper import TranscriptPrepper
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ..models import Tag, Transcript, YtJob, YtJobTag

logger = logging.getLogger(__name__)


class DBService:
    """Service class for database operations."""

    def __init__(self, db: AsyncSession):
        """Initialize the service with a database session."""
        self.db = db

    async def get_yt_job_by_video_id(self, video_id: str) -> Optional[YtJob]:
        """Get a YouTube job by its video ID."""
        result = await self.db.execute(
            select(YtJob).filter(YtJob.video_id == video_id)
        )
        return result.scalars().first()

    async def create_or_update_yt_job(self, job_data: Dict[str, Any]) -> YtJob:
        """Create or update a YtJob."""
        # Remove job_id if present as it's auto-generated
        job_data.pop("job_id", None)

        existing_job = await self.get_yt_job_by_video_id(job_data["video_id"])
        if existing_job:
            for key, value in job_data.items():
                setattr(existing_job, key, value)
            job = existing_job
        else:
            job = YtJob(**job_data)
            self.db.add(job)

        await self.db.commit()
        return job

    async def get_or_create_tag(
        self,
        tag_name: str,
        tag_type: str = "system",
        is_trusted: bool = False,
        user_id: Optional[str] = None,
    ) -> Tag:
        """Get an existing tag by name or create a new one."""
        result = await self.db.execute(
            select(Tag).filter(Tag.name == tag_name)
        )
        tag = result.scalars().first()
        if not tag:
            tag = Tag(
                name=tag_name,
                type=tag_type,
                is_trusted=is_trusted,
                user_id=user_id,
                id=str(uuid.uuid4()),
            )
            self.db.add(tag)
            try:
                await self.db.commit()
                await self.db.refresh(tag)
                logger.info(f"Created new Tag: {tag_name}")
            except IntegrityError:
                await self.db.rollback()
                # Tag might have been created by another concurrent transaction
                result = await self.db.execute(
                    select(Tag).filter(Tag.name == tag_name)
                )
                tag = result.scalars().first()
                if not tag:
                    raise RuntimeError(
                        f"Failed to get or create tag: {tag_name}"
                    )
                logger.warning(
                    f"Tag '{tag_name}' already exists, retrieved existing."
                )
        return tag

    async def link_job_to_tag(
        self,
        job_id: str,
        tag_id: str,
        created_by_user_id: Optional[str] = None,
        confidence: int = 100,
        source: str = "youtube_api",
        extra_metadata: Optional[Dict[str, Any]] = None,
    ) -> YtJobTag:
        """Link a job to a tag with optional metadata."""
        result = await self.db.execute(
            select(YtJobTag).filter(
                YtJobTag.job_id == job_id, YtJobTag.tag_id == tag_id
            )
        )
        yt_job_tag = result.scalars().first()
        if not yt_job_tag:
            yt_job_tag = YtJobTag(
                job_id=job_id,
                tag_id=tag_id,
                created_by=created_by_user_id,
                confidence=confidence,
                source=source,
                extra_metadata=extra_metadata
                if extra_metadata is not None
                else {},
            )
            self.db.add(yt_job_tag)
            try:
                await self.db.commit()
                await self.db.refresh(yt_job_tag)
                logger.info(f"Linked job {job_id} to tag {tag_id}")
            except IntegrityError:
                await self.db.rollback()
                # Link might have been created by another concurrent transaction
                result = await self.db.execute(
                    select(YtJobTag).filter(
                        YtJobTag.job_id == job_id, YtJobTag.tag_id == tag_id
                    )
                )
                yt_job_tag = result.scalars().first()
                if not yt_job_tag:
                    raise RuntimeError(
                        f"Failed to link job {job_id} to tag {tag_id}"
                    )
                logger.warning(
                    f"Link between job {job_id} and tag {tag_id} already exists, "
                    "retrieved existing."
                )
        return yt_job_tag

    async def get_tags_for_yt_job(self, job_id: str) -> List[str]:
        """Get all tag names associated with a job."""
        result = await self.db.execute(
            select(Tag.name).join(YtJobTag).filter(YtJobTag.job_id == job_id)
        )
        tags = result.scalars().all()
        return list(tags)

    async def create_transcript(
        self,
        video_id: str,
        transcript_items: List[Dict[str, Any]],
        tags: Optional[List[str]] = None,
    ) -> Transcript:
        """Create a new transcript using array-based storage.

        Args:
            video_id: The ID of the video this transcript belongs to
            transcript_items: List of transcript items with start, duration, and text fields
            tags: Optional list of tags for metadata

        Returns:
            The created Transcript object
        """
        # Validate transcript items
        required_fields = ["text", "start", "duration"]
        for item in transcript_items:
            missing = [f for f in required_fields if f not in item]
            if missing:
                raise ValueError(
                    f"Transcript item missing required fields: {missing}"
                )

        # Extract arrays from transcript items
        text_segments = [item["text"] for item in transcript_items]
        start_times = [item["start"] for item in transcript_items]
        durations = [item["duration"] for item in transcript_items]

        # Use TranscriptPrepper to calculate character map and slurp consistently
        prepper = TranscriptPrepper(transcript_items)
        character_map = prepper.cc_map
        slurp = prepper.slurp.rstrip()  # Remove trailing space

        # Create transcript object
        transcript = Transcript(
            transcript_id=str(uuid.uuid4()),
            video_id=video_id,
            text_segments=text_segments,
            start_times=start_times,
            durations=durations,
            character_map=character_map,
            slurp=slurp,
            tag_metadata={"tags": tags} if tags else {},
        )

        self.db.add(transcript)
        await self.db.commit()
        await self.db.refresh(transcript)
        logger.info(
            f"Created new array-based Transcript for video_id: {video_id}"
        )
        return transcript

    async def get_transcript_by_video_id(
        self, video_id: str
    ) -> Optional[Transcript]:
        """Get a transcript by its video ID."""
        result = await self.db.execute(
            select(Transcript).filter(Transcript.video_id == video_id)
        )
        return result.scalars().first()

    def transcript_to_dict(self, transcript: Transcript) -> Dict[str, Any]:
        """Convert a transcript from array-based storage to dictionary format."""
        transcript_items = [
            {"text": text, "start": start, "duration": duration}
            for text, start, duration in zip(
                transcript.text_segments,
                transcript.start_times,
                transcript.durations,
            )
        ]

        return {
            "transcript_id": transcript.transcript_id,
            "video_id": transcript.video_id,
            "transcript": transcript_items,
            "character_map": transcript.character_map,
            "slurp": transcript.slurp,
            "tag_metadata": transcript.tag_metadata,
        }
