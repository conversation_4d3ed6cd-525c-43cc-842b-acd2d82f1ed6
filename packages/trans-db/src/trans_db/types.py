from __future__ import annotations

import json
from typing import Any, List, Optional, Type, TypeVar, Union

from sqlalchemy import String, Text, TypeDecorator
from sqlalchemy.dialects.postgresql import ARRAY as PG_ARRAY
from sqlalchemy.engine.interfaces import Dialect

T = TypeVar("T", str, float, int)


class SqliteArray(TypeDecorator[List[T]]):
    """Array type for SQLite that stores arrays as JSON strings."""

    impl = Text
    cache_ok = True

    def __init__(self, item_type: Type[T]) -> None:
        super().__init__()
        self.item_type = item_type

    def process_bind_param(
        self, value: Optional[List[T]], dialect: Dialect
    ) -> Optional[str]:
        if value is None:
            return None
        # Convert SQLAlchemy String type to str
        values = []
        for x in value:
            # Handle SQLAlchemy String type
            if hasattr(x, "length"):
                x = str(x)
            values.append(x)
        return json.dumps(
            [str(x) if isinstance(x, String) else x for x in values]
        )

    def process_result_value(
        self, value: Optional[str], dialect: Dialect
    ) -> Optional[List[T]]:
        if value is None:
            return None
        data = json.loads(value)
        # Just convert directly since we're converting from JSON
        return (
            data if isinstance(data[0], self.item_type) else [x for x in data]
        )


def array_type(
    item_type: Type[T],
) -> Union[PG_ARRAY, SqliteArray[T]]:
    """Creates an array type that works with both PostgreSQL and SQLite.

    Args:
        item_type: The type of items in the array (str, float, int)

    Returns:
        A type that will be ARRAY in PostgreSQL and JSON-serialized in SQLite
    """
    return PG_ARRAY(item_type).with_variant(SqliteArray(item_type), "sqlite")
