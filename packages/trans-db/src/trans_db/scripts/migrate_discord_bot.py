#!/usr/bin/env python3
"""
Migration script to move Discord bot data from SQLite to PostgreSQL using SQLAlchemy models.
"""

import asyncio
import json
import logging
import os
import sqlite3
import sys
from pathlib import Path
from typing import Any, Dict, List

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import trans_db
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from trans_db.models.base import Base
from trans_db.services.discord_service import DiscordService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DiscordBotMigrator:
    """Handles migration of Discord bot data from SQLite to PostgreSQL."""
    
    def __init__(self, sqlite_db_path: str, postgres_url: str):
        self.sqlite_db_path = sqlite_db_path
        self.postgres_url = postgres_url
        self.engine = create_async_engine(postgres_url)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
    
    def read_sqlite_discord_users(self) -> List[Dict[str, Any]]:
        """Read Discord users from SQLite database."""
        if not os.path.exists(self.sqlite_db_path):
            logger.error(f"SQLite database not found: {self.sqlite_db_path}")
            return []
        
        conn = sqlite3.connect(self.sqlite_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM discord_users")
            rows = cursor.fetchall()
            users = [dict(row) for row in rows]
            logger.info(f"Found {len(users)} Discord users in SQLite")
            return users
        except sqlite3.Error as e:
            logger.error(f"Error reading SQLite database: {e}")
            return []
        finally:
            conn.close()
    
    def read_sqlite_transcripts(self) -> List[Dict[str, Any]]:
        """Read cached transcripts from SQLite database."""
        if not os.path.exists(self.sqlite_db_path):
            return []
        
        conn = sqlite3.connect(self.sqlite_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM transcripts")
            rows = cursor.fetchall()
            transcripts = []
            for row in rows:
                transcript_data = {
                    "video_id": row["video_id"],
                    "transcript_json": json.loads(row["transcript_json"])
                }
                transcripts.append(transcript_data)
            logger.info(f"Found {len(transcripts)} cached transcripts in SQLite")
            return transcripts
        except sqlite3.Error as e:
            logger.error(f"Error reading transcripts from SQLite: {e}")
            return []
        finally:
            conn.close()
    
    def read_sqlite_searches(self) -> List[Dict[str, Any]]:
        """Read search history from SQLite database."""
        if not os.path.exists(self.sqlite_db_path):
            return []
        
        conn = sqlite3.connect(self.sqlite_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT * FROM searches ORDER BY timestamp DESC")
            rows = cursor.fetchall()
            searches = [dict(row) for row in rows]
            logger.info(f"Found {len(searches)} search records in SQLite")
            return searches
        except sqlite3.Error as e:
            logger.error(f"Error reading searches from SQLite: {e}")
            return []
        finally:
            conn.close()
    
    async def migrate_discord_users(self) -> int:
        """Migrate Discord users to new SQLAlchemy models."""
        sqlite_users = self.read_sqlite_discord_users()
        if not sqlite_users:
            logger.info("No Discord users to migrate")
            return 0
        
        async with self.async_session() as session:
            discord_service = DiscordService(session)
            migrated_count = await discord_service.migrate_from_sqlite_discord_users(sqlite_users)
            return migrated_count
    
    async def migrate_transcripts(self) -> int:
        """Migrate cached transcripts to new SQLAlchemy models."""
        sqlite_transcripts = self.read_sqlite_transcripts()
        if not sqlite_transcripts:
            logger.info("No transcripts to migrate")
            return 0
        
        async with self.async_session() as session:
            db_service = DiscordService(session)  # Use base DBService for transcripts
            migrated_count = 0
            
            for transcript_data in sqlite_transcripts:
                try:
                    video_id = transcript_data["video_id"]
                    transcript_json = transcript_data["transcript_json"]
                    
                    # Check if transcript already exists
                    existing = await db_service.get_transcript_by_video_id(video_id)
                    if existing:
                        logger.info(f"Transcript already exists for video_id: {video_id}")
                        continue
                    
                    # Create YtJob if it doesn't exist
                    yt_job = await db_service.get_yt_job_by_video_id(video_id)
                    if not yt_job:
                        yt_job = await db_service.create_yt_job(
                            video_id=video_id,
                            title=f"Migrated from Discord bot: {video_id}",
                            user_id=None  # No user association for bot-cached transcripts
                        )
                    
                    # Create transcript
                    if "transcript" in transcript_json:
                        await db_service.create_transcript_from_items(
                            video_id=video_id,
                            transcript_items=transcript_json["transcript"]
                        )
                        migrated_count += 1
                        logger.info(f"Migrated transcript for video_id: {video_id}")
                    
                except Exception as e:
                    logger.error(f"Failed to migrate transcript {transcript_data.get('video_id', 'unknown')}: {e}")
                    continue
            
            logger.info(f"Successfully migrated {migrated_count} transcripts")
            return migrated_count
    
    async def create_tables(self):
        """Create database tables if they don't exist."""
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created/verified")
    
    async def run_migration(self):
        """Run the complete migration process."""
        logger.info("Starting Discord bot migration...")
        
        # Create tables
        await self.create_tables()
        
        # Migrate users
        user_count = await self.migrate_discord_users()
        logger.info(f"Migrated {user_count} Discord users")
        
        # Migrate transcripts
        transcript_count = await self.migrate_transcripts()
        logger.info(f"Migrated {transcript_count} transcripts")
        
        logger.info("Discord bot migration completed!")
        return user_count, transcript_count


async def main():
    """Main migration function."""
    # Default paths - adjust as needed
    sqlite_db_path = os.path.join(
        os.path.dirname(__file__), 
        "../../../apps/discord-trans/src/discord_bot_cache.db"
    )
    
    # Get PostgreSQL URL from environment
    postgres_url = os.getenv(
        "DATABASE_URL", 
        "postgresql+asyncpg://user:password@localhost/knowtrails"
    )
    
    if not postgres_url.startswith("postgresql+asyncpg://"):
        logger.error("DATABASE_URL must use asyncpg driver (postgresql+asyncpg://)")
        return
    
    migrator = DiscordBotMigrator(sqlite_db_path, postgres_url)
    await migrator.run_migration()


if __name__ == "__main__":
    asyncio.run(main())
