from typing import Async<PERSON><PERSON>ator, <PERSON><PERSON>

from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)

from .models import Base


async def create_test_db() -> <PERSON><PERSON>[
    AsyncEngine, async_sessionmaker[AsyncSession]
]:
    """Create a test database engine and session factory.

    Uses SQLite in-memory database for testing, which converts array types
    to JSON strings automatically via SqliteArray type decorator.
    """
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False,
    )

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    TestingSessionLocal = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    return engine, TestingSessionLocal
