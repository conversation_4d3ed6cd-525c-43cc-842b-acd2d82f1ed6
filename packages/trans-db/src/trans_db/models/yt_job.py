from datetime import datetime
from typing import TYPE_CHECKING, Optional
from uuid import uuid4

from sqlalchemy import DateTime, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base
from .transcript import Transcript

if TYPE_CHECKING:
    from .yt_job_tag import YtJobTag


class YtJob(Base):
    __tablename__ = "yt_jobs"

    job_id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid4())
    )
    video_id: Mapped[str] = mapped_column(String(20), unique=True)
    job_date: Mapped[int] = mapped_column(Integer)
    corpus: Mapped[str] = mapped_column(String(100))
    trail_id: Mapped[str] = mapped_column(String(100))
    pl_etag: Mapped[str] = mapped_column(String(100))
    kind: Mapped[str] = mapped_column(String(50))
    etag: Mapped[str] = mapped_column(String(100))
    channel_id: Mapped[str] = mapped_column(String(100))
    video_url: Mapped[str] = mapped_column(String(500))
    video_date: Mapped[str] = mapped_column(String(20))
    thumb: Mapped[str] = mapped_column(String(500))
    title: Mapped[str] = mapped_column(String(500))
    description: Mapped[str] = mapped_column(String(5000))
    file_status_flag: Mapped[int] = mapped_column(Integer)

    tags: Mapped[list["YtJobTag"]] = relationship(
        "YtJobTag", back_populates="job"
    )
    transcript: Mapped["Transcript"] = relationship(
        "Transcript", back_populates="job", uselist=False
    )
