import uuid
from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, Optional

from sqlalchemy import DateTime, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from .base import Base

if TYPE_CHECKING:
    from .user import User


class SocialAccount(Base):
    """
    Represents a social media account linked to a user.
    Supports Discord, GitHub, Google, Twitter, etc.
    """
    __tablename__ = "social_accounts"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    
    # Link to main user
    user_id: Mapped[str] = mapped_column(
        String(36), ForeignKey("users.id"), nullable=False
    )
    
    # Social provider info
    provider: Mapped[str] = mapped_column(
        String(50), nullable=False  # 'discord', 'clerk', 'github', 'google', etc.
    )
    provider_user_id: Mapped[str] = mapped_column(
        String(255), nullable=False  # The ID from the social provider
    )
    provider_username: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True  # Username on that platform
    )
    
    # OAuth/Auth data
    access_token: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    refresh_token: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    token_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Provider-specific metadata
    provider_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, nullable=True, default={}
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now(), onupdate=func.now()
    )
    
    # Ensure unique provider + provider_user_id combination
    __table_args__ = (
        UniqueConstraint('provider', 'provider_user_id', name='unique_provider_account'),
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="social_accounts")

    def __repr__(self) -> str:
        return f"<SocialAccount(provider='{self.provider}', username='{self.provider_username}')>"
