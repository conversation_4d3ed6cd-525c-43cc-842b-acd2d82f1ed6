import uuid

from sqlalchemy.orm import Session
from trans_db.models.fallacy.card_models import Claim<PERSON>ard, FallacyCard


def seed_fallacies(session: Session):
    fallacies = [
        (
            "Strawman",
            "Misrepresenting someone's argument to make it easier to attack.",
        ),
        (
            "Ad Hominem",
            "Attacking your opponent's character or personal traits.",
        ),
        (
            "Appeal to Ignorance",
            "Claiming something is true because it hasn’t been proven false.",
        ),
        ("False Dilemma", "Presenting two options as the only possibilities."),
        (
            "Slippery Slope",
            "Arguing that one small step will lead to a chain of negative events.",
        ),
        ("Circular Reasoning", "Using the conclusion as a premise."),
        ("Red Herring", "Distracting from the real issue."),
        ("Bandwagon", "Claiming something is true because it's popular."),
        (
            "Appeal to Authority",
            "Using authority as proof rather than evidence.",
        ),
        ("Post Hoc", "Assuming causation from sequence alone."),
    ]

    for name, desc in fallacies:
        card = FallacyCard(
            id=str(uuid.uuid4()),
            name=name,
            category="Logical",
            description=desc,
        )
        session.add(card)

    session.commit()

    # Optional: link sample claims
    strawman = session.query(FallacyCard).filter_by(name="Strawman").first()
    if strawman:
        claim = ClaimCard(
            title="Climate scientists want us to live in caves",
            description="Misrepresents the argument about reducing emissions.",
            meta_level_0="Climate",
            meta_level_1="Misrepresentation",
        )
        claim.fallacies.append(strawman)
        session.add(claim)

    session.commit()
