# /models/fallacy/card_models.py

import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy import <PERSON>SO<PERSON>, DateTime, ForeignKey, Integer, String, Table, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..base import Base
from ..user import User
from ..yt_job import YtJob  # Link to media context, if needed

# === Association Tables ===

claim_fallacy_link = Table(
    "claim_fallacy_link",
    Base.metadata,
    mapped_column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    mapped_column(
        "fallacy_id", ForeignKey("fallacy_cards.id"), primary_key=True
    ),
)

user_claim_reference = Table(
    "user_claim_reference",
    Base.metadata,
    mapped_column("user_id", ForeignKey("users.id"), primary_key=True),
    mapped_column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    mapped_column(
        "context_id", ForeignKey("yt_jobs.job_id"), primary_key=True
    ),
)

# === Core Models ===


class FallacyCard(Base):
    __tablename__ = "fallacy_cards"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    name: Mapped[str] = mapped_column(String(100), unique=True)
    category: Mapped[str] = mapped_column(String(50))
    description: Mapped[str] = mapped_column(Text)

    examples: Mapped[List["FallacyExample"]] = relationship(
        back_populates="fallacy", cascade="all, delete-orphan"
    )
    claims: Mapped[List["ClaimCard"]] = relationship(
        secondary=claim_fallacy_link, back_populates="fallacies"
    )


class FallacyExample(Base):
    __tablename__ = "fallacy_examples"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    fallacy_id: Mapped[str] = mapped_column(
        ForeignKey("fallacy_cards.id"), nullable=False
    )
    example_text: Mapped[str] = mapped_column(Text)
    context_summary: Mapped[Optional[str]] = mapped_column(Text)
    job_id: Mapped[Optional[str]] = mapped_column(
        ForeignKey("yt_jobs.job_id")
    )  # Optional media link

    fallacy: Mapped["FallacyCard"] = relationship(back_populates="examples")
    job: Mapped[Optional[YtJob]] = relationship()  # if media context


class ClaimCard(Base):
    __tablename__ = "claim_cards"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    title: Mapped[str] = mapped_column(String(255), unique=True)
    description: Mapped[str] = mapped_column(Text)

    # Meta-tags: could be hierarchical or just tagged flat
    meta_level_0: Mapped[Optional[str]] = mapped_column(String(100))
    meta_level_1: Mapped[Optional[str]] = mapped_column(String(100))
    meta_level_2: Mapped[Optional[str]] = mapped_column(String(100))
    meta_level_3: Mapped[Optional[str]] = mapped_column(String(100))

    fallacies: Mapped[List[FallacyCard]] = relationship(
        secondary=claim_fallacy_link, back_populates="claims"
    )
    users: Mapped[List["User"]] = relationship(
        secondary=user_claim_reference, back_populates="claims"
    )


# Augment user model with backref
User.claims = relationship(
    "ClaimCard", secondary=user_claim_reference, back_populates="users"
)


# 🎯 Integration Notes
# Fully reuses User, YtJob, and Base.

# Avoids hard foreign key lock-in to conversations, keeping YtJob as the default media context.

# Leaves room for ClaimEvidenceCard if needed later.

# 🧪 Next Steps?
# Would you like:

# ✅ Alembic migration files for this?

# ✅ Seed fallacies and claims (e.g., 10 common fallacies)?

# ✅ Query examples for pulling top claims per fallacy or top fallacies per corpus?

# Let me know which route to go.
