import uuid
from typing import TYPE_CHECKING, Any, Dict, List

from sqlalchemy import <PERSON><PERSON><PERSON>, Float, <PERSON><PERSON><PERSON>, Integer, String, Text
from sqlalchemy.dialects.postgresql import ARRAY as PG_ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from ..types import array_type
from .base import Base

if TYPE_CHECKING:
    from .yt_job import YtJob


class Transcript(Base):
    __tablename__ = "transcripts"

    transcript_id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    video_id: Mapped[str] = mapped_column(
        ForeignKey("yt_jobs.video_id"), unique=True
    )

    # Use array_type helper for cross-database compatibility
    text_segments: Mapped[List[str]] = mapped_column(
        array_type(String), nullable=False
    )
    start_times: Mapped[List[float]] = mapped_column(
        array_type(Float), nullable=False
    )
    durations: Mapped[List[float]] = mapped_column(
        array_type(Float), nullable=False
    )
    character_map: Mapped[List[int]] = mapped_column(
        array_type(Integer), nullable=False
    )

    # Original slurped text for full-text search
    slurp: Mapped[str] = mapped_column(Text, nullable=False)

    # Keep some JSON for metadata like language, version, etc.
    tag_metadata: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=True, default={}
    )

    # Relationships
    job: Mapped["YtJob"] = relationship(
        "YtJob",
        back_populates="transcript",
    )
