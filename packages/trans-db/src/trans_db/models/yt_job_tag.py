from typing import TYPE_CHECKING, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base
from .tag import Tag

if TYPE_CHECKING:
    from .yt_job import YtJob


class YtJobTag(Base):
    __tablename__ = "yt_job_tags"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    job_id: Mapped[str] = mapped_column(
        ForeignKey("yt_jobs.job_id"), nullable=False
    )
    tag_id: Mapped[str] = mapped_column(ForeignKey("tags.id"), nullable=False)
    extra_metadata: Mapped[dict] = mapped_column(JSON, default=dict)
    confidence: Mapped[int] = mapped_column(Integer, default=100)
    source: Mapped[str] = mapped_column(String(50), default="youtube_api")
    created_by: Mapped[Optional[str]] = mapped_column(
        ForeignKey("users.id"), nullable=True
    )

    job: Mapped["YtJob"] = relationship("YtJob", back_populates="tags")
    tag: Mapped["Tag"] = relationship("Tag", back_populates="yt_jobs")
