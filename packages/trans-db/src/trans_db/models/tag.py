from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base

if TYPE_CHECKING:
    from .yt_job_tag import YtJobTag


class Tag(Base):
    __tablename__ = "tags"

    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True)
    type: Mapped[str] = mapped_column(String(50))  # Add type field
    is_trusted: Mapped[bool] = mapped_column(Boolean, default=False)
    user_id: Mapped[Optional[str]] = mapped_column(String(36), nullable=True)

    # Relationships
    yt_jobs: Mapped[List["YtJobTag"]] = relationship(back_populates="tag")
