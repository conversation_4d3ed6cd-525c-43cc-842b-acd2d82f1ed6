import json
import os
import unittest
from unittest.mock import mock_open, patch

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from trans_db.discord_user.discord_user_details import DiscordUserDetails
from trans_db.discord_user.migrate_profiles import (
    PROFILES_JSON_PATH,
    migrate_profiles_to_db,
)
from trans_db.models.base import Base
from trans_db.models.user import User


class TestMigrateProfiles(unittest.TestCase):
    def setUp(self):
        # Use an in-memory SQLite database for testing
        self.engine = create_engine(
            "sqlite:///:memory:",
            connect_args={"check_same_thread": False},
            poolclass=StaticPool,
        )
        Base.metadata.create_all(self.engine)
        self.Session = sessionmaker(bind=self.engine)

        # Mock profiles.json content
        self.mock_profiles_data = [
            {
                "username": "testuser1",
                "skill_level": "expert",
                "temperament": "strategic",
                "humor": "dry",
                "trust_level": "high",
                "preferred_topics": ["coding", "AI"],
                "communication_style": "direct",
                "sentiment_tendency": "positive",
            },
            {
                "username": "testuser2",
                "skill_level": "intermediate",
                "temperament": "fiery",
                "humor": "playful",
                "trust_level": "medium",
                "preferred_topics": [],
                "communication_style": "neutral",
                "sentiment_tendency": "neutral",
            },
        ]

    def tearDown(self):
        Base.metadata.drop_all(self.engine)

    @patch("builtins.open", new_callable=mock_open)
    @patch("json.load")
    @patch.dict(os.environ, {"DATABASE_URL": "sqlite:///:memory:"})
    def test_migration(self, mock_json_load, mock_file_open):
        mock_json_load.return_value = self.mock_profiles_data

        # Ensure the path is correct for the mock
        mock_file_open.side_effect = (
            lambda f, mode: mock_open(
                read_data=json.dumps(self.mock_profiles_data)
            ).return_value
            if f == PROFILES_JSON_PATH
            else unittest.mock.DEFAULT
        )

        migrate_profiles_to_db()

        session = self.Session()

        # Verify User records
        users = session.query(User).all()
        self.assertEqual(len(users), 2)

        user1 = session.query(User).filter_by(name="testuser1").first()
        self.assertIsNotNone(user1)
        self.assertEqual(user1.email, "<EMAIL>")

        user2 = session.query(User).filter_by(name="testuser2").first()
        self.assertIsNotNone(user2)
        self.assertEqual(user2.email, "<EMAIL>")

        # Verify DiscordUserDetails records
        discord_details1 = (
            session.query(DiscordUserDetails)
            .filter_by(user_id=user1.id)
            .first()
        )
        self.assertIsNotNone(discord_details1)
        self.assertEqual(discord_details1.skill_level, "expert")
        self.assertEqual(discord_details1.preferred_topics, ["coding", "AI"])
        self.assertIsNone(discord_details1.profile_image_url)
        self.assertIsNone(discord_details1.sound_clip_url)

        discord_details2 = (
            session.query(DiscordUserDetails)
            .filter_by(user_id=user2.id)
            .first()
        )
        self.assertIsNotNone(discord_details2)
        self.assertEqual(discord_details2.skill_level, "intermediate")
        self.assertEqual(discord_details2.preferred_topics, [])
        self.assertIsNone(discord_details2.profile_image_url)
        self.assertIsNone(discord_details2.sound_clip_url)

        session.close()

    @patch("builtins.open", new_callable=mock_open)
    @patch("json.load")
    @patch.dict(os.environ, {"DATABASE_URL": "sqlite:///:memory:"})
    def test_migration_updates_existing_user_details(
        self, mock_json_load, mock_file_open
    ):
        # Initial data
        initial_data = [
            {
                "username": "existinguser",
                "skill_level": "beginner",
                "temperament": "calm",
                "humor": "none",
                "trust_level": "low",
                "preferred_topics": [],
                "communication_style": "passive",
                "sentiment_tendency": "negative",
            }
        ]
        mock_json_load.return_value = initial_data
        mock_file_open.side_effect = (
            lambda f, mode: mock_open(
                read_data=json.dumps(initial_data)
            ).return_value
            if f == PROFILES_JSON_PATH
            else unittest.mock.DEFAULT
        )

        migrate_profiles_to_db()

        # Update data
        updated_data = [
            {
                "username": "existinguser",
                "skill_level": "advanced",
                "temperament": "energetic",
                "humor": "witty",
                "trust_level": "high",
                "preferred_topics": ["music"],
                "communication_style": "assertive",
                "sentiment_tendency": "positive",
            }
        ]
        mock_json_load.return_value = updated_data
        mock_file_open.side_effect = (
            lambda f, mode: mock_open(
                read_data=json.dumps(updated_data)
            ).return_value
            if f == PROFILES_JSON_PATH
            else unittest.mock.DEFAULT
        )

        migrate_profiles_to_db()  # Run migration again with updated data

        session = self.Session()
        user = session.query(User).filter_by(name="existinguser").first()
        self.assertIsNotNone(user)

        discord_details = (
            session.query(DiscordUserDetails)
            .filter_by(user_id=user.id)
            .first()
        )
        self.assertIsNotNone(discord_details)
        self.assertEqual(discord_details.skill_level, "advanced")
        self.assertEqual(discord_details.preferred_topics, ["music"])
        self.assertEqual(discord_details.humor, "witty")
        self.assertEqual(discord_details.trust_level, "high")
        self.assertEqual(discord_details.communication_style, "assertive")
        self.assertEqual(discord_details.sentiment_tendency, "positive")
        self.assertIsNone(discord_details.profile_image_url)
        self.assertIsNone(discord_details.sound_clip_url)

        session.close()


if __name__ == "__main__":
    unittest.main()
