import pytest
import pytest_asyncio
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from trans_db.models import Base
from trans_db.services import DBService


@pytest_asyncio.fixture(scope="function")
async def test_db():
    """Create a test database."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        execution_options={"isolation_level": "AUTOCOMMIT"},
    )
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    TestingSessionLocal = async_sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    async with TestingSessionLocal() as db:
        try:
            yield db
        finally:
            await db.close()
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture(scope="function")
async def db_service(test_db: AsyncSession) -> DBService:
    """Create a DB service instance."""
    return DBService(test_db)


@pytest.mark.asyncio
async def test_create_yt_job(db_service: DBService):
    job_data = {
        "video_id": "test_video_id_1",
        "job_date": 20231026,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video1",
        "video_date": "2023-10-26",
        "thumb": "http://example.com/thumb1.jpg",
        "title": "Test Video Title 1",
        "description": "Test Video Description 1",
        "file_status_flag": 1,
    }
    yt_job = await db_service.create_or_update_yt_job(job_data)
    assert yt_job.video_id == "test_video_id_1"
    assert yt_job.job_id is not None
    assert (
        await db_service.get_yt_job_by_video_id("test_video_id_1") is not None
    )


@pytest.mark.asyncio
async def test_get_or_create_tag(db_service: DBService):
    tag = await db_service.get_or_create_tag("test_tag_1")
    assert tag.name == "test_tag_1"
    assert tag.id is not None

    # Test getting existing tag
    tag2 = await db_service.get_or_create_tag("test_tag_1")
    assert tag.id == tag2.id


@pytest.mark.asyncio
async def test_link_job_to_tag(db_service: DBService):
    job_data = {
        "video_id": "test_video_id_2",
        "job_date": 20231027,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video2",
        "video_date": "2023-10-27",
        "thumb": "http://example.com/thumb2.jpg",
        "title": "Test Video Title 2",
        "description": "Test Video Description 2",
        "file_status_flag": 1,
    }
    yt_job = await db_service.create_or_update_yt_job(job_data)
    tag = await db_service.get_or_create_tag("test_tag_for_link")

    yt_job_tag = await db_service.link_job_to_tag(
        yt_job.job_id, tag.id, extra_metadata={"key": "value"}
    )
    assert yt_job_tag.job_id == yt_job.job_id
    assert yt_job_tag.tag_id == tag.id
    assert yt_job_tag.extra_metadata == {"key": "value"}

    # Test linking again (should return existing)
    yt_job_tag2 = await db_service.link_job_to_tag(
        yt_job.job_id, tag.id, extra_metadata={"key": "value"}
    )
    assert yt_job_tag.job_id == yt_job_tag2.job_id


@pytest.mark.asyncio
async def test_get_tags_for_yt_job(db_service: DBService):
    job_data = {
        "video_id": "test_video_id_3",
        "job_date": 20231028,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video3",
        "video_date": "2023-10-28",
        "thumb": "http://example.com/thumb3.jpg",
        "title": "Test Video Title 3",
        "description": "Test Video Description 3",
        "file_status_flag": 1,
    }
    yt_job = await db_service.create_or_update_yt_job(job_data)
    tag1 = await db_service.get_or_create_tag("tag_A")
    tag2 = await db_service.get_or_create_tag("tag_B")

    await db_service.link_job_to_tag(yt_job.job_id, tag1.id)
    await db_service.link_job_to_tag(yt_job.job_id, tag2.id)

    tags = await db_service.get_tags_for_yt_job(yt_job.job_id)
    assert "tag_A" in tags
    assert "tag_B" in tags
    assert len(tags) == 2


@pytest.mark.asyncio
async def test_create_transcript(db_service: DBService):
    job_data = {
        "video_id": "test_video_id_4",
        "job_date": 20231029,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video4",
        "video_date": "2023-10-29",
        "thumb": "http://example.com/thumb4.jpg",
        "title": "Test Video Title 4",
        "description": "Test Video Description 4",
        "file_status_flag": 1,
    }
    yt_job = await db_service.create_or_update_yt_job(job_data)

    transcript_items = [
        {"text": "This is a test transcript.", "start": 0.0, "duration": 1.0}
    ]
    transcript = await db_service.create_transcript(
        video_id=yt_job.video_id, transcript_items=transcript_items
    )
    assert transcript.transcript_id is not None
    assert transcript.video_id == yt_job.video_id
    assert transcript.text_segments[0] == "This is a test transcript."

    retrieved_transcript = await db_service.get_transcript_by_video_id(
        yt_job.video_id
    )
    assert retrieved_transcript.transcript_id == transcript.transcript_id


@pytest.mark.asyncio
async def test_create_transcript_with_arrays(db_service: DBService):
    # First create a YT job that the transcript will reference
    job_data = {
        "video_id": "test_video_id_1",
        "job_date": 20231026,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video1",
        "video_date": "2023-10-26",
        "thumb": "http://example.com/thumb1.jpg",
        "title": "Test Video Title 1",
        "description": "Test Video Description 1",
        "file_status_flag": 1,
    }
    await db_service.create_or_update_yt_job(job_data)

    # Sample transcript data
    transcript_items = [
        {"text": "Hello world", "start": 0.0, "duration": 1.5},
        {"text": "This is a test", "start": 1.5, "duration": 2.0},
        {"text": "Of array storage", "start": 3.5, "duration": 1.8},
    ]
    tags = ["test", "example"]

    # Create transcript
    transcript = await db_service.create_transcript(
        video_id="test_video_id_1",
        transcript_items=transcript_items,
        tags=tags,
    )

    # Verify the transcript was created with correct array data
    assert transcript.video_id == "test_video_id_1"
    assert transcript.text_segments == [
        "Hello world",
        "This is a test",
        "Of array storage",
    ]
    assert transcript.start_times == [0.0, 1.5, 3.5]
    assert transcript.durations == [1.5, 2.0, 1.8]
    assert transcript.character_map == [
        0,
        11,
        25,
        39,
    ]  # Verify character mapping
    assert transcript.slurp == "Hello world This is a test Of array storage"
    assert transcript.tag_metadata == {"tags": ["test", "example"]}


@pytest.mark.asyncio
async def test_get_transcript_and_convert_to_dict(db_service: DBService):
    # Create a YT job first
    job_data = {
        "video_id": "test_video_id_2",
        "job_date": 20231026,
        "corpus": "test_corpus",
        "trail_id": "test_trail_id",
        "pl_etag": "test_pl_etag",
        "kind": "youtube#video",
        "etag": "test_etag",
        "channel_id": "test_channel_id",
        "video_url": "http://example.com/video2",
        "video_date": "2023-10-26",
        "thumb": "http://example.com/thumb2.jpg",
        "title": "Test Video Title 2",
        "description": "Test Video Description 2",
        "file_status_flag": 1,
    }
    await db_service.create_or_update_yt_job(job_data)

    # Create a transcript
    transcript_items = [
        {"text": "First segment", "start": 0.0, "duration": 1.0},
        {"text": "Second segment", "start": 1.0, "duration": 1.5},
    ]

    transcript = await db_service.create_transcript(
        video_id="test_video_id_2",
        transcript_items=transcript_items,
    )

    # Retrieve the transcript
    retrieved = await db_service.get_transcript_by_video_id("test_video_id_2")
    assert retrieved is not None

    # Convert to dict and verify structure
    transcript_dict = db_service.transcript_to_dict(retrieved)
    assert transcript_dict["video_id"] == "test_video_id_2"
    assert len(transcript_dict["transcript"]) == 2
    assert transcript_dict["transcript"][0]["text"] == "First segment"
    assert transcript_dict["transcript"][1]["text"] == "Second segment"
    assert transcript_dict["character_map"] == [0, 13, 27]
    assert transcript_dict["slurp"] == "First segment Second segment"


@pytest.mark.asyncio
async def test_transcript_array_error_handling(db_service: DBService):
    # Test creating transcript with mismatched arrays
    with pytest.raises(ValueError):
        await db_service.create_transcript(
            video_id="test_video_id_3",
            transcript_items=[
                {"text": "Test", "start": 0.0}
            ],  # Missing duration
        )

    # Test creating transcript with non-existent video_id
    with pytest.raises(IntegrityError):
        await db_service.create_transcript(
            video_id="non_existent_video",
            transcript_items=[
                {"text": "Test", "start": 0.0, "duration": 1.0},
            ],
        )
