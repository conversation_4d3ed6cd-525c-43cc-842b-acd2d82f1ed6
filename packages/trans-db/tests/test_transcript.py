"""Tests for the array-based transcript model."""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from trans_db.models import Base, Transcript, YtJob
from trans_db.testing import create_test_db


@pytest_asyncio.fixture(scope="function")
async def test_db():
    """Create a test database."""
    engine, TestingSessionLocal = await create_test_db()
    async with TestingSessionLocal() as db:
        try:
            yield db
        finally:
            await db.close()
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)


@pytest.mark.asyncio
async def test_transcript_array_storage(test_db: AsyncSession):
    """Test storing and retrieving transcript data in array format."""
    # First create a YtJob that the transcript will reference
    yt_job = YtJob(
        video_id="test123",
        job_date=20231026,
        corpus="test_corpus",
        trail_id="test_trail",
        pl_etag="test_etag",
        kind="youtube#video",
        etag="test_etag",
        channel_id="test_channel",
        video_url="http://example.com/test",
        video_date="2023-10-26",
        thumb="http://example.com/thumb.jpg",
        title="Test Video",
        description="Test Description",
        file_status_flag=1,
    )
    test_db.add(yt_job)
    await test_db.commit()

    # Create transcript with array data
    transcript = Transcript(
        video_id="test123",
        text_segments=["Hello", "world", "!"],
        start_times=[0.0, 1.5, 3.0],
        durations=[1.0, 1.0, 0.5],
        character_map=[
            0,
            5,
            11,
            12,
        ],  # Manually calculated character positions
        slurp="Hello world !",
        tag_metadata={"language": "en", "confidence": 0.95},
    )
    test_db.add(transcript)
    await test_db.commit()

    # Retrieve and verify
    result = await test_db.get(Transcript, transcript.transcript_id)
    assert result is not None
    assert result.text_segments == ["Hello", "world", "!"]
    assert result.start_times == [0.0, 1.5, 3.0]
    assert result.durations == [1.0, 1.0, 0.5]
    assert result.character_map == [0, 5, 11, 12]
    assert result.slurp == "Hello world !"
    assert result.tag_metadata == {"language": "en", "confidence": 0.95}


@pytest.mark.asyncio
async def test_transcript_relationship(test_db: AsyncSession):
    """Test the relationship between Transcript and YtJob."""
    # Create YtJob
    yt_job = YtJob(
        video_id="test456",
        job_date=20231026,
        corpus="test_corpus",
        trail_id="test_trail",
        pl_etag="test_etag",
        kind="youtube#video",
        etag="test_etag",
        channel_id="test_channel",
        video_url="http://example.com/test",
        video_date="2023-10-26",
        thumb="http://example.com/thumb.jpg",
        title="Test Video",
        description="Test Description",
        file_status_flag=1,
    )
    test_db.add(yt_job)
    await test_db.commit()

    # Create transcript
    transcript = Transcript(
        video_id="test456",
        text_segments=["Test"],
        start_times=[0.0],
        durations=[1.0],
        character_map=[0, 4],
        slurp="Test",
        tag_metadata={},
    )
    test_db.add(transcript)
    await test_db.commit()

    # Explicitly load relationships to avoid lazy loading errors
    await test_db.refresh(transcript, ["job"])

    # Verify relationship
    assert transcript.job.video_id == "test456"
    assert transcript.job.title == "Test Video"


@pytest.mark.asyncio
async def test_character_map_validation(test_db: AsyncSession):
    """Test that character map matches text segment lengths."""
    # Create YtJob first
    yt_job = YtJob(
        video_id="test789",
        job_date=20231026,
        corpus="test_corpus",
        trail_id="test_trail",
        pl_etag="test_etag",
        kind="youtube#video",
        etag="test_etag",
        channel_id="test_channel",
        video_url="http://example.com/test",
        video_date="2023-10-26",
        thumb="http://example.com/thumb.jpg",
        title="Test Video",
        description="Test Description",
        file_status_flag=1,
    )
    test_db.add(yt_job)
    await test_db.commit()

    # Test with multi-segment text
    segments = ["First", "Second", "Third"]
    char_map = [0]  # Start at 0
    current_pos = 0

    for segment in segments:
        current_pos += len(segment) + 1  # +1 for space
        char_map.append(current_pos)

    transcript = Transcript(
        video_id="test789",
        text_segments=segments,
        start_times=[0.0, 2.0, 4.0],
        durations=[1.5, 1.5, 1.5],
        character_map=char_map,
        slurp="First Second Third",
        tag_metadata={"test": True},
    )
    test_db.add(transcript)
    await test_db.commit()
    await test_db.refresh(transcript)

    # Verify character map positions
    assert transcript.character_map == [
        0,
        6,
        13,
        18,
    ]  # Manually verified positions
    assert len(transcript.character_map) == len(transcript.text_segments) + 1


@pytest.mark.asyncio
async def test_metadata_jsonb(test_db: AsyncSession):
    """Test JSONB metadata field with various data types."""
    # Create YtJob first
    yt_job = YtJob(
        video_id="test101",
        job_date=20231026,
        corpus="test_corpus",
        trail_id="test_trail",
        pl_etag="test_etag",
        kind="youtube#video",
        etag="test_etag",
        channel_id="test_channel",
        video_url="http://example.com/test",
        video_date="2023-10-26",
        thumb="http://example.com/thumb.jpg",
        title="Test Video",
        description="Test Description",
        file_status_flag=1,
    )
    test_db.add(yt_job)
    await test_db.commit()

    # Test complex metadata
    complex_metadata = {
        "language": "en",
        "confidence": 0.95,
        "tags": ["test", "example", "metadata"],
        "processing": {
            "version": "1.0",
            "date": "2023-10-26",
            "stats": {"words": 10, "duration": 5.5},
        },
    }

    transcript = Transcript(
        video_id="test101",
        text_segments=["Test"],
        start_times=[0.0],
        durations=[1.0],
        character_map=[0, 4],
        slurp="Test",
        tag_metadata=complex_metadata,
    )
    test_db.add(transcript)
    await test_db.commit()
    await test_db.refresh(transcript)

    # Verify metadata was stored and retrieved correctly
    assert transcript.tag_metadata == complex_metadata
    assert transcript.tag_metadata["processing"]["stats"]["words"] == 10
    assert transcript.tag_metadata["tags"] == ["test", "example", "metadata"]
