{"name": "tooling-trans_api", "version": "0.2.0", "private": false, "type": "module", "scripts": {"dev": "cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api dev && exit 0", "build": "export REPO_DIR=\"/home/<USER>/repo/knowtrails\" && cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api prod", "build-docker": "../../packages/tooling/run-docker-build.sh trans_api prod", "dev-docker": "../../packages/tooling/run-docker-build.sh trans_api dev", "bbb": "'source /home/<USER>/repo/knowtrails/repo-scripts/repo-env-setup.sh' && cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api prod", "test-prod": "cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api prod test", "test-dev": "cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api dev test", "test": "cd ../../packages/tooling/ && tsx ./build-docker-compose.ts trans_api dev test", "lint": "echo 'Add python lint script here' && exit 1"}, "dependencies": {"tsx": "^4.19.4"}}