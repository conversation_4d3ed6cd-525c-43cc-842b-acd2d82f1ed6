{"name": "@repo/ui", "version": "0.0.0", "private": true, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.4", "@turbo/gen": "^2.4.0", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.20.0", "tailwindcss": "^4.1.4", "typescript": "5.7.3"}, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.0.1", "tw-animate-css": "^1.2.5"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./org-comps/*": "./src/*.tsx", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}