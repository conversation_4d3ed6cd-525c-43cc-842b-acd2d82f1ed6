import * as fs from 'fs';
import * as dotenv from 'dotenv';
import { Octokit } from '@octokit/rest';
import * as sodium from 'libsodium-wrappers';
// import nacl from 'tweetnacl';
// import { encodeUTF8, decodeUTF8, encodeBase64 } from 'tweetnacl-util';
import { exit } from 'process';
dotenv.config();
const token = process.env.GITHUB_TOKEN;
const owner = process.env.GITHUB_OWNER || "knowtrails";
const repo = process.env.GITHUB_REPO || "ideatrails";
const envFile = '.env';
if (!token || !owner || !repo) {
    throw new Error('Missing GITHUB_TOKEN, GITHUB_OWNER, or GITHUB_REPO in process.env');
}
const octokit = new Octokit({ auth: token });
async function getPublicKey() {
    const { data } = await octokit.actions.getRepoPublicKey({ owner, repo });
    return {
        keyId: data.key_id,
        publicKey: data.key,
    };
}
async function getRepositoryId() {
    const { data: repoData } = await octokit.repos.get({ owner, repo });
    const repository_id = repoData.id;
    return {
        RepositoryId: repository_id
    };
}
async function encryptSecret(publicKey, secretValue) {
    await sodium.ready;
    const binkey = sodium.from_base64(publicKey, sodium.base64_variants.ORIGINAL);
    const binsec = sodium.from_string(secretValue);
    const encBytes = sodium.crypto_box_seal(binsec, binkey);
    return sodium.to_base64(encBytes, sodium.base64_variants.ORIGINAL);
}
// function encryptSecret(publicKey: string, secretValue: string): string {
//   const messageBytes = decodeUTF8(secretValue);
//   const keyBytes = decodeUTF8(publicKey);
//   const encryptedBytes = nacl.box.seal(messageBytes, keyBytes);
//   return encodeBase64(encryptedBytes);
// }
function parseEnvFile(path) {
    const content = fs.readFileSync(path, 'utf-8');
    const lines = content.split('\n');
    const entries = {};
    for (const line of lines) {
        if (line.trim() === '' || line.startsWith('#'))
            continue;
        const [key, ...rest] = line.split('=');
        if (!key) {
            console.error(`parseEnvFile(): Missing key in line: ${line}`);
            exit(1);
        }
        entries[key.trim()] = rest.join('=').trim();
    }
    return entries;
}
async function uploadSecrets() {
    const envVars = parseEnvFile(envFile);
    const { keyId, publicKey } = await getPublicKey();
    const { RepositoryId } = await getRepositoryId();
    for (const [name, value] of Object.entries(envVars)) {
        const encryptedValue = await encryptSecret(publicKey, value);
        await octokit.actions.createOrUpdateEnvironmentSecret({
            owner: owner,
            repo: repo,
            environment_name: 'prod-turbo',
            secret_name: name,
            encrypted_value: encryptedValue,
            key_id: keyId
        });
        await octokit.actions.createOrUpdateEnvironmentSecret({
            owner: owner,
            repo: repo,
            environment_name: 'dev-turbo',
            secret_name: name,
            encrypted_value: encryptedValue,
            key_id: keyId
        });
        console.log(`✅ Uploaded secret: ${name}`);
    }
}
uploadSecrets().catch(err => {
    console.error('❌ Failed to upload secrets:', err);
});
