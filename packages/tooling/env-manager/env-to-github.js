import fs from 'fs/promises';
import path from 'path';
import * as core from '@actions/core';
import { getOctokit } from '@actions/github';
import sodium from 'libsodium-wrappers';
await sodium.ready;
const key = sodium.randombytes_buf(sodium.crypto_secretbox_KEYBYTES);
async function readEnvFile(filepath) {
    const content = await fs.readFile(filepath, 'utf-8');
    const lines = content.split('\n');
    const env = {};
    for (const line of lines) {
        const trimmed = line.trim();
        if (!trimmed || trimmed.startsWith('#'))
            continue;
        const eqIndex = trimmed.indexOf('=');
        if (eqIndex === -1)
            continue;
        const key = trimmed.substring(0, eqIndex).trim();
        const value = trimmed.substring(eqIndex + 1).trim();
        env[key] = value;
    }
    return env;
}
async function uploadSecret(octokit, owner, repo, secretName, secretValue) {
    // Get public key for repo secrets
    const { data: publicKeyData } = await octokit.rest.actions.getRepoPublicKey({
        owner,
        repo,
    });
    const publicKey = publicKeyData.key;
    const keyId = publicKeyData.key_id;
    // Encrypt secret using libsodium
    const messageBytes = Buffer.from(secretValue);
    const keyBytes = Buffer.from(publicKey, 'base64');
    const encryptedBytes = sodium.crypto_box_seal(messageBytes, keyBytes);
    const encryptedValue = Buffer.from(encryptedBytes).toString('base64');
    // Create or update secret
    await octokit.rest.actions.createOrUpdateRepoSecret({
        owner,
        repo,
        secret_name: secretName,
        encrypted_value: encryptedValue,
        key_id: keyId,
    });
    core.info(`Uploaded secret ${secretName}`);
}
// should retain an explicit mapping of variables to file to deserialize quickly and deliberatly, 
// not being loose and and resorting to combing the entire env world and double-loop it in deserialize.
async function serializeAllSecrets(token, owner, repo, secrets) {
    const octokit = getOctokit(token);
    const variableTracker = {};
    for (const [prefix, envFilePath] of Object.entries(secrets)) {
        const envFile = path.resolve(process.cwd(), envFilePath);
        core.info(`Processing file ${envFile} with prefix ${prefix}`);
        let envMap;
        try {
            envMap = await readEnvFile(envFile);
        }
        catch (e) {
            core.warning(`Skipping ${envFilePath}: ${e.message}`);
            continue;
        }
        variableTracker[envFile] = Object.keys(envMap).map(key => `${prefix}${key}`);
        for (const [key, value] of Object.entries(envMap)) {
            const secretName = prefix + key.toUpperCase().replace(/[^A-Z0-9_]/g, '_');
            try {
                await uploadSecret(octokit, owner, repo, secretName, value);
            }
            catch (e) {
                core.warning(`Failed to upload secret ${secretName}: ${e.message}`);
            }
        }
    }
}
export { serializeAllSecrets };
