import secretMap from './prefix-map.json' with { type: "json" };
import { serializeAllSecrets } from './env-to-github.js';
(async () => {
    try {
        const token = process.env.GH_TOKEN;
        const owner = 'angels-4/knowtrails';
        const githubRepo = 'angels-4/knowtrails';
        if (!token || !owner || !githubRepo) {
            throw new Error('Missing required environment variables.');
        }
        await serializeAllSecrets(token, owner, githubRepo, secretMap);
        console.log('🌲 Secrets serialized successfully.');
    }
    catch (err) {
        console.error('💥 Failed to serialize secrets:', err);
        process.exit(1);
    }
})();
