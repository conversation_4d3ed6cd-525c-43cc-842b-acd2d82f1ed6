{"name": "tooling", "version": "0.1.0", "private": false, "type": "module", "dependencies": {"@actions/core": "^1.11.1", "@actions/github": "^6.0.1", "@octokit/rest": "^21.1.1", "dotenv": "^16.0.3", "libsodium-wrappers": "^0.7.15"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/libsodium-wrappers": "^0.7.14", "@types/node": "^22", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.25.0", "typescript": "5.8.2", "tsx": "^4.0.0"}}