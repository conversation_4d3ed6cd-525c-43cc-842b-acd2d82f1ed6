# syntax: hcl

group "default" {
  targets = [
    "trans_api-dev-runner",
    "trans_api-prod-runner",
    "web-dev-runner",
    "web-prod-runner"
  ]
}

# "celery-prod-runner", # Added celery-prod-runner to the default group

variable "IMAGETAG" {
  type    = string
  default = "0.8.1"
}

target "trans_api-prod-runner" {
  context    = "../../"
  dockerfile = "apps/trans_api/Dockerfile"
  target     = "prod-runner"
  tags       = ["ideas/udu:trans_api-prod-${IMAGETAG}"]
  cache_from = ["type=local,src=/tmp/.buildcache"]
  args = {
    DEBUG = "true"
  }
}

target "trans_api-dev-runner" {
  context    = "../../"
  dockerfile = "apps/trans_api/Dockerfile"
  target     = "dev-runner"
  tags       = ["ideas/udu:trans_api-dev-${IMAGETAG}"]
  cache_from = ["type=local,src=/tmp/.buildcache"]
  args = {
    DEBUG = "true"
  }
}

# cache_from = ["type=local,src=${HOME}/.docker-cache/trans_api"] #["type=local,src=/tmp/.buildcache"]


target "web-prod-runner" {
  context    = "../../"
  dockerfile = "apps/web/Dockerfile"
  target     = "prod-runner"
  tags       = ["ideas/udu:web-prod-${IMAGETAG}"]
  cache_from = ["type=local,src=/tmp/.buildcache"]
  args = {
    DEBUG = "true"
  }
}

target "web-dev-runner" {
  context    = "../../"
  dockerfile = "apps/web/Dockerfile"
  target     = "dev-runner"
  tags       = ["ideas/udu:web-dev-${IMAGETAG}"]
  cache_from = ["type=local,src=/tmp/.buildcache"]
  args = {
    DEBUG = "true"
  }
}



# target.name
# target.context
# target.args
# target.description
# target.dockerfile
# target.secret
# target.target
# target.tags

# target.network
# target.inherits
# target.labels
# target.ssh
# target.ulimits
# target.shm-size

# target.pull
# target.contexts
# target.annotations
# target.attest
# target.cache-from
# target.cache-to
# target.call
# target.dockerfile-inline
# target.entitlements
# target.matrix
# target.no-cache-filter
# target.no-cache
# target.output
# target.platforms




# If you're doing local development and want persistent caching between reboots or builds, 
# consider using a more stable path like:
#
# cache_from = ["type=local,src=./.docker-cache"]
# Or better yet:
# cache_from = ["type=local,src=${HOME}/.docker-cache/trans_api"]
# 
# target "celery-prod-runner" {
#   context    = "../../apps/celery_worker/"
#   dockerfile = "Dockerfile-celery"
#   target     = "prod-runner"
#   tags       = ["ideas/udu:celery-prod-${IMAGETAG}"]
#   cache_from = ["type=local,src=/tmp/.buildcache"]
#   args = {
#     DEBUG = "false"
#   }
# }
# 
