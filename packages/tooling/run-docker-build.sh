#!/bin/bash

# =============================================================================
# run-docker-build.sh
# Author: Angels Meta
# Usage: Helper script for Turborepo to orchestrate Docker builds.
# Description: <PERSON>les changing directory and passing force flags to bake-and-compose.sh.
#
# Arguments:
#   $1: appName (e.g., trans_api, web)
#   $2: buildMode (e.g., prod, dev)
#
# History: 2025-06-05: Angels Meta: Initial version.
# =============================================================================

source "$(dirname "$0")/../repo-scripts/repo-env-setup.sh" # Source from repo-scripts
source "$(dirname "$0")/../repo-scripts/log_utils.sh"     # Source from repo-scripts

# Check for correct number of arguments
if [ "$#" -lt 2 ]; then
    logLabError "Usage: $0 <appName> <buildMode>"
    exit 1
fi

APP_NAME="$1"
BUILD_MODE="$2"
OPTIONAL_BAKE_ARGS=""

# Check for TURBO_FORCE environment variable set by Turborepo
if [[ "${TURBO_FORCE:-}" = "true" ]]; then
    logLabInfo "TURBO_FORCE detected. Adding --no-cache to bake command."
    OPTIONAL_BAKE_ARGS="--set ${APP_NAME}-${BUILD_MODE}-runner.no-cache=true"
fi

# This script is called from packages/tooling-trans_api or packages/tooling-web.
# It needs to call bake-and-compose.sh from repo-scripts/
# and ensure the working directory for bake-and-compose.sh is packages/tooling/.

# Change to the packages/tooling directory
(
    cd "$(dirname "$0")" || { logLabError "Failed to change directory to packages/tooling."; exit 1; }
    logLabInfo "Changed directory to packages/tooling for bake operations."

    # Call bake-and-compose.sh from repo-scripts/
    # Relative path from packages/tooling to repo-scripts/ is ../repo-scripts/
    ../repo-scripts/bake-and-compose.sh "${APP_NAME}" "${BUILD_MODE}" "${OPTIONAL_BAKE_ARGS}"
)

exit $?
