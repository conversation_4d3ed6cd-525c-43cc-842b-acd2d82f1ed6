// ============================================================================
// Turborepo custom tooling package script to 
// build and push docker image and start docker container stack.
// for repo targetet applications.
// Compose up optional based on composefile env setting.
//
// TODO: Suggestion: document that docker network create app_network must be run beforehand.
// it doesn't exist, docker compose up will fail.
// ============================================================================
import { execSync } from 'child_process';
import dotenv from 'dotenv'
import path from 'path'
import fs from 'fs'
import {
    logLabCreate, logLabExecute,
    logLabComplete, logLabSkip,
    logLabDebug, logLabError
} from './logLabs.js';

type TurboMode = 'dev' | 'prod';

const appName = process.argv[2];
const buildMode = process.argv[3] as TurboMode;

if (!appName) {
    console.error(`tooling#build ${logLabError} invalid appName: ${appName}`);
    process.exit(1);
}

if (buildMode != 'prod' && buildMode !== 'dev') {
    console.error(`tooling#build ${logLabError} invalid buildMode: ${buildMode}`);
    process.exit(1);
}
console.log(`Building and running for ${appName} in ${buildMode} mode.`);

function loadEnvFile(envFile: string) {
    if (!fs.existsSync(envFile)) {
        console.error(`tooling#build ${logLabError} Missing .env file: ${envFile}`);
        process.exit(1);
    }
    dotenv.config({ path: envFile });
    console.log(`tooling#build 🧬 envFile loading complete: ${envFile}`);
}

loadEnvFile(path.resolve(process.cwd(), `./.env.${appName}.build`));
const externalEnv = process.env[buildMode === 'prod' ? 'EXTERNAL_PROD_ENVS' : 'EXTERNAL_DEV_ENVS'];
if (externalEnv) loadEnvFile(path.resolve(process.cwd(), externalEnv));

const requiredEnvVars = [
    'SERVICES',
    'COMPOSEFILE',
    'PROGRESS',
];

for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        console.error(`tooling#build ${logLabError} Missing ${envVar} env variable. Please check your .env.${appName}.build file.`);
        process.exit(1);
    }
}

let services = process.env.SERVICES;
const composeFile = process.env.COMPOSEFILE;
const progress = process.env.PROGRESS;
const is_nocache = process.env.NOCACHE === 'true'
const is_provenance = process.env.PROVENANCE === 'true'
const is_sbom = process.env.SBOM === 'true'
const is_push = process.env.PUSH === 'true'
const is_build = process.env.IS_BUILD === 'true'
const is_compose = process.env.IS_COMP === "true";
const is_force_recreate = process.env.FORCE_RECREATE === 'true'

if (!services || !composeFile || !progress) {
    console.error(`tooling#build ${logLabError} Missing SERVICES env variable. Please check your .env.${appName}.build file.`);
    process.exit(1);
}


function printVars() {
    console.log(`${logLabDebug} services: ${services}`);
    console.log(`${logLabDebug} composeFile: ${composeFile}`);
    console.log(`${logLabDebug} progress: ${progress}`);
    console.log(`${logLabDebug} is_nocache: ${is_nocache}`)
    console.log(`${logLabDebug} is_provenance: ${is_provenance}`)
    console.log(`${logLabDebug} is_sbom: ${is_sbom}`);
    console.log(`${logLabDebug} is_push: ${is_push}`);
    console.log(`${logLabDebug} is_build: ${is_build}`);
    console.log(`${logLabDebug} is_compose: ${is_compose}`);
    console.log(`${logLabDebug} is_force_recreate: ${is_force_recreate}`);
}
printVars();

const bakeTarget = `${appName}-${buildMode}-runner`; // e.g. "trans_api-prod-runner" or "web-dev-runner"
let fullImageName: string | undefined; // Will be populated after bake --print

console.log(`${logLabDebug} bakeTarget: ${bakeTarget}`);


const composeServices = services
    .split(' ')
    .map(word => `${word}-${buildMode}`)
    .join(' ');
console.log(`${logLabDebug} serviceList: ${composeServices}`);

const buildAndPush = () => {
    // First, run docker buildx bake with --print to get the build configuration, including image tags
    const bakeCommand = `docker buildx bake ${bakeTarget} --print`;
    console.log(`${logLabExecute}: ${bakeCommand}`);
    let bakeOutput: any;
    try {
        const output = execSync(bakeCommand, { encoding: 'utf8' }); // Removed stdio: 'inherit'
        bakeOutput = JSON.parse(output);
    } catch (error) {
        console.error(`tooling#build ${logLabError} buildx bake --print failed:`, error);
        process.exit(1);
    }

    // Extract the full image name from the bake output
    const targetConfig = bakeOutput.target[bakeTarget];
    if (!targetConfig || !targetConfig.tags || targetConfig.tags.length === 0) {
        console.error(`tooling#build ${logLabError} Could not find image tags in bake output for target ${bakeTarget}`);
        process.exit(1);
    }
    fullImageName = targetConfig.tags[0]; // Assuming the first tag is the primary one
    
    
    //  duplicate code
    const bakeArgs = [
        is_nocache ? `--set ${bakeTarget}.args.NO_CACHE=true` : '',
        is_provenance ? `--set ${bakeTarget}.args.PROVENANCE=true` : '',
        is_sbom ? `--set ${bakeTarget}.args.SBOM=true` : '',
        is_push ? '--push' : '',
        `--progress=${progress}`,
        appName === 'web' ? '--allow=fs.read=/home/<USER>' : '',
    ].filter(Boolean).join(' ');

    const command = `BUILDX_BAKE_ENTITLEMENTS_FS=0 docker buildx bake ${bakeTarget} ${bakeArgs}`;
    console.log(`${logLabExecute}: ${command}`);
    try {
        // The context is now defined in the HCL, so we don't need to pass cwd here
        execSync(command, { stdio: 'inherit' });
        console.log(`${logLabCreate} image ${fullImageName} successfully built and pushed.`);
    } catch (error) {
        console.error(`tooling#build ${logLabError} buildx bake failed:`, error);
        process.exit(1);
    }

    // console.log(`${logLabDebug} buildArgs: ${buildArgs}`);
    // console.log(`${logLabDebug} provenance: ${is_provenance}`);

    // const DEBUG_DOCKER = "--build-arg DEBUG=true"
    // const command = `docker build ${DEBUG_DOCKER} ${buildArgs}`
    // console.log(`${logLabExecute}: ${command} from CWD: ${context}`);

    // try {
    //     console.log(`Building and pushing image ${fullImageName}...`);
    //     execSync(`${command}`, { stdio: 'inherit', cwd: path.resolve(process.cwd(), context) }); // Execute in the context directory
    //     console.log(`${logLabCreate} image ${fullImageName} successfully.`);
    //     if (is_push) {
    //         console.log(`⛵ pushed image ${fullImageName} to dockerhub successfully.`);
    //     };
    // } catch (error) {
    //     console.error(`tooling#build ${logLabError} during production build and push for ${fullImageName}:`, error);
    //     process.exit(1);
    // }
}

// function safeRmContainer() {
//     try {
//         console.log(`Stop and stop running containers from ${composeFile} ...`);
//         const command = `IMAGE=${fullImageName} docker compose  --file ${composeFile} down`
//         console.log(`${logLabExecute}: ${command}`);
//         console.log(`Execute command: ${command}`);
//         execSync(`${command}`, { stdio: 'inherit' });
//         console.log(`containers stopped successfully.`);
//     } catch (error) {
//         console.error(`tooling#build ${logLabError} Failed to stop running containers from ${composeFile}: ${error.message}`);
//     }
// }

function composeUp() {
    const composeArgs = [
        appName === 'web' ? '-f' : '',
        appName === 'web' ? '../../docker-compose.yml' : '',
        '-f', composeFile,
        'up',
        //  '--build',
        // '--remove-orphans',
        is_force_recreate ? '--force-recreate' : '',
        // '--no-deps',
        '-d',
        composeServices
    ].filter(Boolean).join(' ');

    const command = `IMAGE=${fullImageName} docker compose ${composeArgs}`;

    try {
        console.log(`${logLabExecute}: ${command}`);
        // safeRmContainer();
        execSync(command, { stdio: "inherit" });
        console.log(`${logLabCreate} container for image ${fullImageName}`);
    } catch (error) {
        console.error(`tooling#build ${logLabError} docker compose up:`, error);
        process.exit(1);
    }
}

function process_job() {
    console.log('Custom tooling evoked ... ');

    // Shared services are now managed by the Makefile and run-shared-services.sh script
    // They are brought up as dependencies of the all-dev and all-prod targets

    if (is_build) {
        // Build trans_api image
        buildAndPush();
        console.log(`${logLabComplete} trans_api docker build`);
    } else {
        console.log(`${logLabSkip} skipping docker build`);
    }

    if (is_compose) {
        composeUp()
        console.log(`${logLabComplete} docker compose up`)
    } else {
        console.log(`${logLabSkip} docker compose up`);
    }

    if (!is_build && !is_compose) { // Update condition
        console.log(`${logLabSkip} Tooling build for both docker build and docker compose up options (did nothing)`);
    }

    process.exit(0);
}

process_job()
