import os

from celery import Celery

# Celery configuration
# Use environment variables for broker and backend URLs
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0")

shared_celery_app = Celery(
    "test_share",
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=['packages.test_share.tasks', 'apps.trans_api.trans_tasks.example_tasks'] # Include tasks from shared and trans_api
)

shared_celery_app.conf.update(
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    timezone='UTC',
    enable_utc=True,
    task_default_queue="test" # Keep the default queue for test_share tasks
)
