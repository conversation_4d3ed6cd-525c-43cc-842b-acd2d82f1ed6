import random
import time

from .celery_app import shared_celery_app

FORTUNES = [
    "Our greatest glory is not in never falling, but in rising every time we fall.",
    "It does not matter how slowly you go as long as you do not stop.",
    "The man who asks a question is a fool for a minute, the man who does not ask is a fool for life.",
    "By three methods we may learn wisdom: First, by reflection, which is noblest; Second, by imitation, which is easiest; and third by experience, which is the bitterest.",
    "Before you embark on a journey of revenge, dig two graves.",
]

@shared_celery_app.task
def delayed_fortune(seconds: int = 3) -> str:
    time.sleep(seconds)  # blocking — okay for testing
    fortune = random.choice(FORTUNES)
    print(f"🎱 Fortune after {seconds}s: {fortune}")
    return fortune
