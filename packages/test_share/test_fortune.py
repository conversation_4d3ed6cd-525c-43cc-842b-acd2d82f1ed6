from test_share.tasks import delayed_fortune

def test_delayed_fortune():
    result = delayed_fortune.apply(args=[1])
    assert result.successful()
    assert result.result in [
        "Our greatest glory is not in never falling, but in rising every time we fall.",
        "It does not matter how slowly you go as long as you do not stop.",
        "The man who asks a question is a fool for a minute, the man who does not ask is a fool for life.",
        "By three methods we may learn wisdom: First, by reflection, which is noblest; Second, by imitation, which is easiest; and third by experience, which is the bitterest.",
        "Before you embark on a journey of revenge, dig two graves.",
    ]
