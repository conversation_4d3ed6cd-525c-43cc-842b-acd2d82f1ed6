"""Tests for Discord bot database models."""

from datetime import datetime

import pytest
from discord_trans_db.models import (
    Base,
    CachedTranscript,
    DiscordUser,
    SearchHistory,
)
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker


@pytest.fixture
async def async_session():
    """Create an async test database session."""
    # Use in-memory SQLite for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async_session_maker = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session_maker() as session:
        yield session

    await engine.dispose()


@pytest.mark.asyncio
async def test_discord_user_creation(async_session):
    """Test creating a Discord user."""
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
        skill_level=3,
        temperament="analytical",
    )

    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    assert user.discord_id == "123456789012345678"
    assert user.discord_username == "testuser"
    assert user.skill_level == 3
    assert user.temperament == "analytical"
    assert user.created_at is not None


@pytest.mark.asyncio
async def test_discord_user_to_dict(async_session):
    """Test Discord user serialization."""
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
        preferred_topics=["coding", "AI"],
        bot_metadata={"test": "data"},
    )

    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    user_dict = user.to_dict()

    assert user_dict["discord_id"] == "123456789012345678"
    assert user_dict["discord_username"] == "testuser"
    assert user_dict["preferred_topics"] == ["coding", "AI"]
    assert user_dict["bot_metadata"] == {"test": "data"}


@pytest.mark.asyncio
async def test_cached_transcript_creation(async_session):
    """Test creating a cached transcript."""
    transcript_data = {
        "transcript": [
            {"text": "Hello world", "start": 0.0, "duration": 2.0},
            {"text": "This is a test", "start": 2.0, "duration": 3.0},
        ]
    }

    cached_transcript = CachedTranscript(
        video_id="test_video_123",
        title="Test Video",
        transcript_json=transcript_data,
        slurp_text="Hello world This is a test",
        cache_source="test",
    )

    async_session.add(cached_transcript)
    await async_session.commit()
    await async_session.refresh(cached_transcript)

    assert cached_transcript.video_id == "test_video_123"
    assert cached_transcript.title == "Test Video"
    assert cached_transcript.transcript_json == transcript_data
    assert cached_transcript.slurp_text == "Hello world This is a test"
    assert cached_transcript.cache_source == "test"


@pytest.mark.asyncio
async def test_search_history_with_user_relationship(async_session):
    """Test search history with user relationship."""
    # Create user first
    user = DiscordUser(
        discord_id="123456789012345678", discord_username="testuser"
    )
    async_session.add(user)
    await async_session.commit()

    # Create search history
    search = SearchHistory(
        discord_user_id=user.discord_id,
        search_term="test search",
        transcript_id="video123",
        results_found=5,
    )
    async_session.add(search)
    await async_session.commit()
    await async_session.refresh(search)

    # Test relationship - need to refresh to load relationships
    await async_session.refresh(user, ["search_history"])
    await async_session.refresh(search, ["user"])

    assert search.user.discord_username == "testuser"
    assert len(user.search_history) == 1
    assert user.search_history[0].search_term == "test search"


@pytest.mark.asyncio
async def test_search_history_to_dict(async_session):
    """Test search history serialization."""
    user = DiscordUser(
        discord_id="123456789012345678", discord_username="testuser"
    )
    async_session.add(user)
    await async_session.commit()

    search = SearchHistory(
        discord_user_id=user.discord_id,
        search_term="test search",
        transcript_id="video123",
        results_found=5,
        search_metadata={"performance": "fast"},
    )
    async_session.add(search)
    await async_session.commit()
    await async_session.refresh(search)

    search_dict = search.to_dict()

    assert search_dict["discord_user_id"] == "123456789012345678"
    assert search_dict["search_term"] == "test search"
    assert search_dict["transcript_id"] == "video123"
    assert search_dict["results_found"] == 5
    assert search_dict["search_metadata"] == {"performance": "fast"}
