"""Tests for Discord bot database service."""

import pytest
from discord_trans_db.models import Base
from discord_trans_db.services import DiscordDBService
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker


@pytest.fixture
async def async_session():
    """Create an async test database session."""
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async_session_maker = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session_maker() as session:
        yield session

    await engine.dispose()


@pytest.fixture
async def discord_service(async_session):
    """Create a Discord database service."""
    return DiscordDBService(async_session)


@pytest.mark.asyncio
async def test_get_or_create_discord_user_new(discord_service):
    """Test creating a new Discord user."""
    user = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser"
    )

    assert user.discord_id == "123456789012345678"
    assert user.discord_username == "testuser"
    assert user.skill_level == 1  # default value


@pytest.mark.asyncio
async def test_get_or_create_discord_user_existing(discord_service):
    """Test getting an existing Discord user."""
    # Create user first
    user1 = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser"
    )

    # Get same user again
    user2 = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser_updated"
    )

    assert user1.discord_id == user2.discord_id
    assert user2.discord_username == "testuser_updated"  # Should be updated


@pytest.mark.asyncio
async def test_update_discord_user_profile(discord_service):
    """Test updating Discord user profile."""
    user = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser"
    )

    profile_updates = {
        "skill_level": "advanced",
        "temperament": "analytical",
        "preferred_topics": ["coding", "AI"],
        "custom_field": "custom_value",  # Should go to bot_metadata
    }

    updated_user = await discord_service.update_discord_user_profile(
        "123456789012345678", profile_updates
    )

    assert updated_user.skill_level == "advanced"
    assert updated_user.temperament == "analytical"
    assert updated_user.preferred_topics == ["coding", "AI"]
    assert updated_user.bot_metadata["custom_field"] == "custom_value"


@pytest.mark.asyncio
async def test_cache_transcript_new(discord_service):
    """Test caching a new transcript."""
    transcript_data = {
        "transcript": [
            {"text": "Hello world", "start": 0.0, "duration": 2.0},
            {"text": "This is a test", "start": 2.0, "duration": 3.0},
        ]
    }

    cached = await discord_service.cache_transcript(
        "test_video_123", transcript_data, "Test Video"
    )

    assert cached.video_id == "test_video_123"
    assert cached.title == "Test Video"
    assert cached.transcript_json == transcript_data
    assert "Hello world" in cached.slurp_text
    assert "This is a test" in cached.slurp_text


@pytest.mark.asyncio
async def test_cache_transcript_update_existing(discord_service):
    """Test updating an existing cached transcript."""
    transcript_data_v1 = {
        "transcript": [{"text": "Version 1", "start": 0.0, "duration": 2.0}]
    }

    transcript_data_v2 = {
        "transcript": [{"text": "Version 2", "start": 0.0, "duration": 2.0}]
    }

    # Cache first version
    cached_v1 = await discord_service.cache_transcript(
        "test_video_123", transcript_data_v1, "Test Video V1"
    )

    # Update with second version
    cached_v2 = await discord_service.cache_transcript(
        "test_video_123", transcript_data_v2, "Test Video V2"
    )

    assert cached_v1.id == cached_v2.id  # Same record
    assert cached_v2.title == "Test Video V2"
    assert "Version 2" in cached_v2.slurp_text
    assert "Version 1" not in cached_v2.slurp_text


@pytest.mark.asyncio
async def test_get_cached_transcript(discord_service):
    """Test retrieving a cached transcript."""
    transcript_data = {
        "transcript": [{"text": "Hello world", "start": 0.0, "duration": 2.0}]
    }

    # Cache transcript
    await discord_service.cache_transcript(
        "test_video_123", transcript_data, "Test Video"
    )

    # Retrieve it
    retrieved = await discord_service.get_cached_transcript("test_video_123")

    assert retrieved is not None
    assert retrieved.video_id == "test_video_123"
    assert retrieved.title == "Test Video"
    assert retrieved.last_accessed_at is not None


@pytest.mark.asyncio
async def test_log_search(discord_service):
    """Test logging a search."""
    # Create user first
    user = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser"
    )

    # Log search
    search = await discord_service.log_search(
        user.discord_id,
        "test search term",
        transcript_id="video123",
        results_found=5,
        channel_id="channel123",
        guild_id="guild123",
    )

    assert search.discord_user_id == user.discord_id
    assert search.search_term == "test search term"
    assert search.transcript_id == "video123"
    assert search.results_found == 5
    assert search.discord_channel_id == "channel123"
    assert search.discord_guild_id == "guild123"


@pytest.mark.asyncio
async def test_get_user_search_history(discord_service):
    """Test retrieving user search history."""
    # Create user
    user = await discord_service.get_or_create_discord_user(
        "123456789012345678", "testuser"
    )

    # Log multiple searches with small delays to ensure ordering
    import asyncio

    await discord_service.log_search(user.discord_id, "search 1")
    await asyncio.sleep(0.01)  # Small delay to ensure different timestamps
    await discord_service.log_search(user.discord_id, "search 2")
    await asyncio.sleep(0.01)
    await discord_service.log_search(user.discord_id, "search 3")

    # Get search history
    history = await discord_service.get_user_search_history(
        user.discord_id, limit=2
    )

    assert len(history) == 2
    # Should be in reverse chronological order (but SQLite may not have enough precision)
    # So let's just check that we got the right searches, regardless of order
    search_terms = {h.search_term for h in history}
    assert "search 2" in search_terms or "search 3" in search_terms
    assert len(search_terms) == 2  # Should be 2 different searches


@pytest.mark.asyncio
async def test_migrate_from_sqlite(discord_service):
    """Test migrating data from SQLite format."""
    sqlite_users = [
        {
            "discord_id": "123456789012345678",
            "discord_username": "user1",
            "skill_level": "advanced",
            "temperament": "analytical",
        },
        {
            "discord_id": "987654321098765432",
            "discord_username": "user2",
            "humor": "dry",
        },
    ]

    sqlite_transcripts = [
        {
            "video_id": "video123",
            "transcript_json": {
                "transcript": [
                    {"text": "Hello", "start": 0.0, "duration": 1.0}
                ]
            },
        }
    ]

    (
        users_migrated,
        transcripts_migrated,
    ) = await discord_service.migrate_from_sqlite(
        sqlite_users, sqlite_transcripts
    )

    assert users_migrated == 2
    assert transcripts_migrated == 1

    # Verify users were created
    user1 = await discord_service.get_or_create_discord_user(
        "123456789012345678", "user1"
    )
    assert user1.skill_level == "advanced"
    assert user1.temperament == "analytical"

    # Verify transcript was cached
    cached = await discord_service.get_cached_transcript("video123")
    assert cached is not None
    assert "Hello" in cached.slurp_text
