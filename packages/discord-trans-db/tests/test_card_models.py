"""Tests for fallacy and claim card models."""

import pytest
from discord_trans_db.models import (
    Base,
    ClaimCard,
    ClaimEvidence,
    DiscordUser,
    FallacyCard,
    FallacyExample,
)
from discord_trans_db.seed_data import seed_fallacy_cards
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker


@pytest.fixture
async def async_session():
    """Create an async test database session."""
    # Use in-memory SQLite for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async_session_maker = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session_maker() as session:
        yield session

    await engine.dispose()


@pytest.mark.asyncio
async def test_fallacy_card_creation(async_session):
    """Test creating a fallacy card."""
    fallacy = FallacyCard(
        name="Test Fallacy",
        category="Logical",
        description="A test fallacy for unit testing",
        complexity_level=1,
        unlock_meta_level=0,
    )

    async_session.add(fallacy)
    await async_session.commit()
    await async_session.refresh(fallacy)

    assert fallacy.id is not None
    assert fallacy.name == "Test Fallacy"
    assert fallacy.category == "Logical"
    assert fallacy.complexity_level == 1
    assert fallacy.unlock_meta_level == 0


@pytest.mark.asyncio
async def test_fallacy_example_creation(async_session):
    """Test creating a fallacy example."""
    # First create a fallacy
    fallacy = FallacyCard(
        name="Strawman",
        category="Logical",
        description="Misrepresenting an argument",
        complexity_level=1,
        unlock_meta_level=0,
    )
    async_session.add(fallacy)
    await async_session.commit()
    await async_session.refresh(fallacy)

    # Then create an example
    example = FallacyExample(
        fallacy_id=fallacy.id,
        example_text="You want to reduce emissions, so you want us to live in caves",
        context_summary="Misrepresents environmental argument",
        transcript_id="test_transcript_123",
        timestamp=45.5,
    )

    async_session.add(example)
    await async_session.commit()
    await async_session.refresh(example)

    assert example.id is not None
    assert example.fallacy_id == fallacy.id
    assert example.transcript_id == "test_transcript_123"
    assert example.timestamp == 45.5


@pytest.mark.asyncio
async def test_claim_card_creation(async_session):
    """Test creating a claim card."""
    # First create a user
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
    )
    async_session.add(user)
    await async_session.commit()

    # Then create a claim
    claim = ClaimCard(
        title="Test Claim",
        description="A test claim for unit testing",
        creator_discord_id=user.discord_id,
        meta_level_0="Testing",
        meta_level_1="Unit Tests",
        meta_level_2="Models",
        difficulty_level=1,
        unlock_meta_level=0,
        is_public=True,
    )

    async_session.add(claim)
    await async_session.commit()
    await async_session.refresh(claim)

    assert claim.id is not None
    assert claim.title == "Test Claim"
    assert claim.creator_discord_id == user.discord_id
    assert claim.meta_level_0 == "Testing"
    assert claim.difficulty_level == 1


@pytest.mark.asyncio
async def test_claim_fallacy_relationship(async_session):
    """Test the many-to-many relationship between claims and fallacies."""
    # Create user
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
    )
    async_session.add(user)

    # Create fallacy
    fallacy = FallacyCard(
        name="Strawman",
        category="Logical",
        description="Misrepresenting an argument",
        complexity_level=1,
        unlock_meta_level=0,
    )
    async_session.add(fallacy)

    # Create claim
    claim = ClaimCard(
        title="Climate scientists want us to live in caves",
        description="Misrepresents environmental arguments",
        creator_discord_id=user.discord_id,
        meta_level_0="Climate",
        meta_level_1="Misrepresentation",
    )
    async_session.add(claim)
    await async_session.commit()

    # Link claim to fallacy using the association table
    from discord_trans_db.models.fallacy_card import claim_fallacy_link
    from sqlalchemy import insert

    # Insert the relationship directly into the association table
    await async_session.execute(
        insert(claim_fallacy_link).values(
            claim_id=claim.id, fallacy_id=fallacy.id
        )
    )
    await async_session.commit()

    # Test the relationship by querying with explicit loading
    from sqlalchemy import select
    from sqlalchemy.orm import selectinload

    # Query claim with fallacies loaded
    claim_with_fallacies = await async_session.execute(
        select(ClaimCard)
        .options(selectinload(ClaimCard.fallacies))
        .where(ClaimCard.id == claim.id)
    )
    claim_result = claim_with_fallacies.scalar_one()

    # Query fallacy with claims loaded
    fallacy_with_claims = await async_session.execute(
        select(FallacyCard)
        .options(selectinload(FallacyCard.claims))
        .where(FallacyCard.id == fallacy.id)
    )
    fallacy_result = fallacy_with_claims.scalar_one()

    assert len(claim_result.fallacies) == 1
    assert claim_result.fallacies[0].name == "Strawman"
    assert len(fallacy_result.claims) == 1
    assert (
        fallacy_result.claims[0].title
        == "Climate scientists want us to live in caves"
    )


@pytest.mark.asyncio
async def test_user_game_progression(async_session):
    """Test user game progression fields."""
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
        skill_level=3,
        wisdom_level=2,  # Can access meta-level 0-2 claims
        experience_points=250,
        total_opinions_assessed=5,
        wisdom_score=150,
    )

    async_session.add(user)
    await async_session.commit()
    await async_session.refresh(user)

    assert user.skill_level == 3
    assert user.wisdom_level == 2  # Can access claims up to meta-level 2
    assert user.experience_points == 250
    assert user.total_opinions_assessed == 5
    assert user.wisdom_score == 150


@pytest.mark.asyncio
async def test_seed_fallacy_cards(async_session):
    """Test seeding fallacy cards."""
    fallacies = await seed_fallacy_cards(async_session)

    assert len(fallacies) > 0
    assert "Strawman" in fallacies
    assert "Ad Hominem" in fallacies

    # Check that fallacies were actually created
    strawman = fallacies["Strawman"]
    assert strawman.name == "Strawman"
    assert strawman.category == "Logical"
    assert strawman.complexity_level == 1
    assert strawman.unlock_meta_level == 0


@pytest.mark.asyncio
async def test_claim_evidence_creation(async_session):
    """Test creating claim evidence."""
    # Create user and claim first
    user = DiscordUser(
        discord_id="123456789012345678",
        discord_username="testuser",
    )
    async_session.add(user)

    claim = ClaimCard(
        title="Test Claim",
        description="A test claim",
        creator_discord_id=user.discord_id,
    )
    async_session.add(claim)
    await async_session.commit()

    # Create evidence
    evidence = ClaimEvidence(
        claim_id=claim.id,
        evidence_text="This is supporting evidence",
        evidence_type="support",
        strength_rating=4,
        added_by_discord_id=user.discord_id,
        source_url="https://example.com/evidence",
    )

    async_session.add(evidence)
    await async_session.commit()
    await async_session.refresh(evidence)

    assert evidence.id is not None
    assert evidence.claim_id == claim.id
    assert evidence.evidence_type == "support"
    assert evidence.strength_rating == 4
