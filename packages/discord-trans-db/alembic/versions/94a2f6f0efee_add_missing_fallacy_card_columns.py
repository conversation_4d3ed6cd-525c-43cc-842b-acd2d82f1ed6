"""Add missing fallacy card columns

Revision ID: 94a2f6f0efee
Revises: 75f97739bc0c
Create Date: 2025-06-22 23:07:53.424472

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '94a2f6f0efee'
down_revision: Union[str, None] = '75f97739bc0c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('opinion_assessments',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('discord_user_id', sa.String(length=20), nullable=False),
    sa.Column('opinion_text', sa.Text(), nullable=False),
    sa.Column('target_type', sa.String(length=50), nullable=False),
    sa.Column('target_reference', sa.String(length=200), nullable=True),
    sa.Column('logical_patterns', sa.JSON(), nullable=True),
    sa.Column('wisdom_indicators', sa.JSON(), nullable=True),
    sa.Column('complexity_level', sa.Integer(), nullable=False),
    sa.Column('logical_coherence_score', sa.Float(), nullable=False),
    sa.Column('evidence_quality_score', sa.Float(), nullable=False),
    sa.Column('wisdom_potential_score', sa.Float(), nullable=False),
    sa.Column('community_resonance', sa.Float(), nullable=False),
    sa.Column('intersubjective_weight', sa.Float(), nullable=False),
    sa.Column('contributes_to_wisdom', sa.Boolean(), nullable=False),
    sa.Column('wisdom_card_potential', sa.Boolean(), nullable=False),
    sa.Column('progression_notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('source_transcript_id', sa.String(length=50), nullable=True),
    sa.Column('source_timestamp', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['discord_user_id'], ['discord_users.discord_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('opinion_reflections',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('opinion_id', sa.String(length=36), nullable=False),
    sa.Column('reflection_text', sa.Text(), nullable=False),
    sa.Column('reflection_type', sa.String(length=50), nullable=False),
    sa.Column('recognized_patterns', sa.JSON(), nullable=True),
    sa.Column('wisdom_gained', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['opinion_id'], ['opinion_assessments.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('wisdom_trails',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('topic', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('starting_opinion_id', sa.String(length=36), nullable=True),
    sa.Column('current_wisdom_level', sa.Integer(), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('mentor_insights', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['starting_opinion_id'], ['opinion_assessments.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('discord_users', sa.Column('wisdom_level', sa.Integer(), nullable=False))
    op.add_column('discord_users', sa.Column('total_opinions_assessed', sa.Integer(), nullable=False))
    op.add_column('discord_users', sa.Column('total_reflections_made', sa.Integer(), nullable=False))
    op.add_column('discord_users', sa.Column('wisdom_score', sa.Integer(), nullable=False))
    op.drop_column('discord_users', 'unlock_level')
    op.drop_column('discord_users', 'game_score')
    op.drop_column('discord_users', 'total_claims_created')
    op.drop_column('discord_users', 'total_fallacies_found')
    op.add_column('fallacy_cards', sa.Column('complexity_level', sa.Integer(), nullable=False))
    op.add_column('fallacy_cards', sa.Column('wisdom_insight', sa.Text(), nullable=True))
    op.drop_column('fallacy_cards', 'rarity')
    op.add_column('fallacy_examples', sa.Column('complexity_level', sa.Integer(), nullable=False))
    op.add_column('fallacy_examples', sa.Column('meta_level_appropriateness', sa.Integer(), nullable=False))
    op.add_column('fallacy_examples', sa.Column('is_fundamental', sa.Boolean(), nullable=False))
    op.add_column('fallacy_examples', sa.Column('educational_notes', sa.Text(), nullable=True))
    op.add_column('fallacy_examples', sa.Column('wisdom_insight', sa.Text(), nullable=True))
    op.add_column('fallacy_examples', sa.Column('source_type', sa.String(length=50), nullable=True))
    op.add_column('fallacy_examples', sa.Column('source_quality', sa.String(length=20), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('fallacy_examples', 'source_quality')
    op.drop_column('fallacy_examples', 'source_type')
    op.drop_column('fallacy_examples', 'wisdom_insight')
    op.drop_column('fallacy_examples', 'educational_notes')
    op.drop_column('fallacy_examples', 'is_fundamental')
    op.drop_column('fallacy_examples', 'meta_level_appropriateness')
    op.drop_column('fallacy_examples', 'complexity_level')
    op.add_column('fallacy_cards', sa.Column('rarity', sa.VARCHAR(length=20), nullable=False))
    op.drop_column('fallacy_cards', 'wisdom_insight')
    op.drop_column('fallacy_cards', 'complexity_level')
    op.add_column('discord_users', sa.Column('total_fallacies_found', sa.INTEGER(), nullable=False))
    op.add_column('discord_users', sa.Column('total_claims_created', sa.INTEGER(), nullable=False))
    op.add_column('discord_users', sa.Column('game_score', sa.INTEGER(), nullable=False))
    op.add_column('discord_users', sa.Column('unlock_level', sa.INTEGER(), nullable=False))
    op.drop_column('discord_users', 'wisdom_score')
    op.drop_column('discord_users', 'total_reflections_made')
    op.drop_column('discord_users', 'total_opinions_assessed')
    op.drop_column('discord_users', 'wisdom_level')
    op.drop_table('wisdom_trails')
    op.drop_table('opinion_reflections')
    op.drop_table('opinion_assessments')
    # ### end Alembic commands ###
