"""Initial Discord bot schema with fallacy and claim cards

Revision ID: 75f97739bc0c
Revises: 
Create Date: 2025-06-19 13:03:10.388138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '75f97739bc0c'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cached_transcripts',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('video_id', sa.String(length=20), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=True),
    sa.Column('transcript_json', sa.JSON(), nullable=False),
    sa.Column('slurp_text', sa.Text(), nullable=False),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('language', sa.String(length=10), nullable=True),
    sa.Column('cache_source', sa.String(length=50), nullable=False),
    sa.Column('cache_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('last_accessed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('video_id')
    )
    op.create_table('discord_users',
    sa.Column('discord_id', sa.String(length=20), nullable=False),
    sa.Column('discord_username', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=True),
    sa.Column('bot_profile_name', sa.String(length=100), nullable=True),
    sa.Column('skill_level', sa.Integer(), nullable=False),
    sa.Column('temperament', sa.String(length=50), nullable=False),
    sa.Column('humor', sa.String(length=50), nullable=False),
    sa.Column('trust_level', sa.String(length=50), nullable=False),
    sa.Column('communication_style', sa.String(length=50), nullable=False),
    sa.Column('sentiment_tendency', sa.String(length=50), nullable=False),
    sa.Column('preferred_topics', sa.JSON(), nullable=True),
    sa.Column('knowtrail_user_id', sa.String(length=36), nullable=True),
    sa.Column('bot_metadata', sa.JSON(), nullable=True),
    sa.Column('unlock_level', sa.Integer(), nullable=False),
    sa.Column('experience_points', sa.Integer(), nullable=False),
    sa.Column('total_fallacies_found', sa.Integer(), nullable=False),
    sa.Column('total_claims_created', sa.Integer(), nullable=False),
    sa.Column('game_score', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('discord_id')
    )
    op.create_table('fallacy_cards',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('rarity', sa.String(length=20), nullable=False),
    sa.Column('unlock_meta_level', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('claim_cards',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('creator_discord_id', sa.String(length=20), nullable=False),
    sa.Column('meta_level_0', sa.String(length=100), nullable=True),
    sa.Column('meta_level_1', sa.String(length=100), nullable=True),
    sa.Column('meta_level_2', sa.String(length=100), nullable=True),
    sa.Column('meta_level_3', sa.String(length=100), nullable=True),
    sa.Column('meta_level_4', sa.String(length=100), nullable=True),
    sa.Column('meta_level_5', sa.String(length=100), nullable=True),
    sa.Column('difficulty_level', sa.Integer(), nullable=False),
    sa.Column('unlock_meta_level', sa.Integer(), nullable=False),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('source_transcript_id', sa.String(length=50), nullable=True),
    sa.Column('source_timestamp', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['creator_discord_id'], ['discord_users.discord_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('title')
    )
    op.create_table('fallacy_examples',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('fallacy_id', sa.String(length=36), nullable=False),
    sa.Column('example_text', sa.Text(), nullable=False),
    sa.Column('context_summary', sa.Text(), nullable=True),
    sa.Column('transcript_id', sa.String(length=50), nullable=True),
    sa.Column('timestamp', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['fallacy_id'], ['fallacy_cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('search_history',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('discord_user_id', sa.String(length=20), nullable=False),
    sa.Column('search_term', sa.String(length=500), nullable=False),
    sa.Column('transcript_id', sa.String(length=20), nullable=True),
    sa.Column('results_found', sa.Integer(), nullable=True),
    sa.Column('search_method', sa.String(length=50), nullable=False),
    sa.Column('discord_channel_id', sa.String(length=20), nullable=True),
    sa.Column('discord_guild_id', sa.String(length=20), nullable=True),
    sa.Column('search_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['discord_user_id'], ['discord_users.discord_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('claim_evidence',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('claim_id', sa.Integer(), nullable=False),
    sa.Column('evidence_text', sa.Text(), nullable=False),
    sa.Column('evidence_type', sa.String(length=50), nullable=False),
    sa.Column('source_transcript_id', sa.String(length=50), nullable=True),
    sa.Column('source_timestamp', sa.Float(), nullable=True),
    sa.Column('source_url', sa.String(length=500), nullable=True),
    sa.Column('strength_rating', sa.Integer(), nullable=False),
    sa.Column('added_by_discord_id', sa.String(length=20), nullable=False),
    sa.ForeignKeyConstraint(['added_by_discord_id'], ['discord_users.discord_id'], ),
    sa.ForeignKeyConstraint(['claim_id'], ['claim_cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('claim_fallacy_link',
    sa.Column('claim_id', sa.Integer(), nullable=False),
    sa.Column('fallacy_id', sa.String(length=36), nullable=False),
    sa.ForeignKeyConstraint(['claim_id'], ['claim_cards.id'], ),
    sa.ForeignKeyConstraint(['fallacy_id'], ['fallacy_cards.id'], ),
    sa.PrimaryKeyConstraint('claim_id', 'fallacy_id')
    )
    op.create_table('user_claim_reference',
    sa.Column('discord_user_id', sa.String(length=20), nullable=False),
    sa.Column('claim_id', sa.Integer(), nullable=False),
    sa.Column('context_transcript_id', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['claim_id'], ['claim_cards.id'], ),
    sa.ForeignKeyConstraint(['discord_user_id'], ['discord_users.discord_id'], ),
    sa.PrimaryKeyConstraint('discord_user_id', 'claim_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_claim_reference')
    op.drop_table('claim_fallacy_link')
    op.drop_table('claim_evidence')
    op.drop_table('search_history')
    op.drop_table('fallacy_examples')
    op.drop_table('claim_cards')
    op.drop_table('fallacy_cards')
    op.drop_table('discord_users')
    op.drop_table('cached_transcripts')
    # ### end Alembic commands ###
