"""Seed data for Discord bot game mechanics."""

import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from .models import ClaimCard, FallacyCard, FallacyExample


async def seed_fallacy_cards(session: AsyncSession):
    """Seed the database with common logical fallacies."""

    fallacies = [
        # Meta Level 0 - Basic Fallacies (Complexity 1)
        (
            "<PERSON><PERSON><PERSON>",
            "Logical",
            "Misrepresenting someone's argument to make it easier to attack.",
            1,
            0,
        ),
        (
            "Ad Hominem",
            "Logical",
            "Attacking your opponent's character or personal traits instead of their argument.",
            1,
            0,
        ),
        (
            "False Dilemma",
            "Logical",
            "Presenting two options as the only possibilities when more exist.",
            1,
            0,
        ),
        # Meta Level 1 - Intermediate Fallacies (Complexity 2)
        (
            "Appeal to Ignorance",
            "Logical",
            "Claiming something is true because it hasn't been proven false.",
            2,
            1,
        ),
        (
            "Slippery Slope",
            "Logical",
            "Arguing that one small step will lead to a chain of negative events.",
            2,
            1,
        ),
        (
            "Circular Reasoning",
            "Logical",
            "Using the conclusion as a premise in your argument.",
            2,
            1,
        ),
        # Meta Level 2 - Advanced Fallacies (Complexity 3)
        (
            "Red Herring",
            "Distraction",
            "Distracting from the real issue with irrelevant information.",
            3,
            2,
        ),
        (
            "Bandwagon",
            "Social",
            "Claiming something is true because it's popular or widely believed.",
            3,
            2,
        ),
        (
            "Appeal to Authority",
            "Authority",
            "Using authority as proof rather than evidence.",
            3,
            2,
        ),
        # Meta Level 3+ - Expert Level (Complexity 4-5)
        (
            "Post Hoc",
            "Causal",
            "Assuming causation from sequence alone (correlation vs causation).",
            4,
            3,
        ),
        (
            "No True Scotsman",
            "Logical",
            "Dismissing counterexamples by redefining terms.",
            4,
            3,
        ),
        (
            "Composition Fallacy",
            "Logical",
            "Assuming what's true for parts is true for the whole.",
            5,
            4,
        ),
    ]

    created_fallacies = {}

    for name, category, description, complexity, meta_level in fallacies:
        fallacy = FallacyCard(
            id=str(uuid.uuid4()),
            name=name,
            category=category,
            description=description,
            complexity_level=complexity,
            unlock_meta_level=meta_level,
        )
        session.add(fallacy)
        created_fallacies[name] = fallacy

    await session.commit()

    # Add some examples
    examples = [
        (
            "Strawman",
            "Climate scientists want us to live in caves",
            "Misrepresents the argument about reducing emissions",
        ),
        (
            "Ad Hominem",
            "You can't trust John's economic analysis because he's a college dropout",
            "Attacks the person rather than addressing the economic argument",
        ),
        (
            "False Dilemma",
            "You're either with us or against us",
            "Ignores the possibility of neutral positions or alternative approaches",
        ),
        (
            "Appeal to Authority",
            "Einstein believed in God, so God must exist",
            "Uses authority outside their area of expertise",
        ),
        (
            "Slippery Slope",
            "If we allow gay marriage, next people will marry animals",
            "Assumes extreme consequences without justification",
        ),
    ]

    for fallacy_name, example_text, context in examples:
        if fallacy_name in created_fallacies:
            example = FallacyExample(
                fallacy_id=created_fallacies[fallacy_name].id,
                example_text=example_text,
                context_summary=context,
            )
            session.add(example)

    await session.commit()
    return created_fallacies


async def seed_sample_claims(session: AsyncSession, fallacies: dict):
    """Seed some sample claim cards."""

    claims = [
        {
            "title": "Climate change is a hoax",
            "description": "A common climate denial claim that can be analyzed for fallacies",
            "meta_level_0": "Climate",
            "meta_level_1": "Denial",
            "meta_level_2": "Conspiracy",
            "difficulty_level": 2,
            "unlock_meta_level": 1,
            "fallacies": ["Appeal to Ignorance", "Ad Hominem"],
        },
        {
            "title": "Vaccines cause autism",
            "description": "A debunked medical claim useful for fallacy analysis",
            "meta_level_0": "Health",
            "meta_level_1": "Medical",
            "meta_level_2": "Misinformation",
            "difficulty_level": 3,
            "unlock_meta_level": 2,
            "fallacies": ["Post Hoc", "Appeal to Authority"],
        },
        {
            "title": "All politicians are corrupt",
            "description": "An overgeneralization that demonstrates composition fallacy",
            "meta_level_0": "Politics",
            "meta_level_1": "Generalization",
            "meta_level_2": "Cynicism",
            "difficulty_level": 1,
            "unlock_meta_level": 0,
            "fallacies": ["Composition Fallacy"],
        },
    ]

    # Note: We'll need a creator_discord_id for these claims
    # For now, we'll create them without a creator (they'll be system claims)
    # In a real implementation, you'd pass a system user ID

    return claims  # Return for now, implement creation when we have users


async def seed_all_data(session: AsyncSession):
    """Seed all game data."""
    fallacies = await seed_fallacy_cards(session)
    claims = await seed_sample_claims(session, fallacies)
    return {"fallacies": fallacies, "claims": claims}
