"""Enhanced seed data with properly categorized examples by sophistication level."""

import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from .models import Fallacy<PERSON><PERSON>, Fallacy<PERSON><PERSON><PERSON>, ClaimCard


async def seed_enhanced_fallacy_examples(session: AsyncSession, fallacies: dict):
    """Seed fallacy examples categorized by sophistication and meta-level."""
    
    # Enhanced examples with proper categorization
    enhanced_examples = [
        # STRAWMAN FALLACY - Progressive sophistication
        {
            "fallacy": "Strawman",
            "examples": [
                {
                    "text": "Environmentalists want us to live in caves and never use technology",
                    "context": "Misrepresents environmental advocacy as extreme primitivism",
                    "difficulty": 1,
                    "rarity": "common",
                    "meta_level": 0,
                    "fundamental": True,
                    "notes": "Classic strawman - takes reasonable position and exaggerates to absurdity"
                },
                {
                    "text": "Gun control advocates want to ban all weapons and leave citizens defenseless",
                    "context": "Misrepresents nuanced gun policy as total prohibition",
                    "difficulty": 2,
                    "rarity": "rare",
                    "meta_level": 1,
                    "fundamental": False,
                    "notes": "Political strawman - common in policy debates"
                },
                {
                    "text": "Critics of AI development want to halt all technological progress",
                    "context": "Misrepresents AI safety concerns as anti-technology stance",
                    "difficulty": 3,
                    "rarity": "epic",
                    "meta_level": 2,
                    "fundamental": False,
                    "notes": "Sophisticated strawman in emerging technology debates"
                }
            ]
        },
        
        # AD HOMINEM - Progressive sophistication
        {
            "fallacy": "Ad Hominem",
            "examples": [
                {
                    "text": "Don't listen to John's economic analysis - he's just a college dropout",
                    "context": "Attacks person's education rather than addressing the argument",
                    "difficulty": 1,
                    "rarity": "common",
                    "meta_level": 0,
                    "fundamental": True,
                    "notes": "Direct personal attack - most obvious form of ad hominem"
                },
                {
                    "text": "The climate scientist's research is biased because she receives government funding",
                    "context": "Attacks funding source rather than examining the research methodology",
                    "difficulty": 2,
                    "rarity": "rare",
                    "meta_level": 1,
                    "fundamental": False,
                    "notes": "Circumstantial ad hominem - attacks circumstances, not character"
                },
                {
                    "text": "The philosopher's argument about consciousness is invalid because he's never experienced altered states",
                    "context": "Dismisses philosophical argument based on personal experience rather than logic",
                    "difficulty": 3,
                    "rarity": "epic",
                    "meta_level": 2,
                    "fundamental": False,
                    "notes": "Subtle ad hominem in academic contexts - attacks experiential authority"
                }
            ]
        },
        
        # APPEAL TO AUTHORITY - Legendary sophistication
        {
            "fallacy": "Appeal to Authority",
            "examples": [
                {
                    "text": "Einstein believed in God, so God must exist",
                    "context": "Uses scientific authority outside their domain of expertise",
                    "difficulty": 2,
                    "rarity": "rare",
                    "meta_level": 1,
                    "fundamental": True,
                    "notes": "Classic appeal to irrelevant authority"
                },
                {
                    "text": "The Nobel Prize winner endorses this investment strategy, so it must be sound",
                    "context": "Appeals to scientific prestige in unrelated financial domain",
                    "difficulty": 3,
                    "rarity": "epic",
                    "meta_level": 2,
                    "fundamental": False,
                    "notes": "Prestige transfer across unrelated domains"
                },
                {
                    "text": "Leading philosophers agree that consciousness is fundamental, therefore physicalism is false",
                    "context": "Appeals to philosophical consensus without examining the arguments",
                    "difficulty": 4,
                    "rarity": "legendary",
                    "meta_level": 3,
                    "fundamental": False,
                    "notes": "Sophisticated appeal to authority in academic philosophy - consensus doesn't determine truth"
                }
            ]
        },
        
        # POST HOC - Legendary level causal reasoning
        {
            "fallacy": "Post Hoc",
            "examples": [
                {
                    "text": "I wore my lucky socks and won the game, so the socks caused the victory",
                    "context": "Assumes causation from temporal sequence in sports superstition",
                    "difficulty": 2,
                    "rarity": "rare",
                    "meta_level": 1,
                    "fundamental": True,
                    "notes": "Basic post hoc - obvious temporal correlation without causation"
                },
                {
                    "text": "Crime rates dropped after the new mayor took office, proving his policies work",
                    "context": "Assumes political causation without considering other factors",
                    "difficulty": 3,
                    "rarity": "epic",
                    "meta_level": 2,
                    "fundamental": False,
                    "notes": "Political post hoc - ignores confounding variables and delayed effects"
                },
                {
                    "text": "Consciousness emerged after neural complexity reached a threshold, therefore complexity causes consciousness",
                    "context": "Assumes causal relationship in consciousness studies without mechanism",
                    "difficulty": 4,
                    "rarity": "legendary",
                    "meta_level": 3,
                    "fundamental": False,
                    "notes": "Philosophical post hoc - correlation in emergence doesn't prove causation"
                }
            ]
        }
    ]
    
    # Create the enhanced examples
    for fallacy_data in enhanced_examples:
        fallacy_name = fallacy_data["fallacy"]
        if fallacy_name in fallacies:
            fallacy_card = fallacies[fallacy_name]
            
            for example_data in fallacy_data["examples"]:
                example = FallacyExample(
                    fallacy_id=fallacy_card.id,
                    example_text=example_data["text"],
                    context_summary=example_data["context"],
                    difficulty_level=example_data["difficulty"],
                    example_rarity=example_data["rarity"],
                    meta_level_appropriateness=example_data["meta_level"],
                    is_fundamental=example_data["fundamental"],
                    educational_notes=example_data["notes"],
                    source_type="constructed",
                    source_quality="excellent"
                )
                session.add(example)
    
    await session.commit()
    return len(enhanced_examples)


async def seed_enhanced_fallacy_cards(session: AsyncSession):
    """Seed fallacy cards with enhanced meta-level design."""
    
    # Enhanced fallacy cards with better meta-level progression
    fallacies = [
        # Meta Level 0 - Fundamental logical errors (Common)
        ("Strawman", "Logical", "Misrepresenting someone's argument to make it easier to attack", "common", 0),
        ("Ad Hominem", "Logical", "Attacking the person making an argument rather than the argument itself", "common", 0),
        ("False Dilemma", "Logical", "Presenting only two options when more possibilities exist", "common", 0),
        
        # Meta Level 1 - Intermediate reasoning errors (Rare)
        ("Appeal to Ignorance", "Logical", "Claiming something is true because it hasn't been proven false", "rare", 1),
        ("Slippery Slope", "Causal", "Arguing that one event will inevitably lead to a chain of negative consequences", "rare", 1),
        ("Circular Reasoning", "Logical", "Using the conclusion as evidence for the premise", "rare", 1),
        
        # Meta Level 2 - Advanced contextual fallacies (Epic)
        ("Red Herring", "Distraction", "Introducing irrelevant information to divert attention from the real issue", "epic", 2),
        ("Appeal to Authority", "Authority", "Accepting a claim because an authority figure endorses it", "epic", 2),
        ("Bandwagon", "Social", "Arguing that something is true because many people believe it", "epic", 2),
        
        # Meta Level 3+ - Expert philosophical fallacies (Legendary)
        ("Post Hoc", "Causal", "Assuming causation from temporal sequence alone", "legendary", 3),
        ("No True Scotsman", "Definitional", "Dismissing counterexamples by redefining terms", "legendary", 3),
        ("Composition Fallacy", "Logical", "Assuming what's true for parts is true for the whole", "legendary", 4),
    ]
    
    created_fallacies = {}
    
    for name, category, description, rarity, meta_level in fallacies:
        fallacy = FallacyCard(
            id=str(uuid.uuid4()),
            name=name,
            category=category,
            description=description,
            rarity=rarity,
            unlock_meta_level=meta_level,
        )
        session.add(fallacy)
        created_fallacies[name] = fallacy
    
    await session.commit()
    
    # Add the enhanced examples
    examples_count = await seed_enhanced_fallacy_examples(session, created_fallacies)
    
    return created_fallacies, examples_count


async def seed_enhanced_data(session: AsyncSession):
    """Seed all enhanced game data with proper sophistication levels."""
    fallacies, examples_count = await seed_enhanced_fallacy_cards(session)
    return {
        "fallacies": fallacies, 
        "examples_count": examples_count,
        "design_notes": "Examples now properly categorized by difficulty, rarity, and meta-level appropriateness"
    }
