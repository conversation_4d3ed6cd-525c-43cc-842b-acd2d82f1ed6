"""Claim card models for Discord bot game mechanics."""

from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Inte<PERSON>, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base
from .fallacy_card import claim_fallacy_link, user_claim_reference


class ClaimCard(Base):
    """
    Claim cards for the Discord bot game.
    Users can create, collect, and debate claims with meta-level progression.
    """

    __tablename__ = "claim_cards"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    title: Mapped[str] = mapped_column(
        String(255), unique=True, nullable=False
    )
    description: Mapped[str] = mapped_column(Text, nullable=False)

    # Creator information
    creator_discord_id: Mapped[str] = mapped_column(
        ForeignKey("discord_users.discord_id"), nullable=False
    )

    # Meta-level progression system (0-5 as discussed)
    meta_level_0: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Domain/Topic
    meta_level_1: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Category
    meta_level_2: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Subcategory
    meta_level_3: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Specific aspect
    meta_level_4: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Nuance
    meta_level_5: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )  # Philosophical depth and the Spinozian-Field

    # Game mechanics
    difficulty_level: Mapped[int] = mapped_column(default=1)  # 1-5 difficulty
    unlock_meta_level: Mapped[int] = mapped_column(
        default=0
    )  # Meta level required to unlock
    is_public: Mapped[bool] = mapped_column(
        default=True
    )  # Public or private claim

    # Context linking
    source_transcript_id: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    source_timestamp: Mapped[Optional[float]] = mapped_column(nullable=True)

    # Relationships
    creator: Mapped["DiscordUser"] = relationship(
        "DiscordUser", back_populates="created_claims"
    )
    fallacies: Mapped[List["FallacyCard"]] = relationship(
        "FallacyCard", secondary=claim_fallacy_link, back_populates="claims"
    )
    users: Mapped[List["DiscordUser"]] = relationship(
        "DiscordUser",
        secondary=user_claim_reference,
        back_populates="collected_claims",
    )


class ClaimEvidence(Base):
    """
    Evidence supporting or refuting claims.
    Part of the advanced meta-level system.
    """

    __tablename__ = "claim_evidence"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    claim_id: Mapped[int] = mapped_column(
        ForeignKey("claim_cards.id"), nullable=False
    )
    evidence_text: Mapped[str] = mapped_column(Text, nullable=False)
    evidence_type: Mapped[str] = mapped_column(
        String(50), nullable=False
    )  # support, refute, context

    # Source information
    source_transcript_id: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    source_timestamp: Mapped[Optional[float]] = mapped_column(nullable=True)
    source_url: Mapped[Optional[str]] = mapped_column(
        String(500), nullable=True
    )

    # Meta information
    strength_rating: Mapped[int] = mapped_column(
        default=1
    )  # 1-5 strength of evidence
    added_by_discord_id: Mapped[str] = mapped_column(
        ForeignKey("discord_users.discord_id"), nullable=False
    )

    # Relationships
    claim: Mapped["ClaimCard"] = relationship("ClaimCard")
    added_by: Mapped["DiscordUser"] = relationship("DiscordUser")
