"""Fallacy card models for Discord bot game mechanics."""

import uuid
from typing import List, Optional

from sqlalchemy import <PERSON>umn, ForeignKey, String, Table, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base

# Association table for many-to-many relationship between claims and fallacies
claim_fallacy_link = Table(
    "claim_fallacy_link",
    Base.metadata,
    Column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    Column("fallacy_id", ForeignKey("fallacy_cards.id"), primary_key=True),
)

# Association table for user-claim references in Discord context
user_claim_reference = Table(
    "user_claim_reference",
    Base.metadata,
    Column(
        "discord_user_id",
        ForeignKey("discord_users.discord_id"),
        primary_key=True,
    ),
    Column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    Column(
        "context_transcript_id", String(50), nullable=True
    ),  # Optional transcript context
)


class FallacyCard(Base):
    """
    Fallacy cards for wisdom-centered opinion assessment.
    Fallacies are misalignment indicators that help users recognize patterns
    in their thinking and realign toward greater wisdom.
    """

    __tablename__ = "fallacy_cards"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    category: Mapped[str] = mapped_column(String(50), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)

    # Wisdom-centered properties
    complexity_level: Mapped[int] = mapped_column(
        default=1
    )  # 1-5 sophistication level for understanding
    unlock_meta_level: Mapped[int] = mapped_column(
        default=0
    )  # 0-5 meta-level required to unlock this fallacy
    wisdom_insight: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # What wisdom this fallacy points toward

    # Relationships
    examples: Mapped[List["FallacyExample"]] = relationship(
        back_populates="fallacy", cascade="all, delete-orphan"
    )
    claims: Mapped[List["ClaimCard"]] = relationship(
        "ClaimCard", secondary=claim_fallacy_link, back_populates="fallacies"
    )


class FallacyExample(Base):
    """
    Examples of fallacies in context with wisdom-centered learning.
    Examples help users recognize misalignment patterns and understand
    the wisdom that emerges from recognizing these patterns.
    """

    __tablename__ = "fallacy_examples"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    fallacy_id: Mapped[str] = mapped_column(
        ForeignKey("fallacy_cards.id"), nullable=False
    )
    example_text: Mapped[str] = mapped_column(Text, nullable=False)
    context_summary: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Wisdom-centered properties
    complexity_level: Mapped[int] = mapped_column(
        default=1
    )  # 1-5 sophistication level for understanding
    meta_level_appropriateness: Mapped[int] = mapped_column(
        default=0
    )  # 0-5, which meta-levels this example is appropriate for

    # Educational value
    is_fundamental: Mapped[bool] = mapped_column(
        default=False
    )  # True for core, defining examples of the fallacy
    educational_notes: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # Why this example demonstrates the fallacy
    wisdom_insight: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # What wisdom this example points toward

    # Source and context
    source_type: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )  # "transcript", "academic", "media", "constructed"
    source_quality: Mapped[str] = mapped_column(
        String(20), default="good"
    )  # "poor", "good", "excellent"

    # Optional link to transcript context
    transcript_id: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    timestamp: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )  # Timestamp in transcript

    # Relationships
    fallacy: Mapped["FallacyCard"] = relationship(back_populates="examples")
