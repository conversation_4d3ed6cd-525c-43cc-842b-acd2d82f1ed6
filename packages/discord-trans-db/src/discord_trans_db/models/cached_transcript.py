"""Cached transcript model for Discord bot."""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func
from sqlalchemy.types import <PERSON><PERSON><PERSON>

from .base import Base


class CachedTranscript(Base):
    """
    Cached transcript for Discord bot quick access.
    Independent from main app's Transcript model.
    """

    __tablename__ = "cached_transcripts"

    # Primary key
    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )

    # Video identification
    video_id: Mapped[str] = mapped_column(
        String(20), unique=True, nullable=False
    )
    title: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Transcript data - stored as JSON for Discord bot compatibility
    transcript_json: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False
    )

    # Pre-computed slurp text for fast searching
    slurp_text: Mapped[str] = mapped_column(Text, nullable=False)

    # Metadata
    duration_seconds: Mapped[Optional[float]] = mapped_column(nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)

    # Cache metadata
    cache_source: Mapped[str] = mapped_column(
        String(50),
        default="discord_bot",  # 'discord_bot', 'manual', 'api', etc.
    )
    cache_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default={}
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now(), onupdate=func.now()
    )
    last_accessed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )

    def __repr__(self) -> str:
        return f"<CachedTranscript(video_id='{self.video_id}', title='{self.title}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "video_id": self.video_id,
            "title": self.title,
            "transcript_json": self.transcript_json,
            "slurp_text": self.slurp_text,
            "duration_seconds": self.duration_seconds,
            "language": self.language,
            "cache_source": self.cache_source,
            "cache_metadata": self.cache_metadata,
            "created_at": self.created_at.isoformat()
            if self.created_at
            else None,
            "updated_at": self.updated_at.isoformat()
            if self.updated_at
            else None,
            "last_accessed_at": self.last_accessed_at.isoformat()
            if self.last_accessed_at
            else None,
        }

    def mark_accessed(self) -> None:
        """Mark this transcript as recently accessed."""
        self.last_accessed_at = func.now()
