"""Search history model for Discord bot."""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from sqlalchemy import DateTime, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from sqlalchemy.types import <PERSON><PERSON><PERSON>

from .base import Base


class SearchHistory(Base):
    """
    Search history for Discord bot users.
    Tracks what users search for to improve bot intelligence.
    """

    __tablename__ = "search_history"

    # Primary key
    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )

    # User who performed the search
    discord_user_id: Mapped[str] = mapped_column(
        String(20), ForeignKey("discord_users.discord_id"), nullable=False
    )

    # Search details
    search_term: Mapped[str] = mapped_column(String(500), nullable=False)
    transcript_id: Mapped[Optional[str]] = mapped_column(
        String(20), nullable=True
    )

    # Search results metadata
    results_found: Mapped[Optional[int]] = mapped_column(nullable=True)
    search_method: Mapped[str] = mapped_column(
        String(50),
        default="phrase_search",  # 'phrase_search', 'regex', 'semantic', etc.
    )

    # Context and metadata
    discord_channel_id: Mapped[Optional[str]] = mapped_column(
        String(20), nullable=True
    )
    discord_guild_id: Mapped[Optional[str]] = mapped_column(
        String(20), nullable=True
    )

    # Search metadata (timing, performance, etc.)
    search_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default={}
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now()
    )

    # Relationships
    user: Mapped["DiscordUser"] = relationship(
        "DiscordUser", back_populates="search_history"
    )

    def __repr__(self) -> str:
        return f"<SearchHistory(user='{self.discord_user_id}', term='{self.search_term[:50]}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "discord_user_id": self.discord_user_id,
            "search_term": self.search_term,
            "transcript_id": self.transcript_id,
            "results_found": self.results_found,
            "search_method": self.search_method,
            "discord_channel_id": self.discord_channel_id,
            "discord_guild_id": self.discord_guild_id,
            "search_metadata": self.search_metadata,
            "created_at": self.created_at.isoformat()
            if self.created_at
            else None,
        }
