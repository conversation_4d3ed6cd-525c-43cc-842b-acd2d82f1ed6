"""Discord user model for bot-specific user data."""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import DateTime, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func
from sqlalchemy.types import <PERSON><PERSON><PERSON>

from .base import Base


class DiscordUser(Base):
    """
    Discord user model for bot-specific profile and conversation data.
    This is independent from the main app's User model.
    """

    __tablename__ = "discord_users"

    # Primary key is Discord ID (not UUID)
    discord_id: Mapped[str] = mapped_column(String(20), primary_key=True)

    # Basic Discord info
    discord_username: Mapped[str] = mapped_column(String(100), nullable=False)
    display_name: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )

    # Bot conversation profile
    bot_profile_name: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    temperament: Mapped[str] = mapped_column(String(50), default="unknown")
    humor: Mapped[str] = mapped_column(String(50), default="unknown")
    trust_level: Mapped[str] = mapped_column(String(50), default="unknown")
    communication_style: Mapped[str] = mapped_column(
        String(50), default="neutral"
    )
    sentiment_tendency: Mapped[str] = mapped_column(
        String(50), default="neutral"
    )

    # Preferences and topics
    preferred_topics: Mapped[Optional[List[str]]] = mapped_column(
        JSON, nullable=True
    )

    # Optional link to main app user (for future integration)
    knowtrail_user_id: Mapped[Optional[str]] = mapped_column(
        String(36), nullable=True
    )

    # Bot-specific metadata
    bot_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default={}
    )

    # Wisdom progression (user understanding, not collection)
    skill_level: Mapped[int] = mapped_column(
        default=1
    )  # User skill level (1-10)
    wisdom_level: Mapped[int] = mapped_column(
        default=1
    )  # Wisdom understanding level (1-5)
    experience_points: Mapped[int] = mapped_column(
        default=0
    )  # XP for progression
    total_opinions_assessed: Mapped[int] = mapped_column(
        default=0
    )  # Wisdom stats
    total_reflections_made: Mapped[int] = mapped_column(
        default=0
    )  # Wisdom stats
    wisdom_score: Mapped[int] = mapped_column(
        default=0
    )  # Overall wisdom score

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=func.now(), onupdate=func.now()
    )

    # Relationships
    search_history: Mapped[List["SearchHistory"]] = relationship(
        "SearchHistory", back_populates="user", cascade="all, delete-orphan"
    )
    created_claims: Mapped[List["ClaimCard"]] = relationship(
        "ClaimCard",
        back_populates="creator",
        foreign_keys="ClaimCard.creator_discord_id",
    )
    collected_claims: Mapped[List["ClaimCard"]] = relationship(
        "ClaimCard", secondary="user_claim_reference", back_populates="users"
    )
    opinion_assessments: Mapped[List["OpinionAssessment"]] = relationship(
        "OpinionAssessment",
        back_populates="user",
        cascade="all, delete-orphan",
    )

    def __repr__(self) -> str:
        return f"<DiscordUser(discord_id='{self.discord_id}', username='{self.discord_username}')>"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "discord_id": self.discord_id,
            "discord_username": self.discord_username,
            "display_name": self.display_name,
            "bot_profile_name": self.bot_profile_name,
            "skill_level": self.skill_level,
            "temperament": self.temperament,
            "humor": self.humor,
            "trust_level": self.trust_level,
            "communication_style": self.communication_style,
            "sentiment_tendency": self.sentiment_tendency,
            "preferred_topics": self.preferred_topics,
            "knowtrail_user_id": self.knowtrail_user_id,
            "bot_metadata": self.bot_metadata,
            "created_at": self.created_at.isoformat()
            if self.created_at
            else None,
            "updated_at": self.updated_at.isoformat()
            if self.updated_at
            else None,
        }
