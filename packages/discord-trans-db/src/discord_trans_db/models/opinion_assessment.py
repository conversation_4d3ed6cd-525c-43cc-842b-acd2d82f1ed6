"""Opinion assessment models for wisdom-centered Discord bot."""

import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy import DateTime, Foreign<PERSON>ey, String, Text, <PERSON>ole<PERSON>, Float, Integer, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class OpinionAssessment(Base):
    """
    Opinion assessments for wisdom-centered evaluation system.
    Users express opinions on ideas, things, and people, and the system
    helps identify logical patterns and wisdom potential without judgment.
    """

    __tablename__ = "opinion_assessments"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    
    # User who submitted the opinion
    discord_user_id: Mapped[str] = mapped_column(
        ForeignKey("discord_users.discord_id"), nullable=False
    )
    
    # Opinion content
    opinion_text: Mapped[str] = mapped_column(Text, nullable=False)
    target_type: Mapped[str] = mapped_column(
        String(50), nullable=False
    )  # "idea", "thing", "person", "concept"
    target_reference: Mapped[Optional[str]] = mapped_column(
        String(200), nullable=True
    )  # Optional reference to what the opinion is about
    
    # Assessment results (populated by wisdom engine)
    logical_patterns: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True
    )  # Detected logical patterns (fallacies, strengths)
    wisdom_indicators: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True
    )  # Positive patterns that indicate wisdom
    complexity_level: Mapped[int] = mapped_column(
        default=1
    )  # 1-5 sophistication level of the opinion
    
    # Merit-based evaluation (NOT popularity-based)
    logical_coherence_score: Mapped[float] = mapped_column(
        default=0.0
    )  # 0.0-1.0 logical consistency
    evidence_quality_score: Mapped[float] = mapped_column(
        default=0.0
    )  # 0.0-1.0 quality of supporting evidence
    wisdom_potential_score: Mapped[float] = mapped_column(
        default=0.0
    )  # 0.0-1.0 potential for wisdom development
    
    # Community context (deliberately low weight)
    community_resonance: Mapped[float] = mapped_column(
        default=0.0
    )  # 0.0-1.0 how much community finds it valuable
    intersubjective_weight: Mapped[float] = mapped_column(
        default=0.5
    )  # 0.0-1.0 balance between individual and community perspective
    
    # Wisdom progression
    contributes_to_wisdom: Mapped[bool] = mapped_column(default=False)
    wisdom_card_potential: Mapped[bool] = mapped_column(default=False)
    progression_notes: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # Notes on how this opinion could evolve
    
    # Metadata
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    
    # Context linking
    source_transcript_id: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True
    )
    source_timestamp: Mapped[Optional[float]] = mapped_column(nullable=True)
    
    # Relationships
    user: Mapped["DiscordUser"] = relationship(
        "DiscordUser", back_populates="opinion_assessments"
    )
    reflections: Mapped[List["OpinionReflection"]] = relationship(
        back_populates="opinion", cascade="all, delete-orphan"
    )


class OpinionReflection(Base):
    """
    User reflections on their own opinions.
    Encourages self-examination and pattern recognition.
    """

    __tablename__ = "opinion_reflections"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    
    opinion_id: Mapped[str] = mapped_column(
        ForeignKey("opinion_assessments.id"), nullable=False
    )
    
    # Reflection content
    reflection_text: Mapped[str] = mapped_column(Text, nullable=False)
    reflection_type: Mapped[str] = mapped_column(
        String(50), default="general"
    )  # "general", "pattern_recognition", "wisdom_insight"
    
    # Self-assessment
    recognized_patterns: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True
    )  # Patterns the user recognized
    wisdom_gained: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True
    )  # What wisdom the user gained from reflection
    
    # Metadata
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow
    )
    
    # Relationships
    opinion: Mapped["OpinionAssessment"] = relationship(
        back_populates="reflections"
    )


class WisdomTrail(Base):
    """
    Tracks the evolution of opinions into wisdom.
    Shows how understanding deepens over time.
    """

    __tablename__ = "wisdom_trails"

    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid.uuid4())
    )
    
    # Trail metadata
    topic: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Wisdom progression
    starting_opinion_id: Mapped[Optional[str]] = mapped_column(
        ForeignKey("opinion_assessments.id"), nullable=True
    )
    current_wisdom_level: Mapped[int] = mapped_column(
        default=1
    )  # 1-5 current understanding level
    
    # Community aspects (merit-focused)
    is_public: Mapped[bool] = mapped_column(default=False)
    mentor_insights: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True
    )  # Insights from scientific mentors (Feynman, etc.)
    
    # Metadata
    created_at: Mapped[datetime] = mapped_column(
        DateTime, default=datetime.utcnow
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True
    )
    
    # Relationships
    starting_opinion: Mapped[Optional["OpinionAssessment"]] = relationship(
        "OpinionAssessment", foreign_keys=[starting_opinion_id]
    )
