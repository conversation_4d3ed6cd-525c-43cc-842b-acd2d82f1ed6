"""Database service for Discord bot operations."""

import json
import logging
import random
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from ..models import (
    CachedTranscript,
    DiscordUser,
    FallacyCard,
    OpinionAssessment,
    OpinionReflection,
    SearchHistory,
    WisdomTrail,
)

logger = logging.getLogger(__name__)


class DiscordDBService:
    """Service class for Discord bot database operations."""

    def __init__(self, db: AsyncSession):
        """Initialize the service with a database session."""
        self.db = db

    async def get_or_create_discord_user(
        self, discord_id: str, discord_username: str
    ) -> DiscordUser:
        """Get or create a Discord user."""
        result = await self.db.execute(
            select(DiscordUser).filter(DiscordUser.discord_id == discord_id)
        )
        user = result.scalars().first()

        if user:
            # Update username if it changed
            if user.discord_username != discord_username:
                user.discord_username = discord_username
                await self.db.commit()
            return user

        # Create new user
        user = DiscordUser(
            discord_id=discord_id, discord_username=discord_username
        )
        self.db.add(user)

        try:
            await self.db.commit()
            await self.db.refresh(user)
            logger.info(
                f"Created new Discord user: {discord_username} ({discord_id})"
            )
        except IntegrityError:
            await self.db.rollback()
            # Handle race condition - try to get existing user
            result = await self.db.execute(
                select(DiscordUser).filter(
                    DiscordUser.discord_id == discord_id
                )
            )
            user = result.scalars().first()
            if not user:
                raise RuntimeError(
                    f"Failed to get or create Discord user: {discord_id}"
                )

        return user

    async def update_discord_user_profile(
        self, discord_id: str, profile_updates: Dict[str, Any]
    ) -> Optional[DiscordUser]:
        """Update Discord user profile data."""
        result = await self.db.execute(
            select(DiscordUser).filter(DiscordUser.discord_id == discord_id)
        )
        user = result.scalars().first()

        if not user:
            logger.warning(f"Discord user not found for ID: {discord_id}")
            return None

        # Update profile fields
        for field, value in profile_updates.items():
            if hasattr(user, field):
                setattr(user, field, value)
            else:
                # Store in bot_metadata if field doesn't exist
                if user.bot_metadata is None:
                    user.bot_metadata = {}
                current_metadata = dict(
                    user.bot_metadata
                )  # Copy existing metadata
                current_metadata[field] = value
                user.bot_metadata = current_metadata  # Reassign to trigger SQLAlchemy change detection

        await self.db.commit()
        await self.db.refresh(user)
        logger.info(f"Updated Discord user profile for {discord_id}")
        return user

    async def cache_transcript(
        self,
        video_id: str,
        transcript_data: Dict[str, Any],
        title: Optional[str] = None,
    ) -> CachedTranscript:
        """Cache a transcript for quick Discord bot access."""
        # Check if already cached
        result = await self.db.execute(
            select(CachedTranscript).filter(
                CachedTranscript.video_id == video_id
            )
        )
        existing = result.scalars().first()

        if existing:
            # Update existing
            existing.transcript_json = transcript_data
            existing.title = title or existing.title
            existing.updated_at = datetime.utcnow()

            # Update slurp text if transcript data changed
            if "transcript" in transcript_data:
                try:
                    from binary_trans.trans_prepper import TranscriptPrepper

                    tm = TranscriptPrepper(transcript_data["transcript"])
                    existing.slurp_text = tm.slurp
                except ImportError:
                    # Fallback for testing - simple text concatenation
                    existing.slurp_text = " ".join(
                        [
                            item.get("text", "")
                            for item in transcript_data["transcript"]
                        ]
                    )

            await self.db.commit()
            await self.db.refresh(existing)
            logger.info(f"Updated cached transcript for video_id: {video_id}")
            return existing

        # Create new cached transcript
        slurp_text = ""
        if "transcript" in transcript_data:
            try:
                from binary_trans.trans_prepper import TranscriptPrepper

                tm = TranscriptPrepper(transcript_data["transcript"])
                slurp_text = tm.slurp
            except ImportError:
                # Fallback for testing - simple text concatenation
                slurp_text = " ".join(
                    [
                        item.get("text", "")
                        for item in transcript_data["transcript"]
                    ]
                )
            except Exception as e:
                logger.warning(
                    f"Failed to generate slurp text for {video_id}: {e}"
                )
                slurp_text = str(transcript_data)  # Fallback

        cached_transcript = CachedTranscript(
            id=str(uuid.uuid4()),
            video_id=video_id,
            title=title,
            transcript_json=transcript_data,
            slurp_text=slurp_text,
            cache_source="discord_bot",
        )

        self.db.add(cached_transcript)
        await self.db.commit()
        await self.db.refresh(cached_transcript)
        logger.info(f"Cached new transcript for video_id: {video_id}")
        return cached_transcript

    async def get_cached_transcript(
        self, video_id: str
    ) -> Optional[CachedTranscript]:
        """Get a cached transcript by video ID."""
        result = await self.db.execute(
            select(CachedTranscript).filter(
                CachedTranscript.video_id == video_id
            )
        )
        transcript = result.scalars().first()

        if transcript:
            # Mark as accessed
            transcript.last_accessed_at = datetime.utcnow()
            await self.db.commit()

        return transcript

    async def list_cached_transcripts(self) -> List[CachedTranscript]:
        """List all cached transcripts."""
        result = await self.db.execute(
            select(CachedTranscript).order_by(
                CachedTranscript.created_at.desc()
            )
        )
        return list(result.scalars().all())

    async def log_search(
        self,
        discord_id: str,
        search_term: str,
        transcript_id: Optional[str] = None,
        results_found: Optional[int] = None,
        channel_id: Optional[str] = None,
        guild_id: Optional[str] = None,
    ) -> SearchHistory:
        """Log a search performed by a Discord user."""
        search_record = SearchHistory(
            id=str(uuid.uuid4()),
            discord_user_id=discord_id,
            search_term=search_term,
            transcript_id=transcript_id,
            results_found=results_found,
            discord_channel_id=channel_id,
            discord_guild_id=guild_id,
        )

        self.db.add(search_record)
        await self.db.commit()
        await self.db.refresh(search_record)
        logger.info(
            f"Logged search for Discord user {discord_id}: {search_term}"
        )
        return search_record

    async def get_user_search_history(
        self, discord_id: str, limit: int = 50
    ) -> List[SearchHistory]:
        """Get search history for a Discord user."""
        result = await self.db.execute(
            select(SearchHistory)
            .filter(SearchHistory.discord_user_id == discord_id)
            .order_by(SearchHistory.created_at.desc())
            .limit(limit)
        )
        return list(result.scalars().all())

    async def migrate_from_sqlite(
        self,
        sqlite_users: List[Dict[str, Any]],
        sqlite_transcripts: List[Dict[str, Any]],
    ) -> Tuple[int, int]:
        """
        Migrate data from SQLite to PostgreSQL.
        Returns (users_migrated, transcripts_migrated).
        """
        users_migrated = 0
        transcripts_migrated = 0

        # Migrate users
        for user_data in sqlite_users:
            try:
                discord_id = user_data.get("discord_id")
                discord_username = user_data.get("discord_username")

                if not discord_id or not discord_username:
                    continue

                user = await self.get_or_create_discord_user(
                    discord_id, discord_username
                )

                # Update profile data
                profile_updates = {
                    k: v
                    for k, v in user_data.items()
                    if k not in ["discord_id", "discord_username"]
                    and v is not None
                }

                if profile_updates:
                    await self.update_discord_user_profile(
                        discord_id, profile_updates
                    )

                users_migrated += 1

            except Exception as e:
                logger.error(
                    f"Failed to migrate user {user_data.get('discord_id', 'unknown')}: {e}"
                )

        # Migrate transcripts
        for transcript_data in sqlite_transcripts:
            try:
                video_id = transcript_data.get("video_id")
                transcript_json = transcript_data.get("transcript_json")

                if not video_id or not transcript_json:
                    continue

                # Parse JSON if it's a string
                if isinstance(transcript_json, str):
                    transcript_json = json.loads(transcript_json)

                await self.cache_transcript(video_id, transcript_json)
                transcripts_migrated += 1

            except Exception as e:
                logger.error(
                    f"Failed to migrate transcript {transcript_data.get('video_id', 'unknown')}: {e}"
                )

        logger.info(
            f"Migration completed: {users_migrated} users, {transcripts_migrated} transcripts"
        )
        return users_migrated, transcripts_migrated

    # === Fallacy Card Game Methods ===

    async def get_random_fallacy(
        self, user_unlock_level: int = 0
    ) -> Optional[FallacyCard]:
        """Get a random fallacy card based on user's unlock level."""
        result = await self.db.execute(
            select(FallacyCard)
            .filter(FallacyCard.unlock_meta_level <= user_unlock_level)
            .order_by(func.random())
            .limit(1)
        )
        return result.scalars().first()

    async def get_fallacy_by_name(self, name: str) -> Optional[FallacyCard]:
        """Get a fallacy card by name (case-insensitive)."""
        result = await self.db.execute(
            select(FallacyCard).filter(FallacyCard.name.ilike(f"%{name}%"))
        )
        return result.scalars().first()

    async def get_fallacies_by_complexity(
        self, complexity_level: int
    ) -> List[FallacyCard]:
        """Get all fallacy cards of a specific complexity level."""
        result = await self.db.execute(
            select(FallacyCard).filter(
                FallacyCard.complexity_level == complexity_level
            )
        )
        return list(result.scalars().all())

    async def get_all_fallacies(self) -> List[FallacyCard]:
        """Get all fallacy cards."""
        result = await self.db.execute(select(FallacyCard))
        return list(result.scalars().all())

    async def add_fallacy_to_user_collection(
        self, discord_id: str, fallacy_id: str
    ) -> bool:
        """Add a fallacy to user's collection. Returns True if added, False if already collected."""
        # Check if user already has this fallacy
        user = await self.get_or_create_discord_user(discord_id, "")

        # Track collected fallacies in the user's bot_metadata JSON
        if not user.bot_metadata:
            user.bot_metadata = {}

        collected_fallacies = user.bot_metadata.get("collected_fallacies", [])

        if fallacy_id in collected_fallacies:
            return False  # Already collected

        collected_fallacies.append(fallacy_id)
        user.bot_metadata["collected_fallacies"] = collected_fallacies

        await self.db.commit()
        return True

    async def get_user_collected_fallacies(
        self, discord_id: str
    ) -> List[FallacyCard]:
        """Get all fallacies collected by a user."""
        user = await self.get_or_create_discord_user(discord_id, "")

        if not user.bot_metadata:
            return []

        collected_fallacy_ids = user.bot_metadata.get(
            "collected_fallacies", []
        )

        if not collected_fallacy_ids:
            return []

        result = await self.db.execute(
            select(FallacyCard).filter(
                FallacyCard.id.in_(collected_fallacy_ids)
            )
        )
        return list(result.scalars().all())

    async def award_experience(
        self, discord_id: str, xp_amount: int
    ) -> DiscordUser:
        """Award experience points to a user and handle level progression."""
        user = await self.get_or_create_discord_user(discord_id, "")

        # Add XP
        user.experience_points += xp_amount

        # Calculate new skill level (every 100 XP = 1 level, max 10)
        new_skill_level = min(10, max(1, user.experience_points // 100 + 1))

        # Update skill level if it increased
        if new_skill_level > user.skill_level:
            user.skill_level = new_skill_level
            # Unlock level follows skill level for now
            user.unlock_level = new_skill_level

        await self.db.commit()
        await self.db.refresh(user)
        return user

    async def get_user_progress_stats(self, discord_id: str) -> Dict[str, Any]:
        """Get comprehensive progress statistics for a user."""
        user = await self.get_or_create_discord_user(discord_id, "")
        collected_fallacies = await self.get_user_collected_fallacies(
            discord_id
        )

        # Get total fallacies available at user's level
        result = await self.db.execute(
            select(func.count(FallacyCard.id)).filter(
                FallacyCard.unlock_meta_level <= user.wisdom_level
            )
        )
        available_count = result.scalar() or 0

        # Get complexity breakdown of collected fallacies
        complexity_counts = {}
        for fallacy in collected_fallacies:
            complexity = fallacy.complexity_level
            complexity_counts[f"Level {complexity}"] = (
                complexity_counts.get(f"Level {complexity}", 0) + 1
            )

        return {
            "discord_id": discord_id,
            "discord_username": user.discord_username,
            "skill_level": user.skill_level,
            "wisdom_level": user.wisdom_level,
            "experience_points": user.experience_points,
            "xp_to_next_level": 100 - (user.experience_points % 100),
            "collected_count": len(collected_fallacies),
            "available_count": available_count,
            "collection_percentage": (
                len(collected_fallacies) / max(1, available_count)
            )
            * 100,
            "complexity_breakdown": complexity_counts,
        }

    # === Opinion Assessment Methods ===

    async def create_opinion_assessment(
        self,
        discord_user_id: str,
        opinion_text: str,
        target_type: str,
        target_reference: Optional[str] = None,
        analysis_results: Optional[Dict[str, Any]] = None,
    ) -> OpinionAssessment:
        """Create a new opinion assessment."""
        opinion = OpinionAssessment(
            discord_user_id=discord_user_id,
            opinion_text=opinion_text,
            target_type=target_type,
            target_reference=target_reference,
        )

        # Apply analysis results if provided
        if analysis_results:
            opinion.logical_patterns = analysis_results.get("logical_patterns")
            opinion.wisdom_indicators = analysis_results.get(
                "wisdom_indicators"
            )
            opinion.complexity_level = analysis_results.get(
                "complexity_level", 1
            )
            opinion.logical_coherence_score = analysis_results.get(
                "logical_coherence_score", 0.0
            )
            opinion.evidence_quality_score = analysis_results.get(
                "evidence_quality_score", 0.0
            )
            opinion.wisdom_potential_score = analysis_results.get(
                "wisdom_potential_score", 0.0
            )
            opinion.contributes_to_wisdom = analysis_results.get(
                "contributes_to_wisdom", False
            )
            opinion.wisdom_card_potential = analysis_results.get(
                "wisdom_card_potential", False
            )
            opinion.progression_notes = analysis_results.get(
                "progression_notes"
            )

        self.db.add(opinion)
        await self.db.commit()
        await self.db.refresh(opinion)
        logger.info(f"Created opinion assessment for user {discord_user_id}")
        return opinion

    async def get_opinion_assessment_by_id(
        self, opinion_id: str
    ) -> Optional[OpinionAssessment]:
        """Get an opinion assessment by ID."""
        result = await self.db.execute(
            select(OpinionAssessment).filter(
                OpinionAssessment.id.like(f"{opinion_id}%")
            )
        )
        return result.scalars().first()

    async def get_user_opinion_assessments(
        self, discord_user_id: str, limit: int = 10
    ) -> List[OpinionAssessment]:
        """Get opinion assessments for a user."""
        result = await self.db.execute(
            select(OpinionAssessment)
            .filter(OpinionAssessment.discord_user_id == discord_user_id)
            .order_by(OpinionAssessment.created_at.desc())
            .limit(limit)
        )
        return list(result.scalars().all())

    async def increment_user_opinion_stats(
        self, discord_user_id: str
    ) -> DiscordUser:
        """Increment user's opinion assessment statistics."""
        user = await self.get_or_create_discord_user(discord_user_id, "")
        user.total_opinions_assessed += 1
        user.experience_points += 15  # XP for opinion assessment

        # Update wisdom level based on assessments
        if user.total_opinions_assessed >= 10 and user.wisdom_level < 2:
            user.wisdom_level = 2
        elif user.total_opinions_assessed >= 25 and user.wisdom_level < 3:
            user.wisdom_level = 3
        elif user.total_opinions_assessed >= 50 and user.wisdom_level < 4:
            user.wisdom_level = 4
        elif user.total_opinions_assessed >= 100 and user.wisdom_level < 5:
            user.wisdom_level = 5

        await self.db.commit()
        await self.db.refresh(user)
        return user
