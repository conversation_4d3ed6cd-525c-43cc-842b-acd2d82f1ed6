FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the package
COPY . .

# Install uv and hatch
RUN pip install uv hatch

# Run discord-trans-db specific tests
RUN echo "Testing discord-trans-db package..." && hatch run test

# Success message
RUN echo "🎉 Discord Trans DB tests passed in Docker!"
