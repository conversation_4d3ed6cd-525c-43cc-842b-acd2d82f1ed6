# SQLAlchemy 2.0 Schema Design Documentation

## Overview

This document outlines the schema design patterns and best practices for the Discord bot database using SQLAlchemy 2.0, specifically covering the distinction between `mapped_column()` and `Column()` usage.

## SQLAlchemy 2.0 Column Declaration Patterns

### Core Principle

**Use the right tool for the right context:**
- **`mapped_column()`** for ORM-mapped model classes
- **`Column()`** for association tables and raw table declarations

### Why This Distinction Matters

- `mapped_column()` is designed for **ORM-mapped attributes** within declarative classes that use `Mapped[]` type annotations
- `Column()` is used for **raw Table objects** that are not mapped to Python classes
- Association tables are typically **not mapped to classes** - they exist purely for many-to-many relationships

## Usage Patterns

### ✅ ORM Model Classes - Use `mapped_column()`

```python
from sqlalchemy.orm import DeclarativeBase, mapped_column, Mapped
from sqlalchemy import String, Text, ForeignKey
from typing import List, Optional

class Base(DeclarativeBase):
    pass

class FallacyCard(Base):
    """ORM-mapped class uses mapped_column() for type safety."""
    __tablename__ = "fallacy_cards"
    
    # ✅ Use mapped_column() with Mapped[] annotations
    id: Mapped[str] = mapped_column(String(36), primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    rarity: Mapped[str] = mapped_column(String(20), default="common")
    unlock_meta_level: Mapped[int] = mapped_column(default=0)
    
    # Relationships
    examples: Mapped[List["FallacyExample"]] = relationship(
        back_populates="fallacy", cascade="all, delete-orphan"
    )
```

### ✅ Association Tables - Use `Column()`

```python
from sqlalchemy import Table, Column, ForeignKey, String

# ✅ Association tables use Column() - not mapped to a class
claim_fallacy_link = Table(
    "claim_fallacy_link",
    Base.metadata,
    Column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    Column("fallacy_id", ForeignKey("fallacy_cards.id"), primary_key=True),
)

user_claim_reference = Table(
    "user_claim_reference", 
    Base.metadata,
    Column("discord_user_id", ForeignKey("discord_users.discord_id"), primary_key=True),
    Column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),
    Column("context_transcript_id", String(50), nullable=True),
)
```

## Benefits of This Approach

### `mapped_column()` Advantages
- **Type Safety**: Full integration with Python type checkers (MyPy, Pyright)
- **Modern Syntax**: Designed for SQLAlchemy 2.0+ declarative mapping
- **IDE Support**: Better autocomplete and error detection
- **Future-Proof**: Forward-compatible with SQLAlchemy evolution

### `Column()` for Association Tables
- **Simplicity**: Raw table declarations don't need ORM complexity
- **Performance**: Direct table-to-table relationships without class overhead
- **Clarity**: Explicit about being pure relational constructs

## Schema Design Reference

### Quick Decision Matrix

| Use Case | Use `mapped_column()` | Use `Column()` | Example |
|----------|----------------------|----------------|---------|
| ORM Model Class | ✅ Yes | ⚠️ Avoid | `class User(Base):` |
| Association Table | ❌ No | ✅ Yes | `user_group = Table(...)` |
| Raw Table (no ORM) | ❌ No | ✅ Yes | `metadata_table = Table(...)` |

### Common Mistakes to Avoid

```python
# ❌ WRONG: Using mapped_column() in association tables
claim_fallacy_link = Table(
    "claim_fallacy_link",
    Base.metadata,
    mapped_column("claim_id", ForeignKey("claim_cards.id"), primary_key=True),  # ❌ Error!
    mapped_column("fallacy_id", ForeignKey("fallacy_cards.id"), primary_key=True),  # ❌ Error!
)

# ❌ WRONG: Using Column() in ORM classes  
class FallacyCard(Base):
    __tablename__ = "fallacy_cards"
    id = Column(String(36), primary_key=True)  # ❌ Missing type safety
```
