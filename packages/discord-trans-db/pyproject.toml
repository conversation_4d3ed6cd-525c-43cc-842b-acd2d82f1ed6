[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "discord-trans-db"
version = "0.1.0"
description = "Database models and services for Discord transcript bot"
authors = [
    {name = "Ideas", email = "<EMAIL>"},
]
dependencies = [
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.0",
]
requires-python = ">=3.11"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "aiosqlite>=0.19.0",  # For async SQLite testing
]

[tool.hatch.build.targets.wheel]
packages = ["src/discord_trans_db"]

[tool.hatch.envs.default]
dependencies = [
    "pytest",
    "pytest-asyncio",
    "pytest-cov",
]

[tool.hatch.envs.default.scripts]
test = "pytest {args:tests}"
test-cov = "pytest --cov=discord_trans_db {args:tests}"
cov-report = [
    "pytest --cov=discord_trans_db --cov-report=term-missing {args:tests}",
    "pytest --cov=discord_trans_db --cov-report=html {args:tests}",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_mode = "auto"
