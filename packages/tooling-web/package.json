{"name": "tooling-web", "version": "0.2.0", "private": false, "type": "module", "scripts": {"dev": "cd ./../tooling/ && tsx ./build-docker-compose.ts web dev && exit 0", "build": "cd ./../tooling/ && tsx ./build-docker-compose.ts web prod", "build-docker": "../../packages/tooling/run-docker-build.sh web prod", "dev-docker": "../../packages/tooling/run-docker-build.sh web dev"}, "dependencies": {"tsx": "^4.19.4"}}