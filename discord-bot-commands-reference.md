# Discord Bot Fallacy Card Game - Command Reference

## 🎴 **Fallacy Card Game Commands**

### `!fallacy`
**Discover a random fallacy and earn XP**
- Awards 10 XP for discovery
- Shows fallacy based on your unlock level
- Displays rarity, category, and description
- Suggests using `!collect` to add to collection

**Example:**
```
!fallacy
```

### `!fallacy list`
**Show all available fallacies organized by rarity**
- Displays fallacies you can access at your current level
- Organized by Common, Rare, Epic, Legendary
- Shows unlock requirements

**Example:**
```
!fallacy list
```

### `!fallacy <name>`
**Get detailed information about a specific fallacy**
- Case-insensitive partial matching
- Shows full description and examples
- Displays rarity and unlock level

**Examples:**
```
!fallacy strawman
!fallacy ad hominem
!fallacy appeal to authority
```

### `!collect <fallacy>`
**Add a discovered fallacy to your personal collection**
- Awards 25 XP for collection
- Prevents duplicate collection
- Validates unlock level access
- Updates your collection statistics

**Examples:**
```
!collect strawman
!collect Ad Hominem
!collect slippery slope
```

### `!mycards`
**View your collected fallacy cards**
- Shows all fallacies in your collection
- Organized by rarity with counts
- Displays collection progress
- Links to `!progress` for detailed stats

**Example:**
```
!mycards
```

### `!progress`
**Show comprehensive progression statistics**
- Current skill level (1-10) and XP
- Unlock level and access permissions
- Collection completion percentage
- Rarity breakdown of collected cards
- XP needed for next level
- Visual progress bar

**Example:**
```
!progress
```

## 🎮 **Game Mechanics**

### **Experience Points (XP)**
- **Discovery**: +10 XP for using `!fallacy`
- **Collection**: +25 XP for using `!collect`
- **Level Up**: Every 100 XP = +1 skill level

### **Skill Levels & Unlocks**
- **Level 1-2**: Access to Common fallacies
- **Level 3-4**: Unlock Rare fallacies
- **Level 5-6**: Unlock Epic fallacies
- **Level 7-10**: Unlock Legendary fallacies

### **Rarity System**
- **⚪ Common**: Basic logical fallacies (3 cards)
- **🔵 Rare**: Intermediate fallacies (3 cards)
- **🟣 Epic**: Advanced fallacies (3 cards)
- **🟡 Legendary**: Expert-level fallacies (3 cards)

### **Collection Features**
- **Duplicate Protection**: Can't collect the same fallacy twice
- **Progress Tracking**: Visual completion percentage
- **Rarity Statistics**: Breakdown by rarity type
- **Level Gating**: Higher rarities require progression

## 🔍 **Other Bot Commands**

### **Search & Transcripts**
- `!search <transcript_id> <phrase>` - Search transcripts
- `!list_transcripts` - Show cached transcripts
- `!cache_transcript <id>` - Cache a transcript

### **User Profiles**
- `!vp <@user>` - View user profile (ASCII)
- `!vudex <@user>` - View user profile (Rich embed)
- `!set_profile <@user> <updates>` - Update profile

### **General**
- `!sass` - Show this help menu
- `!ping` - Check bot responsiveness

## 🚀 **Getting Started**

1. **Start Your Journey**: Use `!fallacy` to discover your first fallacy
2. **Build Your Collection**: Use `!collect <fallacy>` to add it to your collection
3. **Track Progress**: Use `!progress` to see your advancement
4. **Explore More**: Use `!fallacy list` to see what's available
5. **Level Up**: Keep discovering and collecting to unlock rare fallacies!

## 💡 **Tips**

- **Case Insensitive**: All fallacy names work with any capitalization
- **Partial Matching**: You can use partial names (e.g., "straw" for "Strawman")
- **Level Strategy**: Focus on collecting all available fallacies before leveling up
- **XP Optimization**: Discovery gives 10 XP, collection gives 25 XP
- **Help Available**: Use `!sass` anytime to see the full command list

---

**Happy fallacy hunting! 🎯**
