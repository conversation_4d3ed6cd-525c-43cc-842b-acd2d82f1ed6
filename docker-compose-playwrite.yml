
services:
  playwright-dev:
    container_name: playwright
    build:
      context: apps/playwright_docker # Corrected context path to be relative to repo root
      dockerfile: Dockerfile
    depends_on:
      - web-prod
    networks:
      - app_network
    environment:
      NEXT_APP_URL: http://web-prod:3000

  playwright-prod:
    container_name: playwright
    build:
      context: apps/playwright_docker # Corrected context path to be relative to repo root
      dockerfile: Dockerfile
    depends_on:
      - web-prod
    networks:
      - app_network
    environment:
      NEXT_APP_URL: http://web-prod:3000

networks:
  app_network:
    external: true
