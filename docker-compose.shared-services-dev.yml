services:
  postgres-dev:
    container_name: postgres-dev
    image: postgres:15-alpine
    ports:
      - "5442:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: knowledgetrails
    volumes:
      - postgres_data_driz_tut:/var/lib/postgresql/data
    networks:
      - shared_network

  redis-dev:
    container_name: redis-dev
    image: redis/redis-stack:latest
    mem_limit: 100m
    cpu_shares: 102
    restart: unless-stopped
    env_file:
      - apps/trans_api/.env-dev-redis # Assuming this env file is still relevant for dev redis
    volumes:
      - redis_data_volume:/data
    networks:
      - shared_network
    ports:
      - 6779:6379
      - 8011:8001
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 1s
      timeout: 3s
      retries: 5

  celery-dev:
    container_name: celery_worker-dev
    mem_limit: 200m
    cpu_shares: 102
    build:
      context: .
      dockerfile: apps/celery_worker/Dockerfile-celery
      target: dev-runner
    env_file:
      - apps/celery_worker/.env-dev-celery
    restart: unless-stopped
    networks:
      - shared_network
    depends_on:
      - redis-dev

volumes:
  postgres_data_driz_tut:
  redis_data_volume:

networks:
  shared_network:
    external: true
