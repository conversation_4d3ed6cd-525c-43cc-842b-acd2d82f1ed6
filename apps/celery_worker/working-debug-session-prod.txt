```
first command
docker exec -it celery_worker-prod bash

app@30a6ec3df2c9:/app$ 

next command
CELERY_BROKER_URL=redis://angels:ThisIsABetterOne@redis-dev:6379 CELERY_RESULT_BACKEND=redis://angels:ThisIsABetterOne@redis-dev:6379 python -m celery -A apps.celery_worker.worker worker --loglevel=info

this worked after i removed the 'apps.celery_worker.''
and changed the redis-dev to redis-prod for production redis :)

This is the output :)

worker: Warm shutdown (MainProcess)
app@30a6ec3df2c9:/app$ CELERY_BROKER_URL=redis://angels:ThisIsABetterOne@redis-prod:6379 CELERY_RESULT_BACKEND=redis://angels:ThisIsABetterOne@redis-prod:6379 python -m celery -A worker worker --logle
vel=info
 
 -------------- celery@30a6ec3df2c9 v5.5.3 (immunity)
--- ***** ----- 
-- ******* ---- Linux-5.15.153.1-microsoft-standard-WSL2-x86_64-with-glibc2.36 2025-06-08 18:39:36
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         worker:0x7fbfe7b08a10
- ** ---------- .> transport:   redis://angels:**@redis-prod:6379//
- ** ---------- .> results:     redis://angels:**@redis-prod:6379/
- *** --- * --- .> concurrency: 12 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . shared_tasks.test_tasks.add
  . shared_tasks.test_tasks.multiply

[2025-06-08 18:39:36,954: INFO/MainProcess] Connected to redis://angels:**@redis-prod:6379//
[2025-06-08 18:39:36,959: INFO/MainProcess] mingle: searching for neighbors
[2025-06-08 18:39:37,967: INFO/MainProcess] mingle: all alone
[2025-06-08 18:39:37,976: INFO/MainProcess] celery@30a6ec3df2c9 ready.