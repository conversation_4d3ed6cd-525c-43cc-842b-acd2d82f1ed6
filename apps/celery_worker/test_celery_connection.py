import os
import sys
from dotenv import load_dotenv
from celery import Celery

# Load environment variables from .env-prod-celery
dotenv_path = os.path.join(os.path.dirname(__file__), '.env-prod-celery')
load_dotenv(dotenv_path)
# Configure Celery app using environment variables
REDIS_USERNAME = os.environ.get("REDIS_USERNAME")
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD")
REDIS_HOST = os.environ.get("REDIS_HOST")
REDIS_PORT = os.environ.get("REDIS_PORT")
REDIS_DB_BROKER = os.environ.get("REDIS_DB", "0") # Assuming REDIS_DB is for broker
REDIS_DB_BACKEND = os.environ.get("REDIS_DB", "1") # Assuming REDIS_DB is for backend, or use a different env var

REDIS_HOST = "localhost"
broker_url = f"redis://{REDIS_USERNAME}:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_BROKER}"
result_backend = f"redis://{REDIS_USERNAME}:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB_BACKEND}"

app = Celery(
    "test_worker",
    broker=broker_url,
    backend=result_backend
)

# Import the add task
# Assuming shared_tasks is in the Python path or accessible
# For this test, we'll define a simple task directly if import issues arise
# Or, we can try to import it if the path is set up correctly.
# Given the project structure, it's likely shared_tasks is accessible.
sys.path.append("/home/<USER>/repo/knowtrails")
from packages.shared_tasks.test_tasks import add

if __name__ == "__main__":
    print(f"Attempting to send 'add' task to broker: {broker_url}")
    result = add.delay(5, 3)
    print(f"Task ID: {result.id}")
    print("Waiting for task to complete...")
    try:
        # Get the result, with a timeout
        task_result = result.get(timeout=10)
        print(f"Task completed. Result: {task_result}")
    except Exception as e:
        print(f"Task failed or timed out: {e}")
