from packages.test_share.celery_app import shared_celery_app

# Use the shared Celery app instance
app = shared_celery_app

app.autodiscover_tasks([
    "apps.trans_api.trans_tasks.video_trans",
    "apps.trans_api.trans_tasks.data_pipeline_tasks", # New pipeline tasks
    "apps.trans_api.trans_tasks.test_tasks",
    "apps.trans_api.trans_tasks.example_tasks", # Include our new example tasks
    "packages.test_share.tasks", # Include tasks from the shared package
])
