# ===========================================================================
# First Stage, build the application in the /app director
FROM ghcr.io/astral-sh/uv:bookworm-slim AS builder-base
ARG DEBUG=false
# ===========================================================================
FROM builder-base AS dev-builder

ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy
ENV UV_PYTHON_INSTALL_DIR=/python
ENV UV_PYTHON_PREFERENCE=only-managed

RUN mkdir -p /app
WORKDIR /app/apps/celery_worker

RUN --mount=type=cache,target=/root/.cache uv python install 3.12

# Copy relevant parts of the monorepo for dependency resolution and editable installs
COPY apps /app/apps
COPY packages /app/packages

COPY apps/celery_worker/uv.lock /app/uv.lock
COPY apps/celery_worker/pyproject.toml /app/pyproject.toml

# Install dependencies and editable packages for development
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --dev && \
    uv pip install -e /app/packages/test_share && \
    uv pip install -e /app/apps/trans_api && \
    uv pip install -e /app/apps/celery_worker

# ===========================================================================
FROM builder-base AS prod-builder 

ENV UV_COMPILE_BYTECODE=1 
ENV UV_LINK_MODE=copy
ENV UV_PYTHON_INSTALL_DIR=/python
ENV UV_PYTHON_PREFERENCE=only-managed
WORKDIR /app

RUN --mount=type=cache,target=/root/.cache/ uv python install 3.12

# Copy relevant parts of the monorepo for dependency resolution
COPY apps /app/apps
COPY packages /app/packages

# Install dependencies for production (non-editable)
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev && \
    uv pip install /app/packages/test_share && \
    uv pip install /app/apps/trans_api && \
    uv pip install /app/apps/celery_worker


FROM debian:stable-slim AS base-runner
# ===========================================================================

FROM base-runner AS dev-runner
ARG DEBUG=true # Set DEBUG to true by default for dev

RUN groupadd -g 1000 app && \
    useradd -u 1000 -g app -m app

COPY --from=dev-builder --chown=python:python /python /python
COPY --from=dev-builder --chown=app:app /app /app

ENV PYTHONPATH=/app
ENV PATH="/app/.venv/bin:$PATH"

WORKDIR /app
USER app

# Command to start the Celery worker
CMD ["python", "-m", "celery", "-A", "worker", "worker", "--loglevel=info"]

# ===========================================================================
# prod Second Stage, use a final image without uv
# PROD is called BUILD in turborepo so, keep for now


FROM base-runner AS prod-runner

RUN groupadd -g 1000 app && \
    useradd -u 1000 -g app -m app

COPY --from=prod-builder --chown=python:python /python /python
COPY --from=prod-builder --chown=app:app /app /app

ENV PYTHONPATH=/app
ENV PATH="/app/.venv/bin:$PATH"

WORKDIR /app/apps/celery_worker
USER app

# Command to start the Celery worker
CMD ["python", "-m", "celery", "-A", "worker", "worker", "--loglevel=info"]
