import os
# import time
from util.logs import GlobalLogger, logging
from dotenv import load_dotenv
load_dotenv(".env")
# https://docs.python.org/3/howto/logging-cookbook.html#custom-handling-of-levels
global_log = GlobalLogger()
jobs_log = global_log.get_logger('jobs', logging.INFO)

recordings_dir = os.environ.get("RECORDINGS_DIR") or ""
recordings_file = os.environ.get("RECORDINGS_FILE") or ""
# include_private = os.environ.get("INCLUDE_PRIVATE") or ""
include_private = os.environ.get("INCLUDE_PRIVATE") or ""

recordings_dir_prv = os.environ.get("RECORDINGS_DIR_PRIVATE") or ""
recordings_file_prv = os.environ.get("RECORDINGS_FILE_PRIVATE") or ""

# Maybe replace with a worker queue style from a job list in RECORDINGS_FILE.
def setup_results_folders():
   
    # environment variables
    """
    Set up the directory structure for recordings and create a job file for job control.

    This function takes no arguments and returns a tuple of two elements, (job_file, job_file_prv).
    job_file is the path to the job file and job_file_prv is the path to the private job file.
    If INCLUDE_PRIVATE is False, job_file_prv is None.

    The function will create the directories if they do not exist.
    """
   
    job_file = setup_recordings_structure(recordings_dir, recordings_file)
    
    job_file_prv = None
    if include_private:
        job_file_prv = setup_recordings_structure(recordings_dir_prv, recordings_file_prv)
    
    # The `job_file, job_file_prv` variables are being set by the `setup_results_folders()`
    # function. This function is responsible for setting up the directory structure for
    # recordings and creating a job file for job control.
    return (job_file, job_file_prv) 
    

def setup_recordings_structure(recs_dir, recs_file):
    """
    Set up the directory structure for recordings and create a job file for job control.
    
    A default recordings directory is created if it does not exist. A default recordings
    file is created if the specified file does not exist. The file contains a description
    of how to use the file, example lines for transcribing mp3 files, and a few example
    recordings.
    
    Returns:
        str: The full path to the job file.
    """
    if not os.path.exists(recs_dir):
        os.mkdir(recs_dir)
        jobs_log.info(f"Default recordings directory {recs_dir} created.")
        
    if not os.path.exists(f"{recs_dir}/transcribed"):
        os.mkdir(f"{recs_dir}/transcribed")
        jobs_log.info(f"Default transcribed directory {recs_dir}/transcribed created.")
        
    if not os.path.exists(f"{recs_dir}/analyzed"):
        os.mkdir(f"{recs_dir}/analyzed")
        jobs_log.info(f"Default analyzed directory {recs_dir}/analyzed created.")
        
    
    # create recordings file for job control if missing
    job_file = f"{recs_dir}/{recs_file}"    
    if not os.path.exists(job_file):
        create_recordings_file_example(job_file, recs_dir)
        
    return job_file


def create_recordings_file_example(job_file_wd, recs_dir):
    """
    Create a default recordings file for job control if it does not exist.
    
    A default recordings file is created if the specified file does not exist.
    The file contains a description of how to use the file, example lines
    for transcribing mp3 files, and a few example recordings.
    """
    with open(job_file_wd, "w") as f:
        f.write("# -----------------------------------------\n")
        f.write("# Usage: mp3s_to_transcribe.txt\n")
        f.write("# MP3 files to transcribe, one per line\n")            
        f.write("# Lines starting with '#' are ignored\n")  
        f.write("# Example mp3s without extension:\n")
        f.write("# -----------------------------------------\n")
        f.write("# Desiderata\n")
    jobs_log.info(f"Default recordings file {job_file_wd} created.")
    
    # copy images/Desiderata.mp3 to recordings/Desiderata.mp3 if is is not in 
    os.system(f"cp media/Desiderata.mp3 {recs_dir}")


def job_files():
    (job_file, job_file_prv) = setup_results_folders()

    jobs = {}       
    if job_file is not None:
        # get files and call transcript with file output dir
        for job in read_job_file(job_file):
            jobs[job] = recordings_dir
        
         
    if job_file_prv is not None:
        for job in read_job_file(job_file_prv):
            jobs[job] = recordings_dir_prv 

    return jobs

def read_job_file(job_file):
    with open(job_file, "r") as file:
        in_files = [
            line.strip() for line in file.readlines() if not line.startswith("#")
        ]
        return in_files
