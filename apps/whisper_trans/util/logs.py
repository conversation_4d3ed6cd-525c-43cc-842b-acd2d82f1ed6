# Usage:
# global_log = GlobalLogger()
# logger1 = global_log.get_logger('script1', logging.INFO)
# logger2 = global_log.get_logger('script2', logging.DEBUG)
#
# logger1.info('This is a message from script1')
# logger2.debug('This is a debug message from script2')

import logging
import os

from dotenv import load_dotenv
load_dotenv(".env")
log_file = os.environ.get("LOG_FILE") or ""

class GlobalLogger:
    _instance = None

    def __new__(cls):
        # Singleton
        if cls._instance is None:
            cls._instance = super(GlobalLogger, cls).__new__(cls)
            cls._instance.init_logger()
        return cls._instance

    def init_logger(self):
        """
        Initialize the global logger.
        
        This creates a root logger and adds a file handler to it. 
        The root logger is used as the parent logger for all other 
        loggers created by this class.
        """
        
        self.logger = logging.getLogger('global_log')
        self.logger.setLevel(logging.DEBUG)
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def get_logger(self, script_name, level):
        """
        Get a logger for a given script with the given logging level.

        This logger will be a child of the global logger and will
        inherit its handlers.

        Parameters
        ----------
        script_name : str
            The name of the script for which the logger is being created.
        level : int
            The logging level of the logger.

        Returns
        -------
        logger : logging.Logger
            A logger for the given script with the given logging level.
        """
        logger = logging.getLogger(f'{script_name}')
        logger.setLevel(level)
        logger.addHandler(self.logger.handlers[0])  # add the global logger's handler
        return logger


# logging.basicConfig(
#     filename=log_file,
#     # format=f"{extra} %(asctime)s %(name)-12s %(levelname)-8s %(relativeCreated)8d %(threadName)s %(message)s",
#     format=f"%(asctime)s %(name)-12s %(levelname)-8s %(relativeCreated)8d %(threadName)s %(message)s",
#     datefmt="%m-%d %H:%M:%S",
#     level=log_level,
# )
# return


# Logging levels
# CRITICAL: Final = 50
# FATAL: Final = CRITICAL
# ERROR: Final = 40
# WARNING: Final = 30
# WARN: Final = WARNING
# INFO: Final = 20
# DEBUG: Final = 10
# NOTSET: Final = 0 