# https://www.meta.ai/c/07d6424c-5115-4347-a5f3-3deadeb65e09
# TODO(nu): Not TESTED
from pydub import AudioSegment
from pydub.silence import split_on_silence
import json

# Open the audio file
audio = AudioSegment.from_file("audio_file.mp3")

# Split the audio into segments separated by silence (greater than 5 seconds)
segments = split_on_silence(audio, min_silence_len=5000)

# Initialize an empty list to store the segment information
segment_info = []

# Iterate over the segments
for i, segment in enumerate(segments):
    # Add a silent segment to mark the annotation (e.g., 1 second at the beginning)
    annotation = AudioSegment.silent(duration=1000)
    segment = annotation + segment
    
    # Get the segment start time in milliseconds
    start_time = i * 1000  # Assuming 1 second per segment (adjust if needed)
    
    # Add the segment information to the list
    segment_info.append({
        "segment_id": i,
        "start_time": start_time,
        "end_time": start_time + len(segment)
    })

# Concatenate the annotated segments
annotated_audio = sum(segments)

# Export the annotated audio file
annotated_audio.export("annotated_audio_file.mp3", format="mp3")

# Save the segment information to a JSON file
with open("segment_info.json", "w") as f:
    json.dump(segment_info, f, indent=4)