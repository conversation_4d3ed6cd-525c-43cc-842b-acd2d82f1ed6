 # Whisper Trans

## Transcribing Audio
Used to prepare audio transcriptions for searching using the playknow search tool.

Read a list of audio file names from a job file mp3s_to_transcribe.txt
```
./whisper_trans/recordings/mp3s_to_transcribe.txt
```
Audio file to transcribe are placed in 
```
./whisper_trans/recordings/
```
/mp3s_to_transcribe.txt in the directory with the audio files 
- lines starting with '#' being ignored. 

Transcription results are saved in JSON format to the file  
```
./whisper_trans/recordings/<timestamp>_<original_filename>_trans.json. 
```

![Alt](media/Stained_Cat.webp)

in venv: installs for python ./transcribe.py to run
--------------
faster_whisper
python-dotenv