from dotenv import load_dotenv
import nltk
import time
from nltk.corpus import treebank
from util.logs import GlobalLogger, logging

def main():
    # download_nltk_resources()
    global_log = GlobalLogger()
    analyse_log = global_log.get_logger('analyse', logging.INFO)
    start_time = time.time()
    analyse_log.info("Starting NLTK sentiment analysis")
    analyse_a_thing()
    job_completion_time = time.time()
    analyse_log.debug("Whole time:", job_completion_time - start_time, "seconds")
    return


def analyse_a_thing():
    sentence = """At eight o'clock on Thursday morning
    ... <PERSON> didn't feel very good."""
    tokens = nltk.word_tokenize(sentence)
    ## tokens
    ## ['At', 'eight', "o'clock", 'on', 'Thursday', 'morning',
    ## 'Arthur', 'did', "n't", 'feel', 'very', 'good', '.']
    tagged = nltk.pos_tag(tokens)
    ## tagged[0:6]
    ## [('At', 'IN'), ('eight', 'CD'), ("o'clock", 'JJ'), ('on', 'IN'),
    ## ('Thursday', 'NNP'), ('morning', 'NN')]
    entities = nltk.chunk.ne_chunk(tagged)
    print(entities)
    # t = treebank.parsed_sents('wsj_0001.mrg')[0]
    # t.draw()
    return


def download_nltk_resources():     
    nltk.download('punkt_tab')
    nltk.download('averaged_perceptron_tagger_eng')
    nltk.download('maxent_ne_chunker_tab')
    nltk.download('words')
    return

if __name__ == "__main__":
    main()

# https://huggingface.co/datasets/google/fleurs
# https://www.google.com/search?q=fleurs+and+faster+whisper&rlz=1C1CHBF_enCA862CA862&oq=fleurs+and+faster+whisper&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIKCAEQABiABBiiBDIKCAIQABiABBiiBDIKCAMQABiABBiiBDIKCAQQABiABBiiBDIKCAUQABiABBiiBNIBCTExNDI0ajBqNKgCALACAA&sourceid=chrome&ie=UTF-8#fpstate=ive&vld=cid:e3d3ebce,vid:k6nIxWGdrS4,st:0
# https://kellylougheed.medium.com/coding-english-lit-natural-language-processing-in-python-ba8ebae4dde3