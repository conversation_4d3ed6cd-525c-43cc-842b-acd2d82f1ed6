import json
import time

from faster_whisper import WhisperModel
from util.logs import GlobalLogger, logging
from util.jobs import job_files

global_log = GlobalLogger()
trans_log = global_log.get_logger('transcribe', logging.DEBUG)

def transcribe_audio(in_file: str):
    # model_size = "tiny.en"
    """
    Transcribe an audio file using the Whisper ASR model.

    Args:
        in_file: path to the audio file to transcribe

    Returns:
        A tuple of two elements. (Iterable[Segment], TranscriptionInfo) The first element is a list of Segment objects,
        where each Segment contains the text of a segment of the audio file, as
        well as the start and end times of that segment. The second element is an
        Info object, which contains metadata about the audio file, such as the
        detected language and its probability.
    """
    model_size = "small"
    model = WhisperModel(model_size, device="cpu", compute_type="int8")
    # (segments, info) = model.transcribe(in_file, beam_size=5, word_timestamps=True)
    (segments, info) = model.transcribe(in_file, beam_size=5)
    trans_log.info(
        f"Detected language {info.language} with probability {info.language_probability}"
    )
    return (segments, info)


def main():
    """
    Main function to transcribe audio files listed in a job file.

    This function reads a list of audio file names from a specified job file, transcribes each file using
    the Whisper ASR model, and saves the transcription results in JSON format with a timestamp in the
    filename. Logging is used to track the process, including execution time and detected language.

    Steps:
    1. Reads the list of audio files from the job file, ignoring lines starting with '#'.
    2. For each audio file, constructs input and output file paths.
    3. Transcribes the audio, logs language detection details, and measures transcription time.
    4. Processes each segment of the transcription and saves the text, start time, and duration.
    5. Writes the transcription results to a JSON file.
    6. Logs the time taken to write the file.

    Raises:
        Exception: If an error occurs during transcription processing.
    """
    start_time = time.time()

    trans_log.info("Starting transcribe")
    files_to_transcribe = job_files()
    
    for job_file, recordings_dir in files_to_transcribe.items():
        
        timestamp_fn = int(time.time())
        full_in_file = f"./{recordings_dir}/{job_file}.mp3"
        out_dir = f"./{recordings_dir}/transcribed"
        out_file = f"{timestamp_fn}_{job_file}_trans.json"
        full_out_file = f"{out_dir}/{out_file}"

        # measure execution time
        (segments, info) = transcribe_audio(full_in_file)
        trans_log.debug(
            f"Detected language '{info.language}' with probability {info.language_probability}"
        )

        # sentences = nltk.sent_tokenize(segments.text)
        trans_time = time.time()
        trans_log.debug(f"Execution time for transcribing: {trans_time - start_time} seconds"
        )

        trans_lines = []
        try:
            for segment in segments:
                # duration = segment.end - segment.start
                entry_obj = {
                    "start": "{:.2f}".format(segment.start),  # segment.start,
                    "end": "{:.2f}".format(segment.end),
                    "text": segment.text,
                }
                trans_lines.append(entry_obj)
        except Exception as e:
            trans_log.error(f"Error: {e}")



        with open(full_out_file, "w") as fh:
            fh.write(f"{json.dumps(trans_lines)}")
            # print(f"{json.dumps(trans_lines)}", file=fh)

        write_end_time = time.time()
        trans_log.debug(f"write file time: {write_end_time - trans_time} seconds"
        )


    job_completion_time = time.time()
    trans_log.debug(f"Whole time: { job_completion_time - start_time} seconds")

if __name__ == "__main__":
    main()