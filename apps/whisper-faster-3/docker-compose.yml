version: "3.8"
services:
  llama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama:/root/.ollama
    restart: unless-stopped

  whisper:
    build:
      context: .
      dockerfile: faster-whisper.Dockerfile
    volumes:
      - ./audio:/app/audio
    entrypoint: ["faster-whisper", "--model", "tiny-int8", "/app/audio/sample.wav"]

  github-mcp:
    image: ghcr.io/github/github-mcp-server
    environment:
      - GITHUB_PERSONAL_ACCESS_TOKEN=your_token_here
      - GITHUB_TOOLSETS=all
    stdin_open: true
    tty: true

volumes:
  ollama:
