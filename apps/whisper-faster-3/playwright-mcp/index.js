const readline = require('readline');
const { chromium } = require('playwright');

const rl = readline.createInterface({ input: process.stdin, output: process.stdout });

(async () => {
  for await (const line of rl) {
    const msg = JSON.parse(line);
    const browser = await chromium.launch();
    const page = await browser.newPage();

    if (msg.tool_name === "navigate_and_screenshot") {
      await page.goto(msg.input.url);
      const screenshot = await page.screenshot({ path: "screenshot.png" });
      console.log(JSON.stringify({ result: "Screenshot saved", path: "screenshot.png" }));
    }

    await browser.close();
  }
})();
