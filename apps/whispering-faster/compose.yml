version: '3.8'

services:
  faster-whisper:
    image: lscr.io/linuxserver/faster-whisper:latest
    container_name: faster-whisper
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC
      - WHISPER_MODEL=tiny-int8
      - WHISPER_BEAM=1 #optional
      - WHISPER_LANG=en #optional
    volumes:
      - /path/to/faster-whisper/data:/config
    ports:
      - 10300:10300
    restart: unless-stopped
    
# Ref notes:
# https://docs.linuxserver.io/images/docker-faster-whisper/#docker-compose-recommended-click-here-for-more-info
#
# cli command
#
# docker run -d \
#   --name=faster-whisper \
#   -e PUID=1000 \
#   -e PGID=1000 \
#   -e TZ=Etc/UTC \
#   -e WHISPER_MODEL=tiny-int8 \
#   -e WHISPER_BEAM=1 `#optional` \
#   -e WHISPER_LANG=en `#optional` \
#   -p 10300:10300 \
#   -v /path/to/faster-whisper/data:/config \
#   --restart unless-stopped \
#   lscr.io/linuxserver/faster-whisper:latest

# Ref 2: 
# - https://github.com/SYSTRAN/faster-whisper/blob/master/docker/Dockerfile
# - https://github.com/SYSTRAN/faster-whisper

# Small model on CPU
# Implementation	                Precision	Beam size	Time	RAM Usage
#   openai/whisper	              fp32	5	6m58s	2335MB
#   whisper.cpp	                  fp32	5	2m05s	1049MB
#   whisper.cpp (OpenVINO)	      fp32	5	1m45s	1642MB
#   faster-whisper	              fp32	5	2m37s	2257MB
#   faster-whisper (batch_size=8)	fp32	5	1m06s	4230MB
#   faster-whisper	              int8	5	1m42s	1477MB
#   faster-whisper (batch_size=8)	int8	5	51s	3608MB
#
#   Executed with 8 threads on an Intel Core i7-12700K.