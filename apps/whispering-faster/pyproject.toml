[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "whispering-faster"
dynamic = ["version"]
description = ''
readme = "README.md"
requires-python = ">=3.8"
license = "MIT"
keywords = []
authors = [
  { name = "Ideas", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
dependencies = []

[project.urls]
Documentation = "https://github.com/Ideas/whispering-faster#readme"
Issues = "https://github.com/Ideas/whispering-faster/issues"
Source = "https://github.com/Ideas/whispering-faster"

[tool.hatch.version]
path = "src/whispering_faster/__about__.py"

[tool.hatch.envs.types]
extra-dependencies = [
  "mypy>=1.0.0",
]
[tool.hatch.envs.types.scripts]
check = "mypy --install-types --non-interactive {args:src/whispering_faster tests}"

[tool.coverage.run]
source_pkgs = ["whispering_faster", "tests"]
branch = true
parallel = true
omit = [
  "src/whispering_faster/__about__.py",
]

[tool.coverage.paths]
whispering_faster = ["src/whispering_faster", "*/whispering-faster/src/whispering_faster"]
tests = ["tests", "*/whispering-faster/tests"]

[tool.coverage.report]
exclude_lines = [
  "no cov",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
]
