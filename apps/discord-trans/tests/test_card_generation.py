#!/usr/bin/env python3
"""
Test script to demonstrate card generation without Discord SSE connection.
This shows that the card generation system works independently.
"""

import sys
import os

# Add the src directory to the path so we can import discord_trans modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from discord_trans.user_card.generate_card import (
    generate_user_card_ascii,
    generate_user_card_embed,
    update_user_profile,
    load_user_data
)

def test_card_generation_without_discord():
    """Test that card generation works without Discord SSE connection."""
    
    print("🧪 Testing card generation without Discord SSE...")
    
    # Test Discord ID (fake but valid format)
    test_discord_id = "123456789012345678"
    test_username = "TestUser"
    
    print(f"\n1. Testing with Discord ID: {test_discord_id}")
    
    # Test 1: Load user data (should return None for non-existent user)
    print("\n📋 Testing load_user_data...")
    user_data = load_user_data(test_discord_id)
    print(f"   Initial user data: {user_data}")
    
    # Test 2: Create a user profile
    print("\n👤 Testing update_user_profile...")
    profile_updates = {
        "skill_level": "intermediate",
        "temperament": "curious",
        "humor": "witty",
        "trust_level": "high",
        "preferred_topics": ["AI", "philosophy", "science"],
        "communication_style": "direct",
        "sentiment_tendency": "optimistic"
    }
    
    success = update_user_profile(test_discord_id, test_username, profile_updates)
    print(f"   Profile creation success: {success}")
    
    # Test 3: Load user data again (should now exist)
    print("\n📋 Testing load_user_data after creation...")
    user_data = load_user_data(test_discord_id)
    print(f"   User data after creation: {user_data}")
    
    # Test 4: Generate ASCII card (no Discord connection needed)
    print("\n🎴 Testing generate_user_card_ascii...")
    try:
        ascii_card = generate_user_card_ascii(test_discord_id)
        print("   ASCII card generation: ✅ SUCCESS")
        print("   Card preview:")
        print(ascii_card)
    except Exception as e:
        print(f"   ASCII card generation: ❌ FAILED - {e}")
    
    # Test 5: Generate embed card (no Discord connection needed)
    print("\n🎨 Testing generate_user_card_embed...")
    try:
        # Note: This returns (file, embed) but we can test without Discord.Member object
        file_obj, embed_obj = generate_user_card_embed(test_discord_id, discord_user_obj=None)
        print("   Embed card generation: ✅ SUCCESS")
        print(f"   Embed title: {embed_obj.title}")
        print(f"   Embed color: {embed_obj.color}")
        print(f"   Number of fields: {len(embed_obj.fields)}")
        print(f"   File attachment: {file_obj.filename}")
    except Exception as e:
        print(f"   Embed card generation: ❌ FAILED - {e}")
    
    # Test 6: Update existing profile
    print("\n🔄 Testing profile updates...")
    update_changes = {
        "skill_level": "advanced",
        "preferred_topics": ["AI", "philosophy", "science", "consciousness"]
    }
    
    success = update_user_profile(test_discord_id, test_username, update_changes)
    print(f"   Profile update success: {success}")
    
    # Verify updates
    updated_data = load_user_data(test_discord_id)
    print(f"   Updated skill level: {updated_data.get('skill_level')}")
    print(f"   Updated topics: {updated_data.get('preferred_topics')}")

def test_card_with_nonexistent_user():
    """Test card generation with a user that doesn't exist."""
    
    print("\n\n🧪 Testing card generation with non-existent user...")
    
    fake_discord_id = "999999999999999999"
    
    print(f"\n1. Testing with non-existent Discord ID: {fake_discord_id}")
    
    # Test ASCII card with non-existent user
    print("\n🎴 Testing ASCII card for non-existent user...")
    try:
        ascii_card = generate_user_card_ascii(fake_discord_id)
        print("   ASCII card for non-existent user: ✅ SUCCESS (shows defaults)")
        print("   Card preview:")
        print(ascii_card)
    except Exception as e:
        print(f"   ASCII card for non-existent user: ❌ FAILED - {e}")
    
    # Test embed card with non-existent user (should create default profile)
    print("\n🎨 Testing embed card for non-existent user...")
    try:
        file_obj, embed_obj = generate_user_card_embed(fake_discord_id, discord_user_obj=None)
        print("   Embed card for non-existent user: ✅ SUCCESS (creates default)")
        print(f"   Embed title: {embed_obj.title}")
        
        # Verify that a default profile was created
        created_data = load_user_data(fake_discord_id)
        print(f"   Auto-created profile: {created_data is not None}")
        
    except Exception as e:
        print(f"   Embed card for non-existent user: ❌ FAILED - {e}")

def main():
    """Run all card generation tests."""
    
    print("=" * 60)
    print("🎴 DISCORD CARD GENERATION TESTS (NO SSE REQUIRED)")
    print("=" * 60)
    
    try:
        test_card_generation_without_discord()
        test_card_with_nonexistent_user()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 60)
        print("\n🎯 CONCLUSION:")
        print("   • Card generation works WITHOUT Discord SSE connection")
        print("   • Only requires Discord ID string (no Discord.Member object)")
        print("   • Can be easily mocked for unit testing")
        print("   • Database operations are independent of Discord API")
        print("   • System creates default profiles automatically")
        
    except Exception as e:
        print(f"\n❌ TEST SUITE FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
