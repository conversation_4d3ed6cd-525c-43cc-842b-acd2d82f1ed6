"""
Integration tests for the wisdom system with fresh database.
Tests the complete flow from opinion submission to wisdom assessment.
"""

import asyncio
import uuid

import pytest
from discord_trans.game_db_service import GameDBContext
from discord_trans.wisdom_engine import WisdomEngine


@pytest.fixture
def unique_user_id():
    """Generate a unique user ID for each test."""
    return f"test_user_{uuid.uuid4().hex[:8]}"


@pytest.mark.asyncio
class TestWisdomSystemIntegration:
    """Integration tests for the complete wisdom system."""

    async def test_complete_wisdom_flow(self, unique_user_id):
        """Test the complete flow from opinion to wisdom assessment."""
        # Initialize wisdom engine
        engine = WisdomEngine()

        # Test evidence-based opinion
        evidence_opinion = (
            "Research shows that climate change is caused by human activity."
        )
        analysis = engine.analyze_opinion(evidence_opinion, "idea")

        # Verify pattern detection
        patterns = [p["pattern"] for p in analysis["logical_patterns"]]
        assert "evidence_based" in patterns
        assert analysis["contributes_to_wisdom"] == True
        assert analysis["evidence_quality_score"] > 0.0

        # Test database operations
        async with GameDBContext() as db_service:
            # Create user
            user = await db_service.get_or_create_discord_user(
                unique_user_id, "TestUser"
            )
            assert hasattr(user, "wisdom_level")
            assert hasattr(user, "total_opinions_assessed")
            assert user.wisdom_level == 1  # Default wisdom level

            # Create opinion assessment
            opinion = await db_service.create_opinion_assessment(
                discord_user_id=unique_user_id,
                opinion_text=evidence_opinion,
                target_type="idea",
                analysis_results=analysis,
            )

            assert opinion.discord_user_id == unique_user_id
            assert opinion.contributes_to_wisdom == True
            assert opinion.logical_coherence_score > 0.0

            # Update user stats
            updated_user = await db_service.increment_user_opinion_stats(
                unique_user_id
            )
            assert updated_user.total_opinions_assessed == 1
            assert updated_user.experience_points > 0

    async def test_fallacy_detection_flow(self):
        """Test fallacy detection and scoring."""
        engine = WisdomEngine()

        # Test ad hominem detection
        fallacy_opinion = (
            "That person is an idiot, so their argument is wrong."
        )
        analysis = engine.analyze_opinion(fallacy_opinion, "person")

        patterns = [p["pattern"] for p in analysis["logical_patterns"]]
        assert "ad_hominem" in patterns
        assert (
            analysis["logical_coherence_score"] < 1.0
        )  # Should be reduced due to fallacy

    async def test_anti_celebrity_culture(self):
        """Test that evidence beats authority in scoring."""
        engine = WisdomEngine()

        authority_opinion = "A famous person said this, so it must be true."
        evidence_opinion = (
            "Multiple peer-reviewed studies demonstrate this conclusion."
        )

        auth_result = engine.analyze_opinion(authority_opinion, "idea")
        evid_result = engine.analyze_opinion(evidence_opinion, "idea")

        # Evidence should score higher than authority
        assert (
            evid_result["evidence_quality_score"]
            >= auth_result["evidence_quality_score"]
        )

    async def test_wisdom_progression(self, unique_user_id):
        """Test wisdom level progression based on assessments."""
        async with GameDBContext() as db_service:
            user = await db_service.get_or_create_discord_user(
                unique_user_id, "ProgressUser"
            )
            initial_level = user.wisdom_level

            # Simulate multiple opinion assessments
            for i in range(5):
                await db_service.increment_user_opinion_stats(unique_user_id)

            # Check if user progressed (though level 2 requires 10 assessments)
            updated_user = await db_service.get_or_create_discord_user(
                unique_user_id, "ProgressUser"
            )
            assert updated_user.total_opinions_assessed == 5
            assert updated_user.experience_points > 0


def test_wisdom_engine_standalone():
    """Test wisdom engine without database dependencies."""
    engine = WisdomEngine()

    # Test complexity assessment
    simple_opinion = "I like this."
    complex_opinion = "While evidence suggests merit, we must consider multiple perspectives and acknowledge uncertainty."

    simple_analysis = engine.analyze_opinion(simple_opinion, "thing")
    complex_analysis = engine.analyze_opinion(complex_opinion, "idea")

    assert (
        simple_analysis["complexity_level"]
        <= complex_analysis["complexity_level"]
    )

    # Test pattern detection
    nuanced_opinion = "While I believe this, I acknowledge there might be other valid perspectives."
    nuanced_analysis = engine.analyze_opinion(nuanced_opinion, "idea")

    patterns = [p["pattern"] for p in nuanced_analysis["logical_patterns"]]
    # Should detect acknowledgment of uncertainty or multiple perspectives
    wisdom_patterns = [
        "acknowledges_uncertainty",
        "considers_multiple_perspectives",
        "nuanced_thinking",
    ]
    assert any(pattern in patterns for pattern in wisdom_patterns)


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
