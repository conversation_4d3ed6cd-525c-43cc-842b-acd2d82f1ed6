"""Tests for opinion assessment system and wisdom engine."""

from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
import pytest_asyncio
from discord_trans.wisdom_engine import <PERSON>alPattern, WisdomEngine
from discord_trans_db.models import Discord<PERSON><PERSON>, OpinionAssessment


class TestWisdomEngine:
    """Test the wisdom engine pattern detection and analysis."""

    def setup_method(self):
        """Set up test fixtures."""
        self.wisdom_engine = WisdomEngine()

    def test_detect_evidence_based_pattern(self):
        """Test detection of evidence-based reasoning."""
        opinion = "Research shows that climate change is primarily caused by human activity."
        analysis = self.wisdom_engine.analyze_opinion(opinion, "idea")

        patterns = analysis["logical_patterns"]
        pattern_types = [p["pattern"] for p in patterns]

        assert "evidence_based" in pattern_types
        assert analysis["evidence_quality_score"] > 0.0

    def test_detect_uncertainty_acknowledgment(self):
        """Test detection of uncertainty acknowledgment."""
        opinion = "I think this might be true, but I'm not entirely certain about all the details."
        analysis = self.wisdom_engine.analyze_opinion(opinion, "idea")

        patterns = analysis["logical_patterns"]
        pattern_types = [p["pattern"] for p in patterns]

        assert "acknowledges_uncertainty" in pattern_types
        assert analysis["wisdom_potential_score"] > 0.0

    def test_detect_ad_hominem_fallacy(self):
        """Test detection of ad hominem attacks."""
        opinion = "That person is an idiot, so their argument is wrong."
        analysis = self.wisdom_engine.analyze_opinion(opinion, "person")

        patterns = analysis["logical_patterns"]
        pattern_types = [p["pattern"] for p in patterns]

        assert "ad_hominem" in pattern_types
        assert analysis["logical_coherence_score"] < 0.5

    def test_complexity_assessment(self):
        """Test complexity level assessment."""
        simple_opinion = "I like this."
        complex_opinion = "While the evidence suggests this approach has merit, we must consider multiple perspectives and acknowledge the uncertainty inherent in complex systems."

        simple_analysis = self.wisdom_engine.analyze_opinion(
            simple_opinion, "thing"
        )
        complex_analysis = self.wisdom_engine.analyze_opinion(
            complex_opinion, "idea"
        )

        assert (
            simple_analysis["complexity_level"]
            < complex_analysis["complexity_level"]
        )

    def test_wisdom_potential_assessment(self):
        """Test wisdom potential evaluation."""
        wisdom_opinion = "The evidence suggests this, but I acknowledge there might be other perspectives worth considering."
        fallacy_opinion = (
            "Everyone knows this is true, so anyone who disagrees is stupid."
        )

        wisdom_analysis = self.wisdom_engine.analyze_opinion(
            wisdom_opinion, "idea"
        )
        fallacy_analysis = self.wisdom_engine.analyze_opinion(
            fallacy_opinion, "idea"
        )

        assert wisdom_analysis["contributes_to_wisdom"] == True
        assert fallacy_analysis["contributes_to_wisdom"] == False

    def test_merit_over_popularity_scoring(self):
        """Test that scoring focuses on merit, not popularity."""
        evidence_opinion = (
            "Data from multiple studies indicates this conclusion."
        )
        popularity_opinion = "Everyone believes this, so it must be true."

        evidence_analysis = self.wisdom_engine.analyze_opinion(
            evidence_opinion, "idea"
        )
        popularity_analysis = self.wisdom_engine.analyze_opinion(
            popularity_opinion, "idea"
        )

        # Evidence-based should score higher than popularity-based
        assert (
            evidence_analysis["evidence_quality_score"]
            > popularity_analysis["evidence_quality_score"]
        )


@pytest.mark.asyncio
class TestOpinionAssessmentDatabase:
    """Test database operations for opinion assessments."""

    @pytest_asyncio.fixture
    async def db_service(self):
        """Mock database service."""
        mock_service = AsyncMock()
        return mock_service

    @pytest.fixture
    def sample_user(self):
        """Sample Discord user."""
        return DiscordUser(
            discord_id="123456789",
            discord_username="test_user",
            wisdom_level=1,
            total_opinions_assessed=0,
        )

    @pytest.fixture
    def sample_opinion_assessment(self):
        """Sample opinion assessment."""
        return OpinionAssessment(
            id="test-opinion-id",
            discord_user_id="123456789",
            opinion_text="This is a test opinion with evidence-based reasoning.",
            target_type="idea",
            logical_patterns=[
                {
                    "pattern": "evidence_based",
                    "confidence": 0.8,
                    "evidence_text": "evidence-based reasoning",
                    "explanation": "References evidence",
                }
            ],
            complexity_level=2,
            logical_coherence_score=0.7,
            evidence_quality_score=0.8,
            wisdom_potential_score=0.6,
            contributes_to_wisdom=True,
            wisdom_card_potential=False,
        )

    async def test_create_opinion_assessment(self, db_service, sample_user):
        """Test creating an opinion assessment."""
        analysis_results = {
            "logical_patterns": [
                {"pattern": "evidence_based", "confidence": 0.8}
            ],
            "complexity_level": 2,
            "logical_coherence_score": 0.7,
            "evidence_quality_score": 0.8,
            "wisdom_potential_score": 0.6,
            "contributes_to_wisdom": True,
            "wisdom_card_potential": False,
        }

        db_service.create_opinion_assessment.return_value = OpinionAssessment(
            id="test-id",
            discord_user_id="123456789",
            opinion_text="Test opinion",
            target_type="idea",
        )

        result = await db_service.create_opinion_assessment(
            discord_user_id="123456789",
            opinion_text="Test opinion",
            target_type="idea",
            analysis_results=analysis_results,
        )

        assert result.discord_user_id == "123456789"
        assert result.opinion_text == "Test opinion"
        assert result.target_type == "idea"

    async def test_get_user_opinion_assessments(
        self, db_service, sample_opinion_assessment
    ):
        """Test retrieving user's opinion assessments."""
        db_service.get_user_opinion_assessments.return_value = [
            sample_opinion_assessment
        ]

        result = await db_service.get_user_opinion_assessments(
            "123456789", limit=10
        )

        assert len(result) == 1
        assert result[0].discord_user_id == "123456789"
        assert result[0].contributes_to_wisdom == True

    async def test_increment_user_opinion_stats(self, db_service, sample_user):
        """Test incrementing user opinion statistics."""
        # Mock the user progression
        updated_user = DiscordUser(
            discord_id="123456789",
            discord_username="test_user",
            wisdom_level=1,
            total_opinions_assessed=1,
            experience_points=15,
        )

        db_service.increment_user_opinion_stats.return_value = updated_user

        result = await db_service.increment_user_opinion_stats("123456789")

        assert result.total_opinions_assessed == 1
        assert result.experience_points == 15

    async def test_wisdom_level_progression(self, db_service):
        """Test wisdom level progression based on assessments."""
        # Test progression at different thresholds
        test_cases = [
            (10, 2),  # 10 assessments -> level 2
            (25, 3),  # 25 assessments -> level 3
            (50, 4),  # 50 assessments -> level 4
            (100, 5),  # 100 assessments -> level 5
        ]

        for assessments, expected_level in test_cases:
            user = DiscordUser(
                discord_id="123456789",
                discord_username="test_user",
                wisdom_level=expected_level,
                total_opinions_assessed=assessments,
            )

            db_service.increment_user_opinion_stats.return_value = user
            result = await db_service.increment_user_opinion_stats("123456789")

            assert result.wisdom_level == expected_level


@pytest.mark.asyncio
class TestOpinionBotCommands:
    """Test Discord bot commands for opinion assessment."""

    @pytest.fixture
    def mock_ctx(self):
        """Mock Discord context."""
        ctx = MagicMock()
        ctx.author.id = "123456789"
        ctx.author.display_name = "TestUser"
        ctx.send = AsyncMock()
        return ctx

    @pytest.fixture
    def mock_db_service(self):
        """Mock database service."""
        return AsyncMock()

    async def test_submit_opinion_command(self, mock_ctx, mock_db_service):
        """Test the !opinion command."""
        with patch("discord_trans.opinion_bot.GameDBContext") as mock_context:
            mock_context.return_value.__aenter__.return_value = mock_db_service

            # Mock user and opinion creation
            mock_user = DiscordUser(
                discord_id="123456789", discord_username="TestUser"
            )
            mock_opinion = OpinionAssessment(
                id="test-opinion-id",
                discord_user_id="123456789",
                opinion_text="Test opinion",
                target_type="idea",
            )

            mock_db_service.get_or_create_discord_user.return_value = mock_user
            mock_db_service.create_opinion_assessment.return_value = (
                mock_opinion
            )
            mock_db_service.increment_user_opinion_stats.return_value = (
                mock_user
            )

            # Import and test the command
            from discord_trans.opinion_bot import submit_opinion

            await submit_opinion(
                mock_ctx, "idea", opinion_text="This is a test opinion"
            )

            # Verify database calls
            mock_db_service.get_or_create_discord_user.assert_called_once()
            mock_db_service.create_opinion_assessment.assert_called_once()
            mock_db_service.increment_user_opinion_stats.assert_called_once()

            # Verify response was sent
            mock_ctx.send.assert_called_once()

    async def test_patterns_command(self, mock_ctx, mock_db_service):
        """Test the !patterns command."""
        with patch("discord_trans.opinion_bot.GameDBContext") as mock_context:
            mock_context.return_value.__aenter__.return_value = mock_db_service

            # Mock opinion assessments with patterns
            mock_opinions = [
                OpinionAssessment(
                    id="opinion1",
                    discord_user_id="123456789",
                    logical_patterns=[
                        {"pattern": "evidence_based", "confidence": 0.8}
                    ],
                    complexity_level=2,
                    contributes_to_wisdom=True,
                ),
                OpinionAssessment(
                    id="opinion2",
                    discord_user_id="123456789",
                    logical_patterns=[
                        {
                            "pattern": "acknowledges_uncertainty",
                            "confidence": 0.7,
                        }
                    ],
                    complexity_level=3,
                    contributes_to_wisdom=True,
                ),
            ]

            mock_db_service.get_user_opinion_assessments.return_value = (
                mock_opinions
            )

            # Import and test the command
            from discord_trans.opinion_bot import view_patterns

            await view_patterns(mock_ctx)

            # Verify database call
            mock_db_service.get_user_opinion_assessments.assert_called_once_with(
                "123456789", limit=10
            )

            # Verify response was sent
            mock_ctx.send.assert_called_once()

    async def test_reflect_command(self, mock_ctx, mock_db_service):
        """Test the !reflect command."""
        with patch("discord_trans.opinion_bot.GameDBContext") as mock_context:
            mock_context.return_value.__aenter__.return_value = mock_db_service

            # Mock opinion assessment
            mock_opinion = OpinionAssessment(
                id="test-opinion-id",
                discord_user_id="123456789",
                opinion_text="Test opinion for reflection",
                target_type="idea",
                logical_patterns=[
                    {
                        "pattern": "evidence_based",
                        "confidence": 0.8,
                        "explanation": "References evidence",
                    }
                ],
                progression_notes="Consider examining alternative perspectives",
            )

            mock_db_service.get_opinion_assessment_by_id.return_value = (
                mock_opinion
            )

            # Import and test the command
            from discord_trans.opinion_bot import reflect_on_opinion

            await reflect_on_opinion(mock_ctx, "test-opinion")

            # Verify database call
            mock_db_service.get_opinion_assessment_by_id.assert_called_once_with(
                "test-opinion"
            )

            # Verify response was sent
            mock_ctx.send.assert_called_once()


class TestAntiCelebrityCulture:
    """Test that the system resists celebrity culture and popularity-based evaluation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.wisdom_engine = WisdomEngine()

    def test_evidence_beats_authority(self):
        """Test that evidence-based reasoning scores higher than appeal to authority."""
        evidence_opinion = (
            "Multiple peer-reviewed studies demonstrate this conclusion."
        )
        authority_opinion = "A famous person said this, so it must be true."

        evidence_analysis = self.wisdom_engine.analyze_opinion(
            evidence_opinion, "idea"
        )
        authority_analysis = self.wisdom_engine.analyze_opinion(
            authority_opinion, "idea"
        )

        assert (
            evidence_analysis["evidence_quality_score"]
            > authority_analysis["evidence_quality_score"]
        )

    def test_merit_over_popularity(self):
        """Test that logical merit is valued over popular opinion."""
        merit_opinion = "The data suggests this conclusion, though it's not widely accepted."
        popularity_opinion = "Everyone believes this, so it must be right."

        merit_analysis = self.wisdom_engine.analyze_opinion(
            merit_opinion, "idea"
        )
        popularity_analysis = self.wisdom_engine.analyze_opinion(
            popularity_opinion, "idea"
        )

        # Merit-based should have higher logical coherence
        assert (
            merit_analysis["logical_coherence_score"]
            > popularity_analysis["logical_coherence_score"]
        )

    def test_wisdom_progression_focus(self):
        """Test that the system focuses on wisdom development, not collection."""
        reflection_opinion = (
            "I used to think this, but examining the evidence changed my view."
        )
        collection_opinion = (
            "I've heard all the arguments and collected many opinions."
        )

        reflection_analysis = self.wisdom_engine.analyze_opinion(
            reflection_opinion, "idea"
        )
        collection_analysis = self.wisdom_engine.analyze_opinion(
            collection_opinion, "idea"
        )

        # Reflection and growth should score higher than mere collection
        assert (
            reflection_analysis["wisdom_potential_score"]
            > collection_analysis["wisdom_potential_score"]
        )
