"""
Test script for Discord bot fallacy card game commands.
This tests the database integration without requiring a Discord bot connection.
"""

import asyncio
import logging
import uuid

import pytest
from discord_trans.game_db_service import GameDBContext

logger = logging.getLogger(__name__)


@pytest.fixture
def unique_user_id():
    """Generate a unique user ID for each test."""
    return f"test_user_{uuid.uuid4().hex[:8]}"


@pytest.mark.asyncio
async def test_user_progression(unique_user_id):
    """Test user creation and progression mechanics."""
    print("🧪 Testing user progression...")

    async with GameDBContext() as db_service:
        # Create a test user
        user = await db_service.get_or_create_discord_user(
            unique_user_id, "TestUser"
        )
        print(
            f"✅ Created user: {user.discord_username} (Level {user.skill_level})"
        )

        # Award some XP
        updated_user = await db_service.award_experience(unique_user_id, 150)
        print(
            f"✅ Awarded 150 XP. New level: {updated_user.skill_level}, Total XP: {updated_user.experience_points}"
        )

        return user


@pytest.mark.asyncio
async def test_fallacy_discovery():
    """Test fallacy discovery and collection."""
    print("\n🎴 Testing fallacy discovery...")

    async with GameDBContext() as db_service:
        # Get all fallacies
        all_fallacies = await db_service.get_all_fallacies()
        print(f"✅ Found {len(all_fallacies)} fallacies in database")

        # Get random fallacy for level 0 user
        random_fallacy = await db_service.get_random_fallacy(0)
        if random_fallacy:
            print(
                f"✅ Random fallacy for level 0: {random_fallacy.name} (complexity: {random_fallacy.complexity_level})"
            )

        # Get fallacies by complexity level
        level_1_fallacies = await db_service.get_fallacies_by_complexity(1)
        level_2_fallacies = await db_service.get_fallacies_by_complexity(2)
        level_3_fallacies = await db_service.get_fallacies_by_complexity(3)
        level_4_fallacies = await db_service.get_fallacies_by_complexity(4)
        level_5_fallacies = await db_service.get_fallacies_by_complexity(5)

        print(f"✅ Complexity distribution:")
        print(f"   Level 1: {len(level_1_fallacies)}")
        print(f"   Level 2: {len(level_2_fallacies)}")
        print(f"   Level 3: {len(level_3_fallacies)}")
        print(f"   Level 4: {len(level_4_fallacies)}")
        print(f"   Level 5: {len(level_5_fallacies)}")

        return random_fallacy


@pytest.mark.asyncio
async def test_collection_mechanics(unique_user_id):
    """Test fallacy collection and user stats."""
    print("\n📚 Testing collection mechanics...")

    async with GameDBContext() as db_service:
        # Create a test user first
        user = await db_service.get_or_create_discord_user(
            unique_user_id, "TestCollector"
        )

        # Collect a fallacy
        strawman = await db_service.get_fallacy_by_name("strawman")
        if strawman:
            added = await db_service.add_fallacy_to_user_collection(
                unique_user_id, strawman.id
            )
            print(f"✅ Added Strawman to collection: {added}")

            # Try to add again (should return False)
            added_again = await db_service.add_fallacy_to_user_collection(
                unique_user_id, strawman.id
            )
            print(
                f"✅ Tried to add Strawman again: {added_again} (should be False)"
            )

        # Get user's collected fallacies
        collected = await db_service.get_user_collected_fallacies(
            unique_user_id
        )
        print(f"✅ User has collected {len(collected)} fallacies")

        # Get progress stats
        stats = await db_service.get_user_progress_stats(unique_user_id)
        print(f"✅ Progress stats:")
        print(f"   Skill Level: {stats['skill_level']}/10")
        print(f"   Wisdom Level: {stats['wisdom_level']}/5")
        print(f"   XP: {stats['experience_points']}")
        print(
            f"   Collection: {stats['collected_count']}/{stats['available_count']} ({stats['collection_percentage']:.1f}%)"
        )
        print(f"   Complexity breakdown: {stats['complexity_breakdown']}")


@pytest.mark.asyncio
async def test_search_functionality():
    """Test fallacy search by name."""
    print("\n🔍 Testing search functionality...")

    async with GameDBContext() as db_service:
        # Test exact match
        ad_hominem = await db_service.get_fallacy_by_name("Ad Hominem")
        if ad_hominem:
            print(f"✅ Found exact match: {ad_hominem.name}")

        # Test partial match
        partial_match = await db_service.get_fallacy_by_name("straw")
        if partial_match:
            print(f"✅ Found partial match: {partial_match.name}")

        # Test case insensitive
        case_insensitive = await db_service.get_fallacy_by_name("STRAWMAN")
        if case_insensitive:
            print(f"✅ Found case insensitive: {case_insensitive.name}")


async def main():
    """Run all tests."""
    print("🎮 Testing Discord Bot Fallacy Card Game Mechanics")
    print("=" * 50)

    try:
        await test_user_progression()
        await test_fallacy_discovery()
        await test_collection_mechanics()
        await test_search_functionality()

        print("\n🎉 All tests completed successfully!")
        print("The Discord bot game mechanics are working correctly.")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error(f"Test error: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
