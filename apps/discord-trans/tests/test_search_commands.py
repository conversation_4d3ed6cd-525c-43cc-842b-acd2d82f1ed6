"""
Test script for Discord bot search functionality.
This tests the search bot functions without requiring a Discord connection.
"""

import json
import logging
import os
import tempfile

logger = logging.getLogger(__name__)


def test_transcript_loading():
    """Test loading transcript data from files."""
    print("📁 Testing transcript loading...")

    # Import the search bot functions
    from discord_trans.search_bot import get_search_hits, load_transcript_data

    # Create a temporary test transcript
    test_transcript = {
        "transcript": [
            {"text": "Hello world", "start": 0.0, "duration": 2.0},
            {"text": "This is a test", "start": 2.0, "duration": 3.0},
            {"text": "Fallacy example here", "start": 5.0, "duration": 4.0},
        ]
    }

    # Create temporary file
    with tempfile.NamedTemporaryFile(
        mode="w", suffix=".json", delete=False
    ) as f:
        json.dump(test_transcript, f)
        temp_path = f.name

    try:
        # Test loading (this will fail since load_transcript_data expects specific directory structure)
        # But we can test the search function directly
        print("✅ Created test transcript data")

        # Test search functionality
        results = get_search_hits(test_transcript, "test")
        print(f"✅ Search for 'test' found {len(results)} results")
        assert len(results) == 1, (
            f"Expected 1 result for 'test', got {len(results)}"
        )

        results = get_search_hits(test_transcript, "fallacy")
        print(f"✅ Search for 'fallacy' found {len(results)} results")
        assert len(results) == 1, (
            f"Expected 1 result for 'fallacy', got {len(results)}"
        )

        results = get_search_hits(test_transcript, "nonexistent")
        print(
            f"✅ Search for 'nonexistent' found {len(results)} results (should be 0)"
        )
        assert len(results) == 0, (
            f"Expected 0 results for 'nonexistent', got {len(results)}"
        )

    finally:
        # Clean up
        if os.path.exists(temp_path):
            os.unlink(temp_path)


def test_database_functions():
    """Test database connection and basic operations."""
    print("\n💾 Testing database functions...")

    from discord_trans.search_bot import get_db_connection

    # Test database connection
    conn = get_db_connection()
    cursor = conn.cursor()

    # Test table creation (should already exist)
    cursor.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='transcripts'"
    )
    table_exists = cursor.fetchone() is not None
    print(f"✅ Database table exists: {table_exists}")
    assert table_exists, "Database table 'transcripts' should exist"

    # Test basic operations
    cursor.execute("SELECT COUNT(*) FROM transcripts")
    count = cursor.fetchone()[0]
    print(f"✅ Found {count} cached transcripts in database")
    assert count >= 0, "Count should be non-negative"

    conn.close()


def test_search_utilities():
    """Test search utility functions."""
    print("\n🔍 Testing search utilities...")

    from binary_trans import build_regex_word_boundary
    from binary_trans.search_utils import search_tran_slurp
    from binary_trans.trans_prepper import TranscriptPrepper

    # Test transcript preparation
    test_data = [
        {
            "text": "This is a test transcript",
            "start": 0.0,
            "duration": 2.0,
        },
        {"text": "With multiple segments", "start": 2.0, "duration": 3.0},
        {
            "text": "For testing search functionality",
            "start": 5.0,
            "duration": 4.0,
        },
    ]

    tm = TranscriptPrepper(test_data)
    print(f"✅ TranscriptPrepper created with {len(test_data)} segments")
    print(f"✅ Full text length: {len(tm.slurp)} characters")
    assert len(test_data) == 3, "Should have 3 test segments"
    assert len(tm.slurp) > 0, "Should have non-empty text"

    # Test regex building
    regex = build_regex_word_boundary(["test"])
    print("✅ Regex pattern built successfully")
    assert regex is not None, "Regex should be created"

    # Test search
    results = search_tran_slurp(tm, regex)
    print(f"✅ Search found {len(results)} matches for 'test'")
    assert len(results) >= 1, "Should find at least one match for 'test'"


# Tests can be run with: pytest test_search_commands.py
