#
# This file is autogenerated by hatch-pip-compile with Python 3.12
#
# - sqlalchemy[asyncio]>=2.0.0
# - aiosqlite>=0.19.0
# - pytest-asyncio>=0.21.0
# - pytest>=8.0.0
# - discord-py>=2.3.2
# - pytest-asyncio>=0.21.0
# - pytest>=8.0.0
#

aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.13
    # via discord-py
aiosignal==1.3.2
    # via aiohttp
aiosqlite==0.21.0
    # via hatch.envs.default
attrs==25.3.0
    # via aiohttp
discord-py==2.5.2
    # via hatch.envs.default
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
greenlet==3.2.3
    # via sqlalchemy
idna==3.10
    # via yarl
iniconfig==2.1.0
    # via pytest
multidict==6.5.0
    # via
    #   aiohttp
    #   yarl
packaging==25.0
    # via pytest
pluggy==1.6.0
    # via pytest
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
pygments==2.19.1
    # via pytest
pytest==8.4.0
    # via
    #   hatch.envs.default
    #   pytest-asyncio
pytest-asyncio==1.0.0
    # via hatch.envs.default
sqlalchemy==2.0.41
    # via hatch.envs.default
typing-extensions==4.14.0
    # via
    #   aiosqlite
    #   sqlalchemy
yarl==1.20.1
    # via aiohttp
