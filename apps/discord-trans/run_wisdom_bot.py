#!/usr/bin/env python3
"""
Simple script to run the wisdom bot with proper environment setup.
"""

import os
import sys

# Set up environment
os.environ['DISCORD_BOT_TOKEN'] = 'MTM4MzIxMzU3NjY3NTc5MDg1OA.G1yReO.ojJL1PSw6x7bGfUAMFQjxEM9YC4Ol7SWSaQwxM'
os.environ['DISCORD_TRANSCRIPTS_BASE_DIR'] = '/home/<USER>/repo/knowtrails/apps/tran-hits/tests/trans_pile'

print("🧘 Starting Wisdom Assessment Bot...")
print(f"Token available: {bool(os.environ.get('DISCORD_BOT_TOKEN'))}")

try:
    # Import and run the bot
    from src.discord_trans.opinion_bot import bot
    print(f"Available commands: {[cmd.name for cmd in bot.commands]}")
    
    # Run the bot
    token = os.environ.get('DISCORD_BOT_TOKEN')
    if token:
        print("🚀 Bot starting...")
        bot.run(token)
    else:
        print("❌ No token found")
        
except Exception as e:
    print(f"❌ Error starting bot: {e}")
    import traceback
    traceback.print_exc()
