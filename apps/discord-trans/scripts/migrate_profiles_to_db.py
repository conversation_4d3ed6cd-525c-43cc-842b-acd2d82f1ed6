import json
import os
import sqlite3

# Define paths relative to the script's location
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
APP_ROOT = os.path.abspath(
    os.path.join(BASE_DIR, "..")
)  # Should be apps/discord-trans/
DB_PATH = os.path.join(APP_ROOT, "discord_bot_cache.db")
PROFILE_JSON_PATH = os.path.join(
    APP_ROOT, "src", "discord_trans", "user_card", "profiles.json"
)


def get_db_connection():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn


def migrate_profiles():
    if not os.path.exists(PROFILE_JSON_PATH):
        print(
            f"profiles.json not found at {PROFILE_JSON_PATH}. No migration needed."
        )
        return

    try:
        with open(PROFILE_JSON_PATH, "r", encoding="utf-8") as f:
            legacy_profiles = json.load(f)
    except json.JSONDecodeError:
        print(
            f"Error decoding <PERSON><PERSON><PERSON> from {PROFILE_JSON_PATH}. Skipping migration."
        )
        return
    except FileNotFoundError:
        print(
            f"profiles.json not found at {PROFILE_JSON_PATH}. No migration needed."
        )
        return

    conn = get_db_connection()
    c = conn.cursor()

    migrated_count = 0
    for profile in legacy_profiles:
        username = profile.get("username")
        if not username:
            print(f"Skipping profile with no username: {profile}")
            continue

        # Create a placeholder discord_id for legacy profiles
        # In a real scenario, you'd want to prompt users to link their Discord ID
        # or have a mapping from old usernames to new Discord IDs.
        discord_id = f"legacy_user_{username.lower()}"  # Using a unique prefix for legacy users

        # Ensure preferred_topics is stored as JSON string
        preferred_topics = json.dumps(profile.get("preferred_topics", []))

        try:
            c.execute(
                """
                INSERT OR IGNORE INTO discord_users (
                    discord_id, discord_username, bot_profile_name, skill_level,
                    temperament, humor, trust_level, preferred_topics,
                    communication_style, sentiment_tendency
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    discord_id,
                    username,  # Use original username as discord_username for legacy
                    username,  # Use original username as bot_profile_name for legacy
                    profile.get("skill_level", "unknown"),
                    profile.get("temperament", "unknown"),
                    profile.get("humor", "unknown"),
                    profile.get("trust_level", "unknown"),
                    preferred_topics,
                    profile.get("communication_style", "neutral"),
                    profile.get("sentiment_tendency", "neutral"),
                ),
            )
            if c.rowcount > 0:
                migrated_count += 1
                print(
                    f"Migrated legacy profile for username: {username} with placeholder ID: {discord_id}"
                )
            else:
                print(
                    f"Profile for {username} (ID: {discord_id}) already exists in DB. Skipping."
                )
        except sqlite3.Error as e:
            print(f"Error migrating profile for {username}: {e}")
            conn.rollback()  # Rollback on error
            continue

    conn.commit()
    conn.close()
    print(
        f"Migration complete. {migrated_count} legacy profiles migrated/updated."
    )

    # Optionally, rename or delete the old profiles.json after successful migration
    # os.rename(PROFILE_JSON_PATH, PROFILE_JSON_PATH + ".bak")
    # print(f"Renamed {PROFILE_JSON_PATH} to {PROFILE_JSON_PATH}.bak")


if __name__ == "__main__":
    print("Starting profiles migration...")
    migrate_profiles()
    print("Profiles migration script finished.")
