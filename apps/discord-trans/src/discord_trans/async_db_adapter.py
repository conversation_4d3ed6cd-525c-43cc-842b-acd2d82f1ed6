"""
Async database adapter for Discord bot.
Replaces blocking SQLite calls with non-blocking PostgreSQL operations.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Import the new Discord-specific database package
try:
    from discord_trans_db.models import Base
    from discord_trans_db.services import DiscordDBService
except ImportError as e:
    logging.error(f"Failed to import discord-trans-db package: {e}")
    logging.error("Make sure discord-trans-db package is installed")
    raise

logger = logging.getLogger(__name__)


class AsyncDiscordDBAdapter:
    """
    Async database adapter for Discord bot.
    Provides non-blocking database operations.
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize the async database adapter."""
        self.database_url = database_url or os.getenv(
            "DISCORD_BOT_DATABASE_URL",
            "postgresql+asyncpg://user:password@localhost/discord_trans_bot"
        )
        
        if not self.database_url.startswith("postgresql+asyncpg://"):
            raise ValueError("Discord bot requires PostgreSQL with asyncpg driver")
        
        self.engine = create_async_engine(self.database_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self._initialized = False
    
    async def initialize(self):
        """Initialize database tables."""
        if self._initialized:
            return
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        self._initialized = True
        logger.info("Discord bot async database adapter initialized")
    
    async def get_or_create_user(self, discord_id: str, discord_username: str) -> Dict[str, Any]:
        """Get or create a Discord user (async)."""
        await self.initialize()
        
        async with self.async_session() as session:
            service = DiscordDBService(session)
            user = await service.get_or_create_discord_user(discord_id, discord_username)
            return user.to_dict()
    
    async def update_user_profile(self, discord_id: str, profile_updates: Dict[str, Any]) -> bool:
        """Update Discord user profile (async)."""
        await self.initialize()
        
        async with self.async_session() as session:
            service = DiscordDBService(session)
            updated_user = await service.update_discord_user_profile(discord_id, profile_updates)
            return updated_user is not None
    
    async def cache_transcript(self, video_id: str, transcript_data: Dict[str, Any]) -> bool:
        """Cache a transcript (async)."""
        await self.initialize()
        
        try:
            async with self.async_session() as session:
                service = DiscordDBService(session)
                title = transcript_data.get("title", f"Discord cached: {video_id}")
                await service.cache_transcript(video_id, transcript_data, title)
                return True
        except Exception as e:
            logger.error(f"Failed to cache transcript {video_id}: {e}")
            return False
    
    async def get_cached_transcript(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get a cached transcript (async)."""
        await self.initialize()
        
        async with self.async_session() as session:
            service = DiscordDBService(session)
            cached = await service.get_cached_transcript(video_id)
            
            if cached:
                return {
                    "transcript_json": json.dumps(cached.transcript_json),
                    "slurp_text": cached.slurp_text,
                    "title": cached.title
                }
            return None
    
    async def list_cached_transcripts(self) -> List[str]:
        """List all cached transcript video IDs (async)."""
        await self.initialize()
        
        async with self.async_session() as session:
            service = DiscordDBService(session)
            cached_transcripts = await service.list_cached_transcripts()
            return [ct.video_id for ct in cached_transcripts]
    
    async def log_search(
        self, 
        discord_id: str, 
        search_term: str, 
        transcript_id: Optional[str] = None,
        results_found: Optional[int] = None,
        channel_id: Optional[str] = None,
        guild_id: Optional[str] = None
    ) -> bool:
        """Log a search (async)."""
        await self.initialize()
        
        try:
            async with self.async_session() as session:
                service = DiscordDBService(session)
                await service.log_search(
                    discord_id, search_term, transcript_id, 
                    results_found, channel_id, guild_id
                )
                return True
        except Exception as e:
            logger.error(f"Failed to log search for {discord_id}: {e}")
            return False
    
    async def close(self):
        """Close the database connection."""
        if hasattr(self, 'engine'):
            await self.engine.dispose()


# Global adapter instance
_adapter: Optional[AsyncDiscordDBAdapter] = None


def get_async_adapter() -> AsyncDiscordDBAdapter:
    """Get the global async database adapter."""
    global _adapter
    if _adapter is None:
        _adapter = AsyncDiscordDBAdapter()
    return _adapter


# Helper functions for Discord bot commands
async def async_get_or_create_user(discord_id: str, discord_username: str) -> Dict[str, Any]:
    """Helper: Get or create user (async)."""
    adapter = get_async_adapter()
    return await adapter.get_or_create_user(discord_id, discord_username)


async def async_update_user_profile(discord_id: str, profile_updates: Dict[str, Any]) -> bool:
    """Helper: Update user profile (async)."""
    adapter = get_async_adapter()
    return await adapter.update_user_profile(discord_id, profile_updates)


async def async_cache_transcript(video_id: str, transcript_data: Dict[str, Any]) -> bool:
    """Helper: Cache transcript (async)."""
    adapter = get_async_adapter()
    return await adapter.cache_transcript(video_id, transcript_data)


async def async_get_cached_transcript(video_id: str) -> Optional[Dict[str, Any]]:
    """Helper: Get cached transcript (async)."""
    adapter = get_async_adapter()
    return await adapter.get_cached_transcript(video_id)


async def async_list_cached_transcripts() -> List[str]:
    """Helper: List cached transcripts (async)."""
    adapter = get_async_adapter()
    return await adapter.list_cached_transcripts()


async def async_log_search(
    discord_id: str, 
    search_term: str, 
    transcript_id: Optional[str] = None,
    results_found: Optional[int] = None,
    channel_id: Optional[str] = None,
    guild_id: Optional[str] = None
) -> bool:
    """Helper: Log search (async)."""
    adapter = get_async_adapter()
    return await adapter.log_search(
        discord_id, search_term, transcript_id, 
        results_found, channel_id, guild_id
    )


# Sync wrapper functions for backward compatibility
def sync_wrapper(async_func):
    """Wrapper to run async functions in sync context (use sparingly)."""
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an async context, this is problematic
                logger.warning("Attempting to run async DB operation in sync context within async loop")
                # You might want to raise an exception here instead
                return None
            return loop.run_until_complete(async_func(*args, **kwargs))
        except RuntimeError:
            # No event loop, create one
            return asyncio.run(async_func(*args, **kwargs))
    return wrapper


# Sync versions (use these sparingly, prefer async versions)
sync_get_or_create_user = sync_wrapper(async_get_or_create_user)
sync_cache_transcript = sync_wrapper(async_cache_transcript)
sync_get_cached_transcript = sync_wrapper(async_get_cached_transcript)
