"""
Example of how to update Discord bot commands to use async database operations.
This shows the pattern for converting blocking SQLite calls to non-blocking PostgreSQL.
"""

import json
import logging
from typing import Dict, Any, Optional

import discord
from discord.ext import commands

from .async_db_adapter import (
    async_cache_transcript,
    async_get_cached_transcript,
    async_get_or_create_user,
    async_list_cached_transcripts,
    async_log_search,
    async_update_user_profile,
)

logger = logging.getLogger(__name__)


# Example: Updated cache_transcript command
async def cache_transcript_async(ctx: commands.Context, transcript_id: str):
    """
    Cache a transcript using async database operations.
    This replaces the blocking SQLite version.
    """
    # Load transcript data (this part stays the same)
    transcript_data, error_msg = load_transcript_data(transcript_id)  # Your existing function
    if error_msg:
        await ctx.send(f":warning: Error: {error_msg}")
        return
    
    # Use async database operation instead of blocking SQLite
    success = await async_cache_transcript(transcript_id, transcript_data)
    
    if success:
        await ctx.send(f"Transcript '{transcript_id}' cached in database.")
    else:
        await ctx.send(f":x: Failed to cache transcript '{transcript_id}'.")


# Example: Updated get_transcript command
async def get_transcript_async(ctx: commands.Context, transcript_id: str):
    """
    Get a cached transcript using async database operations.
    """
    cached_data = await async_get_cached_transcript(transcript_id)
    
    if not cached_data:
        await ctx.send(f"Transcript '{transcript_id}' not found in cache.")
        return
    
    transcript_json = cached_data["transcript_json"]
    
    if len(transcript_json) > 1800:
        await ctx.send(
            f"Transcript '{transcript_id}' is too large to display. "
            f"Showing first 1800 chars:\n```json\n{transcript_json[:1800]}...\n```"
        )
    else:
        await ctx.send(f"Transcript '{transcript_id}':\n```json\n{transcript_json}\n```")


# Example: Updated list_transcripts command
async def list_transcripts_async(ctx: commands.Context):
    """
    List cached transcripts using async database operations.
    """
    video_ids = await async_list_cached_transcripts()
    
    if not video_ids:
        await ctx.send("No transcripts are currently cached.")
        return
    
    # Build numbered list
    numbered = [f"{i + 1}. {vid}" for i, vid in enumerate(video_ids)]
    await ctx.send("Cached transcripts:\n" + "\n".join(numbered))


# Example: Updated search command with logging
async def search_transcript_async(ctx: commands.Context, transcript_id: str, *, phrase: str):
    """
    Search for a phrase in a transcript with async logging.
    """
    # Get user info for logging
    discord_id = str(ctx.author.id)
    discord_username = ctx.author.name
    channel_id = str(ctx.channel.id) if ctx.channel else None
    guild_id = str(ctx.guild.id) if ctx.guild else None
    
    # Ensure user exists in database
    await async_get_or_create_user(discord_id, discord_username)
    
    # Get cached transcript
    cached_data = await async_get_cached_transcript(transcript_id)
    if not cached_data:
        await ctx.send(f"Transcript '{transcript_id}' not found in cache.")
        return
    
    # Parse transcript data
    try:
        transcript_json = json.loads(cached_data["transcript_json"])
        transcript = transcript_json.get("transcript", [])
    except (json.JSONDecodeError, KeyError):
        await ctx.send(f":x: Error parsing cached transcript '{transcript_id}'.")
        return
    
    # Perform search (your existing search logic)
    results = get_search_hits(transcript_json, phrase)  # Your existing function
    
    # Log the search asynchronously (non-blocking)
    await async_log_search(
        discord_id=discord_id,
        search_term=phrase,
        transcript_id=transcript_id,
        results_found=len(results) if results else 0,
        channel_id=channel_id,
        guild_id=guild_id
    )
    
    # Send results
    max_lines = 8
    if results:
        results = results[:max_lines]
        messages = [f"{hit['start']}s : {hit['text']}" for hit in results[:max_lines]]
        messages_text = "\n".join(messages)[:500]
        await ctx.send(messages_text)
    else:
        await ctx.send(f"Phrase '{phrase}' not found in transcript '{transcript_id}'.")


# Example: Updated set_profile command
async def set_profile_async(ctx: commands.Context, member: discord.Member, *, updates_str: str):
    """
    Set or update conversation profile attributes for a user.
    """
    discord_id = str(member.id)
    discord_username = member.name
    
    # Parse updates
    updates = {}
    for item in updates_str.split():
        if "=" in item:
            key, value = item.split("=", 1)
            if key == "preferred_topics":
                updates[key] = [topic.strip() for topic in value.split(",")]
            else:
                updates[key] = value.strip()
    
    # Ensure user exists
    await async_get_or_create_user(discord_id, discord_username)
    
    # Update profile
    success = await async_update_user_profile(discord_id, updates)
    
    if success:
        await ctx.send(
            f"Profile for {member.name} (ID: {member.id}) updated successfully with: {updates}"
        )
    else:
        await ctx.send(f"Failed to update profile for {member.name}.")


# Example: How to register these commands with your bot
def register_async_commands(bot: commands.Bot):
    """
    Register the async versions of commands with your bot.
    Call this instead of the old sync versions.
    """
    
    @bot.command(name="cache_transcript_async")
    async def cache_transcript_cmd(ctx: commands.Context, transcript_id: str):
        await cache_transcript_async(ctx, transcript_id)
    
    @bot.command(name="get_transcript_async")
    async def get_transcript_cmd(ctx: commands.Context, transcript_id: str):
        await get_transcript_async(ctx, transcript_id)
    
    @bot.command(name="list_transcripts_async")
    async def list_transcripts_cmd(ctx: commands.Context):
        await list_transcripts_async(ctx)
    
    @bot.command(name="search_async")
    async def search_cmd(ctx: commands.Context, transcript_id: str, *, phrase: str):
        await search_transcript_async(ctx, transcript_id, phrase)
    
    @bot.command(name="set_profile_async")
    async def set_profile_cmd(ctx: commands.Context, member: discord.Member, *, updates_str: str):
        await set_profile_async(ctx, member, updates_str)
    
    logger.info("Registered async Discord bot commands")


# Migration helper: Sync transcripts from old SQLite to new PostgreSQL
async def migrate_sqlite_to_postgres():
    """
    One-time migration helper to move data from SQLite to PostgreSQL.
    Run this once during the transition.
    """
    try:
        from .async_db_adapter import get_async_adapter
        
        # Read from old SQLite database
        import sqlite3
        import os
        
        # Your existing SQLite path
        sqlite_path = os.path.join(os.path.dirname(__file__), "discord_bot_cache.db")
        
        if not os.path.exists(sqlite_path):
            logger.info("No SQLite database found to migrate")
            return
        
        conn = sqlite3.connect(sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Migrate users
        cursor.execute("SELECT * FROM discord_users")
        sqlite_users = [dict(row) for row in cursor.fetchall()]
        
        # Migrate transcripts
        cursor.execute("SELECT * FROM transcripts")
        sqlite_transcripts = []
        for row in cursor.fetchall():
            sqlite_transcripts.append({
                "video_id": row["video_id"],
                "transcript_json": json.loads(row["transcript_json"])
            })
        
        conn.close()
        
        # Use the new async service to migrate
        adapter = get_async_adapter()
        await adapter.initialize()
        
        async with adapter.async_session() as session:
            from discord_trans_db.services import DiscordDBService
            service = DiscordDBService(session)
            
            users_migrated, transcripts_migrated = await service.migrate_from_sqlite(
                sqlite_users, sqlite_transcripts
            )
            
            logger.info(f"Migration completed: {users_migrated} users, {transcripts_migrated} transcripts")
    
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise
