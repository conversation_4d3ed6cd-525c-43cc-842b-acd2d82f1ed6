"""
Discord Bot for Transcript Search and Analysis
Dedicated bot for searching, caching, and analyzing transcript content.
"""

import glob
import json
import logging
import os
import sqlite3
import traceback

import discord

# Import transcript processing tools
from binary_trans import build_regex_word_boundary
from binary_trans.search_utils import search_tran_slurp
from binary_trans.trans_prepper import TranscriptPrepper
from discord.ext import commands

# Set up logging
logger = logging.getLogger(__name__)

# --- Configuration ---
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")
COMMAND_PREFIX = "!"

# --- Transcript File Management ---
APP_ROOT = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "../../../..")
)
TRANSCRIPTS_BASE_DIR = os.environ.get(
    "DISCORD_TRANSCRIPTS_BASE_DIR",
    os.path.join(APP_ROOT, "packages", "tran-hits", "tests", "trans_pile"),
)

# --- Bot Setup ---
intents = discord.Intents.default()
intents.message_content = True
intents.members = True

bot = commands.Bot(command_prefix=COMMAND_PREFIX, intents=intents)

# Global cache for transcript index mapping (per bot session)
transcript_index_map = {}


@bot.event
async def on_ready():
    """Bot startup event."""
    print(f"🔍 Transcript Search Bot logged in as {bot.user}")
    print(f"Bot is in {len(bot.guilds)} guilds")
    print(f"Transcript directory: {TRANSCRIPTS_BASE_DIR}")

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} slash commands")
    except Exception as e:
        print(f"Error syncing slash commands: {e}")


@bot.command(name="ping")
async def ping(ctx: commands.Context):
    """Checks bot responsiveness."""
    await ctx.send("🔍 Pong! Search Bot is online!")


@bot.command(name="search_help")
async def help_command(ctx):
    """Display help menu for search functionality."""
    embed = discord.Embed(
        title="🔍 Transcript Search Bot - Help Menu",
        color=discord.Color.blue(),
    )

    embed.add_field(
        name="🔍 Search Commands",
        value="`!search <transcript_id> <phrase>` - Search for a phrase in a transcript\n"
        "`!findmcc <transcript_id> <mcc>` - Find line by character count\n"
        "`!search_cached <transcript_id> <phrase>` - Search cached transcripts\n"
        "`!list_transcripts` - List all cached transcripts",
        inline=False,
    )

    embed.add_field(
        name="📁 Cache Management",
        value="`!cache_transcript <id>` - Cache a transcript for faster access\n"
        "`!get_transcript <id>` - View cached transcript content",
        inline=False,
    )

    embed.add_field(
        name="🚀 Getting Started",
        value="1. Use `!list_transcripts` to see available content\n"
        "2. Use `!search <id> <phrase>` to find specific content\n"
        "3. Use `!cache_transcript <id>` for faster repeated searches\n"
        "4. Use `!findmcc` for precise character-based navigation",
        inline=False,
    )

    embed.set_footer(text="Happy searching! 🎯")
    await ctx.send(embed=embed)


# === Database Helper Functions ===


def get_db_connection():
    """Get SQLite database connection for transcript caching."""
    db_path = os.path.join(os.path.dirname(__file__), "discord_bot_cache.db")
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row

    # Create table if it doesn't exist
    conn.execute("""
        CREATE TABLE IF NOT EXISTS transcripts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            video_id TEXT UNIQUE,
            transcript_json TEXT
        )
    """)
    conn.commit()
    return conn


def load_transcript_data(transcript_id: str):
    """Load transcript data from file system."""
    try:
        transcript_path = os.path.join(
            TRANSCRIPTS_BASE_DIR, f"{transcript_id}.json"
        )

        if not os.path.exists(transcript_path):
            return (
                None,
                f"Transcript file '{transcript_id}.json' not found in {TRANSCRIPTS_BASE_DIR}",
            )

        with open(transcript_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        return data, None

    except json.JSONDecodeError as e:
        return None, f"Invalid JSON in transcript file: {e}"
    except Exception as e:
        return None, f"Error loading transcript: {e}"


# === Search Helper Functions ===


def get_search_hits(transcript_data: dict, phrase: str) -> list[dict]:
    """Search for phrase in transcript data and return hits."""
    if "transcript" not in transcript_data:
        raise ValueError("Transcript data missing 'transcript' key")

    tm = TranscriptPrepper(transcript_data["transcript"])
    regex = build_regex_word_boundary([phrase])
    return search_tran_slurp(tm, regex)


# === Search Commands ===


@bot.command(name="list_transcripts")
async def list_transcripts(ctx: commands.Context):
    """List all cached transcripts with numbered index."""
    conn = get_db_connection()
    c = conn.cursor()
    c.execute("SELECT video_id FROM transcripts ORDER BY id DESC")
    rows = c.fetchall()
    conn.close()

    if not rows:
        await ctx.send(
            "No transcripts are currently cached. Use `!cache_transcript <id>` to cache some!"
        )
        return

    ids = [row["video_id"] for row in rows]
    global transcript_index_map
    transcript_index_map = {str(i + 1): vid for i, vid in enumerate(ids)}
    numbered = [f"{i + 1}. {vid}" for i, vid in enumerate(ids)]

    await ctx.send("📁 Cached transcripts:\n" + "\n".join(numbered))


@bot.command(name="search")
async def search_transcript(ctx, transcript_id: str, *, phrase: str):
    """
    Search for a phrase in a transcript and return the hit context(s).
    Usage: !search <transcript_id> <phrase>
    """
    try:
        data, err = load_transcript_data(transcript_id)
        if err:
            await ctx.send(f"❌ Error: {err}")
            return

        results = get_search_hits(data, phrase)

        max_lines = 8
        if results:
            results = results[:max_lines]
            messages = [
                f"{hit['start']}s : {hit['text']}"
                for hit in results[:max_lines]
            ]
            messages = "\n".join(messages)[:1500]  # Discord message limit
            await ctx.send(
                f"🔍 **Results for '{phrase}' in '{transcript_id}':**\n```\n{messages}\n```"
            )
        else:
            await ctx.send(
                f"❌ Phrase '{phrase}' not found in transcript '{transcript_id}'."
            )

    except Exception as e:
        logger.error(f"Error in search command: {e}")
        await ctx.send(f"❌ An error occurred while searching: {str(e)}")


@bot.command(name="findmcc")
async def find_mcc_in_transcript(
    ctx: commands.Context, transcript_id: str, mcc_value: int
):
    """
    Finds the line for a matched character count (mcc) in the transcript.
    Usage: !findmcc <transcript_id> <mcc_value>
    Example: !findmcc PsgBtOVzHKI 150
    """
    await ctx.send(
        f"🔍 Finding MCC {mcc_value} in transcript '{transcript_id}'..."
    )

    transcript_data, error_msg = load_transcript_data(transcript_id)
    if error_msg:
        await ctx.send(f"❌ Error: {error_msg}")
        return

    try:
        tm = TranscriptPrepper(transcript_data["transcript"])

        if mcc_value < 0 or mcc_value >= len(tm.slurp):
            await ctx.send(
                f"❌ MCC value {mcc_value} is out of bounds for this transcript (length {len(tm.slurp)})."
            )
            return

        result = tm.get_hit_context(mcc_value)
        response_data_json = json.dumps(result, indent=2)
        response_message = f"📍 **Context for MCC {mcc_value} in '{transcript_id}':**\n```json\n{response_data_json}\n```"

        if len(response_message) > 2000:
            await ctx.send("❌ Result is too long to display fully.")
        else:
            await ctx.send(response_message)

    except ValueError as ve:
        await ctx.send(f"❌ Error processing transcript or MCC: {str(ve)}")
    except Exception as e:
        await ctx.send(f"❌ An unexpected error occurred: {str(e)}")
        logger.error(
            f"Error in find_mcc_in_transcript: {e}\n{traceback.format_exc()}"
        )


# === Cache Management Commands ===


@bot.command(name="cache_transcript")
async def cache_transcript(ctx: commands.Context, transcript_id: str):
    """Cache a transcript in the local database for faster access."""
    transcript_data, error_msg = load_transcript_data(transcript_id)
    if error_msg:
        await ctx.send(f"❌ Error: {error_msg}")
        return

    conn = get_db_connection()
    c = conn.cursor()
    c.execute(
        "INSERT OR REPLACE INTO transcripts (video_id, transcript_json) VALUES (?, ?)",
        (transcript_id, json.dumps(transcript_data)),
    )
    conn.commit()
    conn.close()
    await ctx.send(f"✅ Transcript '{transcript_id}' cached in database.")


@bot.command(name="get_transcript")
async def get_transcript(ctx: commands.Context, transcript_id: str):
    """Retrieve and display a cached transcript."""
    conn = get_db_connection()
    c = conn.cursor()
    c.execute(
        "SELECT transcript_json FROM transcripts WHERE video_id = ?",
        (transcript_id,),
    )
    row = c.fetchone()
    conn.close()

    if not row:
        await ctx.send(f"❌ Transcript '{transcript_id}' not found in cache.")
        return

    data = row["transcript_json"]
    if len(data) > 1800:
        await ctx.send(
            f"📄 **Transcript '{transcript_id}'** (showing first 1800 chars):\n```json\n{data[:1800]}...\n```"
        )
    else:
        await ctx.send(
            f"📄 **Transcript '{transcript_id}':**\n```json\n{data}\n```"
        )


@bot.command(name="search_cached")
async def search_cached(
    ctx: commands.Context, transcript_id: str, *, phrase: str
):
    """Search for a phrase in a cached transcript."""
    conn = get_db_connection()
    c = conn.cursor()
    c.execute(
        "SELECT transcript_json FROM transcripts WHERE video_id = ?",
        (transcript_id,),
    )
    row = c.fetchone()
    conn.close()

    if not row:
        await ctx.send(f"❌ Transcript '{transcript_id}' not found in cache.")
        return

    try:
        transcript_data = json.loads(row["transcript_json"])
        tm = TranscriptPrepper(transcript_data["transcript"])
        full_text = tm.slurp
        mcc = full_text.lower().find(phrase.lower())

        if mcc == -1:
            await ctx.send(
                f"❌ Phrase '{phrase}' not found in cached transcript '{transcript_id}'."
            )
            return

        hit = tm.get_hit_context(mcc)
        response_data = {
            "phrase": phrase,
            "transcript_id": transcript_id,
            "first_match_char_offset": mcc,
            "hit_details": hit if isinstance(hit, dict) else str(hit),
        }

        response_json = json.dumps(response_data, indent=2)
        if len(response_json) > 1800:
            await ctx.send(
                f"🔍 **Found in '{transcript_id}'** (truncated):\n```json\n{response_json[:1800]}...\n```"
            )
        else:
            await ctx.send(
                f"🔍 **Found in '{transcript_id}':**\n```json\n{response_json}\n```"
            )

    except Exception as e:
        await ctx.send(f"❌ Error searching cached transcript: {str(e)}")
        logger.error(f"Error in search_cached: {e}")


# --- Run the Bot ---
if __name__ == "__main__":
    if not DISCORD_BOT_TOKEN:
        print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
    else:
        try:
            bot.run(DISCORD_BOT_TOKEN)
        except Exception as e:
            print(f"Failed to start bot: {e}")
            print(traceback.format_exc())
