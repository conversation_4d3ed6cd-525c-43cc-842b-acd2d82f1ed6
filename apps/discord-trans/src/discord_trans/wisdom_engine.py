"""
Wisdom Engine for Opinion Assessment System
Analyzes opinions for logical patterns and wisdom potential without judgment.
Focuses on merit-based evaluation over popularity.
"""

import json
import re
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Tuple


class LogicalPattern(Enum):
    """Types of logical patterns that can be detected in opinions."""

    # Positive patterns (wisdom indicators)
    EVIDENCE_BASED = "evidence_based"
    NUANCED_THINKING = "nuanced_thinking"
    ACKNOWLEDGES_UNCERTAINTY = "acknowledges_uncertainty"
    CONSIDERS_MULTIPLE_PERSPECTIVES = "considers_multiple_perspectives"
    BUILDS_ON_EXISTING_KNOWLEDGE = "builds_on_existing_knowledge"

    # Misalignment patterns (fallacy indicators)
    AD_HOMINEM = "ad_hominem"
    STRAWMAN = "strawman"
    FALSE_DICHOTOMY = "false_dichotomy"
    APPEAL_TO_AUTHORITY = "appeal_to_authority"
    APPEAL_TO_POPULARITY = "appeal_to_popularity"
    HASTY_GENERALIZATION = "hasty_generalization"
    CIRCULAR_REASONING = "circular_reasoning"

    # Neutral patterns (neither positive nor negative)
    PERSONAL_EXPERIENCE = "personal_experience"
    EMOTIONAL_EXPRESSION = "emotional_expression"
    QUESTION_RAISING = "question_raising"


@dataclass
class PatternMatch:
    """Represents a detected logical pattern in an opinion."""

    pattern: LogicalPattern
    confidence: float  # 0.0-1.0
    evidence_text: str  # The text that triggered this pattern
    explanation: str  # Why this pattern was detected
    wisdom_direction: Optional[str] = None  # What wisdom this points toward


class WisdomEngine:
    """
    Engine for analyzing opinions and detecting logical patterns.
    Focuses on merit-based evaluation and wisdom cultivation.
    """

    def __init__(self):
        """Initialize the wisdom engine with pattern detection rules."""
        self.pattern_rules = self._initialize_pattern_rules()
        self.mentor_insights = self._initialize_mentor_insights()

    def analyze_opinion(
        self,
        opinion_text: str,
        target_type: str,
        target_reference: Optional[str] = None,
    ) -> Dict:
        """
        Analyze an opinion for logical patterns and wisdom potential.

        Args:
            opinion_text: The opinion to analyze
            target_type: Type of target ("idea", "thing", "person", "concept")
            target_reference: Optional reference to what the opinion is about

        Returns:
            Dictionary with analysis results
        """
        # Detect logical patterns
        patterns = self._detect_patterns(opinion_text)

        # Calculate merit-based scores
        scores = self._calculate_merit_scores(patterns, opinion_text)

        # Assess wisdom potential
        wisdom_assessment = self._assess_wisdom_potential(
            patterns, opinion_text, target_type
        )

        # Generate insights
        insights = self._generate_insights(patterns, scores, wisdom_assessment)

        return {
            "logical_patterns": [self._pattern_to_dict(p) for p in patterns],
            "wisdom_indicators": [
                self._pattern_to_dict(p)
                for p in patterns
                if self._is_wisdom_indicator(p)
            ],
            "complexity_level": self._assess_complexity(
                opinion_text, patterns
            ),
            "logical_coherence_score": scores["logical_coherence"],
            "evidence_quality_score": scores["evidence_quality"],
            "wisdom_potential_score": scores["wisdom_potential"],
            "contributes_to_wisdom": wisdom_assessment[
                "contributes_to_wisdom"
            ],
            "wisdom_card_potential": wisdom_assessment[
                "wisdom_card_potential"
            ],
            "progression_notes": insights["progression_notes"],
            "mentor_insights": insights["mentor_insights"],
        }

    def _detect_patterns(self, text: str) -> List[PatternMatch]:
        """Detect logical patterns in the given text."""
        patterns = []
        text_lower = text.lower()

        for pattern_type, rules in self.pattern_rules.items():
            for rule in rules:
                matches = self._apply_rule(text, text_lower, rule)
                for match in matches:
                    patterns.append(
                        PatternMatch(
                            pattern=pattern_type,
                            confidence=match["confidence"],
                            evidence_text=match["evidence"],
                            explanation=match["explanation"],
                            wisdom_direction=match.get("wisdom_direction"),
                        )
                    )

        return patterns

    def _apply_rule(
        self, text: str, text_lower: str, rule: Dict
    ) -> List[Dict]:
        """Apply a single pattern detection rule."""
        matches = []

        # Check for keyword patterns
        if "keywords" in rule:
            for keyword_set in rule["keywords"]:
                if all(keyword in text_lower for keyword in keyword_set):
                    matches.append(
                        {
                            "confidence": rule.get("confidence", 0.7),
                            "evidence": self._extract_evidence(
                                text, keyword_set
                            ),
                            "explanation": rule.get(
                                "explanation", "Pattern detected"
                            ),
                            "wisdom_direction": rule.get("wisdom_direction"),
                        }
                    )

        # Check for regex patterns
        if "regex" in rule:
            for pattern in rule["regex"]:
                regex_matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in regex_matches:
                    matches.append(
                        {
                            "confidence": rule.get("confidence", 0.8),
                            "evidence": match.group(0),
                            "explanation": rule.get(
                                "explanation", "Pattern detected"
                            ),
                            "wisdom_direction": rule.get("wisdom_direction"),
                        }
                    )

        return matches

    def _extract_evidence(self, text: str, keywords: List[str]) -> str:
        """Extract the relevant text that contains the keywords."""
        sentences = text.split(".")
        for sentence in sentences:
            if all(keyword in sentence.lower() for keyword in keywords):
                return sentence.strip()
        return " ".join(keywords)  # Fallback

    def _calculate_merit_scores(
        self, patterns: List[PatternMatch], text: str
    ) -> Dict[str, float]:
        """Calculate merit-based scores (NOT popularity-based)."""
        wisdom_patterns = [p for p in patterns if self._is_wisdom_indicator(p)]
        fallacy_patterns = [
            p for p in patterns if self._is_fallacy_indicator(p)
        ]

        # Logical coherence: fewer fallacies, more logical structure
        logical_coherence = max(0.0, 1.0 - (len(fallacy_patterns) * 0.2))
        logical_coherence += min(0.3, len(wisdom_patterns) * 0.1)
        logical_coherence = min(1.0, logical_coherence)

        # Evidence quality: presence of evidence-based reasoning
        evidence_score = 0.0
        for pattern in wisdom_patterns:
            if pattern.pattern == LogicalPattern.EVIDENCE_BASED:
                evidence_score += 0.3
            elif (
                pattern.pattern == LogicalPattern.BUILDS_ON_EXISTING_KNOWLEDGE
            ):
                evidence_score += 0.2
        evidence_score = min(1.0, evidence_score)

        # Wisdom potential: nuanced thinking, uncertainty acknowledgment
        wisdom_score = 0.0
        for pattern in wisdom_patterns:
            if pattern.pattern == LogicalPattern.NUANCED_THINKING:
                wisdom_score += 0.25
            elif pattern.pattern == LogicalPattern.ACKNOWLEDGES_UNCERTAINTY:
                wisdom_score += 0.2
            elif (
                pattern.pattern
                == LogicalPattern.CONSIDERS_MULTIPLE_PERSPECTIVES
            ):
                wisdom_score += 0.3
        wisdom_score = min(1.0, wisdom_score)

        return {
            "logical_coherence": logical_coherence,
            "evidence_quality": evidence_score,
            "wisdom_potential": wisdom_score,
        }

    def _assess_complexity(
        self, text: str, patterns: List[PatternMatch]
    ) -> int:
        """Assess the complexity level of the opinion (1-5)."""
        complexity = 1

        # Length and structure
        if len(text) > 200:
            complexity += 1
        if len(text.split(".")) > 3:
            complexity += 1

        # Pattern sophistication
        sophisticated_patterns = [
            LogicalPattern.NUANCED_THINKING,
            LogicalPattern.CONSIDERS_MULTIPLE_PERSPECTIVES,
            LogicalPattern.BUILDS_ON_EXISTING_KNOWLEDGE,
        ]

        for pattern in patterns:
            if pattern.pattern in sophisticated_patterns:
                complexity += 1
                break

        return min(5, complexity)

    def _assess_wisdom_potential(
        self, patterns: List[PatternMatch], text: str, target_type: str
    ) -> Dict:
        """Assess whether this opinion contributes to wisdom."""
        wisdom_patterns = [p for p in patterns if self._is_wisdom_indicator(p)]
        fallacy_patterns = [
            p for p in patterns if self._is_fallacy_indicator(p)
        ]

        # Basic wisdom contribution: more wisdom patterns than fallacies
        contributes_to_wisdom = len(wisdom_patterns) > len(fallacy_patterns)

        # Wisdom card potential: high-quality insights that could help others
        wisdom_card_potential = (
            len(wisdom_patterns) >= 2
            and len(fallacy_patterns) == 0
            and any(
                p.pattern
                in [
                    LogicalPattern.NUANCED_THINKING,
                    LogicalPattern.CONSIDERS_MULTIPLE_PERSPECTIVES,
                ]
                for p in wisdom_patterns
            )
        )

        return {
            "contributes_to_wisdom": contributes_to_wisdom,
            "wisdom_card_potential": wisdom_card_potential,
        }

    def _generate_insights(
        self,
        patterns: List[PatternMatch],
        scores: Dict,
        wisdom_assessment: Dict,
    ) -> Dict:
        """Generate insights and progression notes."""
        progression_notes = []

        # Suggest improvements based on patterns
        fallacy_patterns = [
            p for p in patterns if self._is_fallacy_indicator(p)
        ]
        if fallacy_patterns:
            progression_notes.append(
                f"Consider examining: {', '.join([p.pattern.value for p in fallacy_patterns])}"
            )

        wisdom_patterns = [p for p in patterns if self._is_wisdom_indicator(p)]
        if wisdom_patterns:
            progression_notes.append(
                f"Wisdom indicators present: {', '.join([p.pattern.value for p in wisdom_patterns])}"
            )

        # Mentor insights (from scientific mentors)
        mentor_insights = []
        if scores["evidence_quality"] < 0.3:
            mentor_insights.append(
                "Feynman: 'What I cannot create, I do not understand' - Consider what evidence supports this view"
            )

        if any(
            p.pattern == LogicalPattern.ACKNOWLEDGES_UNCERTAINTY
            for p in patterns
        ):
            mentor_insights.append(
                "Harris: Acknowledging uncertainty is a sign of intellectual honesty"
            )

        return {
            "progression_notes": "; ".join(progression_notes)
            if progression_notes
            else None,
            "mentor_insights": mentor_insights,
        }

    def _is_wisdom_indicator(self, pattern: PatternMatch) -> bool:
        """Check if a pattern indicates wisdom."""
        wisdom_patterns = {
            LogicalPattern.EVIDENCE_BASED,
            LogicalPattern.NUANCED_THINKING,
            LogicalPattern.ACKNOWLEDGES_UNCERTAINTY,
            LogicalPattern.CONSIDERS_MULTIPLE_PERSPECTIVES,
            LogicalPattern.BUILDS_ON_EXISTING_KNOWLEDGE,
        }
        return pattern.pattern in wisdom_patterns

    def _is_fallacy_indicator(self, pattern: PatternMatch) -> bool:
        """Check if a pattern indicates a logical fallacy."""
        fallacy_patterns = {
            LogicalPattern.AD_HOMINEM,
            LogicalPattern.STRAWMAN,
            LogicalPattern.FALSE_DICHOTOMY,
            LogicalPattern.APPEAL_TO_AUTHORITY,
            LogicalPattern.APPEAL_TO_POPULARITY,
            LogicalPattern.HASTY_GENERALIZATION,
            LogicalPattern.CIRCULAR_REASONING,
        }
        return pattern.pattern in fallacy_patterns

    def _pattern_to_dict(self, pattern: PatternMatch) -> Dict:
        """Convert a PatternMatch to a dictionary."""
        return {
            "pattern": pattern.pattern.value,
            "confidence": pattern.confidence,
            "evidence_text": pattern.evidence_text,
            "explanation": pattern.explanation,
            "wisdom_direction": pattern.wisdom_direction,
        }

    def _initialize_pattern_rules(self) -> Dict[LogicalPattern, List[Dict]]:
        """Initialize pattern detection rules."""
        return {
            LogicalPattern.EVIDENCE_BASED: [
                {
                    "keywords": [
                        ["research"],
                        ["evidence"],
                        ["data"],
                        ["studies"],
                        ["study"],
                        ["peer-reviewed"],
                    ],
                    "confidence": 0.8,
                    "explanation": "References evidence or data",
                    "wisdom_direction": "Empirical thinking",
                }
            ],
            LogicalPattern.ACKNOWLEDGES_UNCERTAINTY: [
                {
                    "keywords": [
                        ["might"],
                        ["could"],
                        ["perhaps"],
                        ["maybe"],
                        ["uncertain"],
                        ["not entirely certain"],
                    ],
                    "confidence": 0.7,
                    "explanation": "Acknowledges uncertainty or limitations",
                    "wisdom_direction": "Intellectual humility",
                }
            ],
            LogicalPattern.NUANCED_THINKING: [
                {
                    "keywords": [
                        ["while"],
                        ["however"],
                        ["although"],
                        ["consider"],
                        ["multiple perspectives"],
                    ],
                    "confidence": 0.7,
                    "explanation": "Shows nuanced, multi-faceted thinking",
                    "wisdom_direction": "Sophisticated reasoning",
                }
            ],
            LogicalPattern.CONSIDERS_MULTIPLE_PERSPECTIVES: [
                {
                    "keywords": [
                        ["other perspectives"],
                        ["different views"],
                        ["alternative"],
                        ["on the other hand"],
                    ],
                    "confidence": 0.8,
                    "explanation": "Considers multiple viewpoints",
                    "wisdom_direction": "Intellectual openness",
                }
            ],
            LogicalPattern.BUILDS_ON_EXISTING_KNOWLEDGE: [
                {
                    "keywords": [
                        ["based on"],
                        ["building on"],
                        ["according to"],
                        ["established"],
                    ],
                    "confidence": 0.7,
                    "explanation": "Builds on existing knowledge",
                    "wisdom_direction": "Cumulative understanding",
                }
            ],
            LogicalPattern.AD_HOMINEM: [
                {
                    "keywords": [
                        ["stupid"],
                        ["idiot"],
                        ["moron"],
                        ["fool"],
                        ["dumb"],
                    ],
                    "confidence": 0.9,
                    "explanation": "Attacks the person rather than the argument",
                }
            ],
            LogicalPattern.APPEAL_TO_AUTHORITY: [
                {
                    "keywords": [
                        ["famous person said"],
                        ["celebrity"],
                        ["authority figure"],
                    ],
                    "confidence": 0.8,
                    "explanation": "Appeals to authority rather than evidence",
                }
            ],
            LogicalPattern.APPEAL_TO_POPULARITY: [
                {
                    "keywords": [
                        ["everyone believes"],
                        ["everyone knows"],
                        ["most people"],
                        ["popular opinion"],
                    ],
                    "confidence": 0.8,
                    "explanation": "Appeals to popularity rather than merit",
                }
            ],
        }

    def _initialize_mentor_insights(self) -> Dict[str, List[str]]:
        """Initialize insights from scientific mentors."""
        return {
            "feynman": [
                "What I cannot create, I do not understand",
                "I would rather have questions that can't be answered than answers that can't be questioned",
            ],
            "dawkins": [
                "The feeling of awed wonder that science can give us is one of the highest experiences of which the human psyche is capable"
            ],
            "harris": [
                "The feeling that we call 'I' is an illusion",
                "Acknowledging uncertainty is a sign of intellectual honesty",
            ],
        }
