"""
Database adapter for Discord bot using SQLAlchemy models from trans-db package.
This replaces the raw SQLite operations with proper SQLAlchemy models.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Import from trans-db package
try:
    from trans_db.models.base import Base
    from trans_db.services.db_service import DBService
    from trans_db.services.discord_service import DiscordService
except ImportError as e:
    logging.error(f"Failed to import trans-db package: {e}")
    logging.error("Make sure trans-db package is installed and in PYTHONPATH")
    raise

logger = logging.getLogger(__name__)


class DiscordBotDBAdapter:
    """
    Database adapter for Discord bot using SQLAlchemy models.
    Provides async interface for Discord bot operations.
    """
    
    def __init__(self, database_url: str):
        """Initialize the database adapter."""
        self.database_url = database_url
        self.engine = create_async_engine(database_url)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self._initialized = False
    
    async def initialize(self):
        """Initialize database tables."""
        if self._initialized:
            return
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        self._initialized = True
        logger.info("Discord bot database adapter initialized")
    
    async def get_or_create_discord_user(
        self, discord_id: str, discord_username: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Get or create a Discord user.
        Returns (user_id, profile_data).
        """
        await self.initialize()
        
        async with self.async_session() as session:
            discord_service = DiscordService(session)
            user, discord_account = await discord_service.get_or_create_user_by_discord_id(
                discord_id, discord_username
            )
            
            profile_data = discord_account.provider_data or {}
            return user.id, profile_data
    
    async def update_discord_profile(
        self, discord_id: str, profile_updates: Dict[str, Any]
    ) -> bool:
        """Update Discord user profile."""
        await self.initialize()
        
        async with self.async_session() as session:
            discord_service = DiscordService(session)
            result = await discord_service.update_discord_profile(discord_id, profile_updates)
            return result is not None
    
    async def get_discord_profile(self, discord_id: str) -> Optional[Dict[str, Any]]:
        """Get Discord user profile data."""
        await self.initialize()
        
        async with self.async_session() as session:
            discord_service = DiscordService(session)
            return await discord_service.get_discord_profile(discord_id)
    
    async def cache_transcript(self, video_id: str, transcript_data: Dict[str, Any]) -> bool:
        """Cache a transcript in the database."""
        await self.initialize()
        
        async with self.async_session() as session:
            db_service = DBService(session)
            
            try:
                # Check if transcript already exists
                existing = await db_service.get_transcript_by_video_id(video_id)
                if existing:
                    logger.info(f"Transcript already cached for video_id: {video_id}")
                    return True
                
                # Create YtJob if it doesn't exist
                yt_job = await db_service.get_yt_job_by_video_id(video_id)
                if not yt_job:
                    title = transcript_data.get("title", f"Discord cached: {video_id}")
                    yt_job = await db_service.create_yt_job(
                        video_id=video_id,
                        title=title,
                        user_id=None  # No user association for bot-cached transcripts
                    )
                
                # Create transcript from items
                if "transcript" in transcript_data:
                    await db_service.create_transcript_from_items(
                        video_id=video_id,
                        transcript_items=transcript_data["transcript"]
                    )
                    logger.info(f"Cached transcript for video_id: {video_id}")
                    return True
                else:
                    logger.error(f"No transcript data found for video_id: {video_id}")
                    return False
                    
            except Exception as e:
                logger.error(f"Failed to cache transcript {video_id}: {e}")
                return False
    
    async def get_cached_transcript(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Get a cached transcript."""
        await self.initialize()
        
        async with self.async_session() as session:
            db_service = DBService(session)
            transcript = await db_service.get_transcript_by_video_id(video_id)
            
            if not transcript:
                return None
            
            # Reconstruct the original format for compatibility
            transcript_items = []
            for i, text in enumerate(transcript.text_segments):
                transcript_items.append({
                    "text": text,
                    "start": transcript.start_times[i],
                    "duration": transcript.durations[i]
                })
            
            return {
                "transcript": transcript_items,
                "slurp": transcript.slurp
            }
    
    async def list_cached_transcripts(self) -> List[str]:
        """List all cached transcript video IDs."""
        await self.initialize()
        
        async with self.async_session() as session:
            db_service = DBService(session)
            # This would need a method in DBService to list all video IDs
            # For now, return empty list - you can implement this method
            logger.warning("list_cached_transcripts not fully implemented yet")
            return []
    
    async def log_search(self, discord_id: str, search_term: str) -> bool:
        """Log a search performed by a Discord user."""
        await self.initialize()
        
        async with self.async_session() as session:
            discord_service = DiscordService(session)
            return await discord_service.log_discord_search(discord_id, search_term)
    
    async def close(self):
        """Close the database connection."""
        if hasattr(self, 'engine'):
            await self.engine.dispose()


# Singleton instance for the Discord bot
_db_adapter: Optional[DiscordBotDBAdapter] = None


def get_db_adapter() -> DiscordBotDBAdapter:
    """Get the singleton database adapter instance."""
    global _db_adapter
    
    if _db_adapter is None:
        # Get database URL from environment
        database_url = os.getenv(
            "DATABASE_URL",
            "postgresql+asyncpg://user:password@localhost/knowtrails"
        )
        
        if not database_url.startswith("postgresql+asyncpg://"):
            raise ValueError("DATABASE_URL must use asyncpg driver (postgresql+asyncpg://)")
        
        _db_adapter = DiscordBotDBAdapter(database_url)
    
    return _db_adapter


# Async context manager for database operations
class AsyncDBContext:
    """Context manager for async database operations in Discord bot."""
    
    def __init__(self):
        self.adapter = get_db_adapter()
    
    async def __aenter__(self):
        await self.adapter.initialize()
        return self.adapter
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Don't close the adapter as it's a singleton
        pass


# Helper functions for backward compatibility with existing bot code
async def async_get_or_create_discord_user(discord_id: str, discord_username: str):
    """Helper function for getting/creating Discord users."""
    async with AsyncDBContext() as db:
        return await db.get_or_create_discord_user(discord_id, discord_username)


async def async_cache_transcript(video_id: str, transcript_data: Dict[str, Any]):
    """Helper function for caching transcripts."""
    async with AsyncDBContext() as db:
        return await db.cache_transcript(video_id, transcript_data)


async def async_get_cached_transcript(video_id: str):
    """Helper function for getting cached transcripts."""
    async with AsyncDBContext() as db:
        return await db.get_cached_transcript(video_id)
