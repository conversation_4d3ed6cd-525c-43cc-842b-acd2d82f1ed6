"""
Unified Discord Bot - All Features in One Bot
Combines wisdom assessment, fallacy game, and transcript search functionality.
"""

import logging
import os
from typing import Optional

import discord
from discord.ext import commands

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot configuration
COMMAND_PREFIX = "!"
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")

# Create bot instance with minimal required intents
intents = discord.Intents.default()
intents.message_content = True
# intents.members = True  # Disabled - requires privileged intent

bot = commands.Bot(
    command_prefix=COMMAND_PREFIX,
    intents=intents,
    help_command=None,  # We'll create custom help
)


@bot.event
async def on_ready():
    """Bot startup event."""
    print(f"🤖 Unified KnowTrails Bot logged in as {bot.user}")
    print(f"Bot is in {len(bot.guilds)} guilds")

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"✅ Synced {len(synced)} slash commands")
    except Exception as e:
        print(f"❌ Failed to sync commands: {e}")

    # Show available commands
    prefix_commands = [cmd.name for cmd in bot.commands]
    print(f"📋 Available prefix commands: {prefix_commands}")

    # Show invite URL
    invite_url = discord.utils.oauth_url(
        bot.user.id,
        permissions=discord.Permissions(permissions=2147567616),
        scopes=("bot", "applications.commands"),
    )
    print(f"🔗 Invite URL: {invite_url}")

    print("💡 Available features:")
    print("   🧘 Wisdom Assessment: /opinion, !patterns, !reflect")
    print("   🎮 Fallacy Game: !draw_card, !my_cards, !play_card")
    print("   🔍 Transcript Search: !search, !cache_transcript")


@bot.command(name="help")
async def unified_help(ctx: commands.Context):
    """Display comprehensive help for all bot features."""
    embed = discord.Embed(
        title="🤖 KnowTrails Unified Bot - Help Menu",
        description="All features in one bot: Wisdom Assessment, Fallacy Game, and Transcript Search",
        color=discord.Color.blue(),
    )

    embed.add_field(
        name="🧘 Wisdom Assessment",
        value="`/opinion <type> <text>` - Submit opinion for analysis\n"
        "`!patterns` - View your logical patterns\n"
        "`!reflect <id>` - Reflect on specific opinion\n"
        "`!profile` - View your wisdom profile\n"
        "`!my_trails` - View your wisdom trails",
        inline=False,
    )

    embed.add_field(
        name="🎮 Fallacy Game",
        value="`!draw_card` - Draw a fallacy card\n"
        "`!my_cards` - View your card collection\n"
        "`!play_card <id>` - Play a card in discussion\n"
        "`!game_stats` - View your game statistics",
        inline=False,
    )

    embed.add_field(
        name="🔍 Transcript Search",
        value="`!search <id> <phrase>` - Search transcript\n"
        "`!cache_transcript <id>` - Cache transcript\n"
        "`!list_transcripts` - List cached transcripts\n"
        "`!findmcc <id> <mcc>` - Find by character count",
        inline=False,
    )

    embed.add_field(
        name="📊 Management",
        value="`!my_opinions` - List your opinions\n"
        "`!points` - View point system rules\n"
        "`!reference <id>` - Reference another opinion\n"
        "`!browse_opinions` - Browse community opinions",
        inline=False,
    )

    embed.set_footer(
        text="Use !help_<feature> for detailed help on specific features"
    )

    await ctx.send(embed=embed)


@bot.command(name="ping")
async def ping(ctx: commands.Context):
    """Test bot responsiveness."""
    latency = round(bot.latency * 1000)
    await ctx.send(f"🏓 Pong! Latency: {latency}ms")


# For now, let's start with just the working wisdom commands
# We'll add game and search commands once dependencies are fixed

print("🔄 Loading unified bot commands...")

# Import wisdom assessment functionality
try:
    # Import the wisdom engine and database service
    from discord_trans.game_db_service import GameDBContext
    from discord_trans.wisdom_engine import WisdomEngine

    # Initialize wisdom engine
    wisdom_engine = WisdomEngine()

    print("✅ Wisdom engine loaded")

    # Add core wisdom commands directly to unified bot

    @bot.command(name="opinion")
    async def submit_opinion(
        ctx: commands.Context, target_type: str, *, opinion_text: str
    ):
        """Submit an opinion for wisdom assessment."""
        try:
            async with GameDBContext() as db_service:
                # Get or create user
                user = await db_service.get_or_create_discord_user(
                    str(ctx.author.id), ctx.author.display_name
                )

                # Analyze opinion
                analysis = wisdom_engine.analyze_opinion(
                    opinion_text, target_type
                )

                # Create assessment record
                opinion_assessment = (
                    await db_service.create_opinion_assessment(
                        discord_user_id=str(ctx.author.id),
                        opinion_text=opinion_text,
                        target_type=target_type,
                        analysis_results=analysis,
                    )
                )

                # Create response
                embed = discord.Embed(
                    title="🧘 Opinion Assessment Complete",
                    description=f"**Target**: {target_type.title()}\n**Opinion**: {opinion_text[:100]}{'...' if len(opinion_text) > 100 else ''}",
                    color=discord.Color.blue(),
                )

                embed.add_field(
                    name="📊 Merit Scores",
                    value=f"**Logical Coherence**: {analysis['logical_coherence_score']:.2f}\n"
                    f"**Evidence Quality**: {analysis['evidence_quality_score']:.2f}\n"
                    f"**Wisdom Potential**: {analysis['wisdom_potential_score']:.2f}",
                    inline=True,
                )

                embed.add_field(
                    name="🎯 Assessment",
                    value=f"**Complexity Level**: {analysis['complexity_level']}/5\n"
                    f"**Contributes to Wisdom**: {'Yes' if analysis['contributes_to_wisdom'] else 'No'}\n"
                    f"**Wisdom Card Potential**: {'Yes' if analysis['wisdom_card_potential'] else 'No'}",
                    inline=True,
                )

                embed.set_footer(
                    text=f"Opinion ID: {opinion_assessment.id[:8]} | Unified KnowTrails Bot"
                )

                await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error processing opinion: {str(e)}")

    @bot.command(name="my_opinions")
    async def list_my_opinions(ctx: commands.Context, limit: int = 10):
        """List your recent opinions with IDs."""
        try:
            async with GameDBContext() as db_service:
                user_opinions = await db_service.get_user_opinion_assessments(
                    str(ctx.author.id), limit=min(limit, 20)
                )

                if not user_opinions:
                    await ctx.send(
                        "📝 No opinions submitted yet. Use `!opinion <type> <text>` to start!"
                    )
                    return

                embed = discord.Embed(
                    title=f"📝 {ctx.author.display_name}'s Recent Opinions",
                    description=f"Your last {len(user_opinions)} opinion assessments",
                    color=discord.Color.blue(),
                )

                for i, opinion in enumerate(
                    user_opinions[:5], 1
                ):  # Show top 5
                    wisdom_indicator = (
                        "🌟" if opinion.contributes_to_wisdom else "💭"
                    )
                    embed.add_field(
                        name=f"{wisdom_indicator} Opinion {i} - ID: {opinion.id[:8]}",
                        value=f"**Target**: {opinion.target_type.title()}\n"
                        f"**Opinion**: {opinion.opinion_text[:60]}{'...' if len(opinion.opinion_text) > 60 else ''}\n"
                        f"**Complexity**: {opinion.complexity_level}/5 | **Wisdom**: {'Yes' if opinion.contributes_to_wisdom else 'No'}",
                        inline=False,
                    )

                await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error retrieving opinions: {str(e)}")

    @bot.command(name="profile")
    async def view_profile(ctx: commands.Context):
        """View your wisdom profile."""
        try:
            async with GameDBContext() as db_service:
                user_opinions = await db_service.get_user_opinion_assessments(
                    str(ctx.author.id)
                )

                if not user_opinions:
                    await ctx.send(
                        "🌱 No profile data yet. Submit some opinions to build your wisdom profile!"
                    )
                    return

                # Calculate stats
                total_opinions = len(user_opinions)
                wisdom_contributions = sum(
                    1 for o in user_opinions if o.contributes_to_wisdom
                )
                avg_complexity = (
                    sum(o.complexity_level for o in user_opinions)
                    / total_opinions
                )
                wisdom_rate = (
                    (wisdom_contributions / total_opinions * 100)
                    if total_opinions > 0
                    else 0
                )

                embed = discord.Embed(
                    title=f"🧘 {ctx.author.display_name}'s Wisdom Profile",
                    description="Your journey in wisdom assessment",
                    color=discord.Color.purple(),
                )

                embed.add_field(
                    name="� Statistics",
                    value=f"**Total Opinions**: {total_opinions}\n"
                    f"**Wisdom Contributions**: {wisdom_contributions}\n"
                    f"**Wisdom Rate**: {wisdom_rate:.1f}%\n"
                    f"**Avg Complexity**: {avg_complexity:.1f}/5",
                    inline=True,
                )

                # Determine wisdom level
                if wisdom_rate >= 80:
                    level = "🌟 Wisdom Sage"
                elif wisdom_rate >= 60:
                    level = "🧠 Deep Thinker"
                elif wisdom_rate >= 40:
                    level = "💡 Growing Mind"
                elif wisdom_rate >= 20:
                    level = "🌱 Seeker"
                else:
                    level = "🔍 Explorer"

                embed.add_field(
                    name="🎯 Wisdom Level",
                    value=level,
                    inline=True,
                )

                embed.set_footer(
                    text="Unified KnowTrails Bot - Wisdom through self-examination"
                )

                await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error retrieving profile: {str(e)}")

    print("✅ Core wisdom commands loaded")

except ImportError as e:
    print(f"⚠️ Could not load wisdom engine: {e}")

# Add transcript search functionality
try:
    import json
    import os

    from binary_trans import build_regex_word_boundary
    from binary_trans.search_utils import search_tran_slurp
    from binary_trans.trans_prepper import TranscriptPrepper

    # Transcript configuration
    TRANSCRIPTS_BASE_DIR = os.environ.get(
        "DISCORD_TRANSCRIPTS_BASE_DIR",
        "/home/<USER>/repo/knowtrails/apps/tran-hits/tests/trans_pile",
    )

    def load_transcript_data(transcript_id: str):
        """Load transcript data from file system."""
        try:
            transcript_path = os.path.join(
                TRANSCRIPTS_BASE_DIR, f"{transcript_id}.json"
            )

            if not os.path.exists(transcript_path):
                return (
                    None,
                    f"Transcript file '{transcript_id}.json' not found",
                )

            with open(transcript_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            return data, None

        except Exception as e:
            return None, f"Error loading transcript: {e}"

    @bot.command(name="search")
    async def search_transcript(
        ctx: commands.Context, transcript_id: str, *, phrase: str
    ):
        """Search for a phrase in a transcript - PORTFOLIO DEMO."""
        try:
            # Load transcript
            data, err = load_transcript_data(transcript_id)
            if err:
                await ctx.send(f"❌ {err}")
                return

            # Search using AI-powered transcript processing
            tm = TranscriptPrepper(data["transcript"])
            regex = build_regex_word_boundary([phrase])
            results = search_tran_slurp(tm, regex)

            if results:
                embed = discord.Embed(
                    title="🔍 AI Transcript Search Results",
                    description=f"Found **{len(results)}** matches for '{phrase}' in transcript '{transcript_id}'",
                    color=discord.Color.green(),
                )

                # Show top 3 results
                for i, hit in enumerate(results[:3], 1):
                    embed.add_field(
                        name=f"📍 Match {i} - {hit['start']}s",
                        value=f"```{hit['text'][:150]}{'...' if len(hit['text']) > 150 else ''}```",
                        inline=False,
                    )

                if len(results) > 3:
                    embed.add_field(
                        name="📊 Additional Results",
                        value=f"... and {len(results) - 3} more matches found",
                        inline=False,
                    )

                embed.set_footer(
                    text="KnowTrails AI - Transcript Analysis Portfolio Demo"
                )

                await ctx.send(embed=embed)
            else:
                await ctx.send(
                    f"❌ Phrase '{phrase}' not found in transcript '{transcript_id}'"
                )

        except Exception as e:
            await ctx.send(f"❌ Search error: {str(e)}")

    print("✅ Transcript search loaded")

    @bot.command(name="list_transcripts")
    async def list_available_transcripts(ctx: commands.Context):
        """List available transcripts for search."""
        try:
            if not os.path.exists(TRANSCRIPTS_BASE_DIR):
                await ctx.send(
                    f"❌ Transcript directory not found: {TRANSCRIPTS_BASE_DIR}"
                )
                return

            # Find all JSON files
            transcript_files = [
                f
                for f in os.listdir(TRANSCRIPTS_BASE_DIR)
                if f.endswith(".json")
            ]

            if not transcript_files:
                await ctx.send("❌ No transcript files found")
                return

            embed = discord.Embed(
                title="📁 Available Transcripts",
                description=f"Found {len(transcript_files)} transcript files",
                color=discord.Color.blue(),
            )

            # Show first 10 files
            file_list = []
            for i, filename in enumerate(transcript_files[:10], 1):
                transcript_id = filename.replace(".json", "")
                file_list.append(f"{i}. `{transcript_id}`")

            embed.add_field(
                name="📋 Transcript IDs",
                value="\n".join(file_list),
                inline=False,
            )

            if len(transcript_files) > 10:
                embed.add_field(
                    name="📊 More Files",
                    value=f"... and {len(transcript_files) - 10} more transcripts available",
                    inline=False,
                )

            embed.add_field(
                name="🔍 Usage",
                value="Use `!search <transcript_id> <phrase>` to search",
                inline=False,
            )

            await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error listing transcripts: {str(e)}")

except ImportError as e:
    print(f"⚠️ Could not load transcript engine: {e}")
    from binary_trans.trans_prepper import TranscriptPrepper

    # Transcript configuration
    TRANSCRIPTS_BASE_DIR = os.environ.get(
        "DISCORD_TRANSCRIPTS_BASE_DIR",
        "/home/<USER>/repo/knowtrails/apps/tran-hits/tests/trans_pile",
    )

    def load_transcript_data(transcript_id: str):
        """Load transcript data from file system."""
        try:
            transcript_path = os.path.join(
                TRANSCRIPTS_BASE_DIR, f"{transcript_id}.json"
            )

            if not os.path.exists(transcript_path):
                return (
                    None,
                    f"Transcript file '{transcript_id}.json' not found",
                )

            with open(transcript_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            return data, None

        except Exception as e:
            return None, f"Error loading transcript: {e}"

    @bot.command(name="search")
    async def search_transcript(
        ctx: commands.Context, transcript_id: str, *, phrase: str
    ):
        """Search for a phrase in a transcript - PORTFOLIO DEMO."""
        try:
            # Load transcript
            data, err = load_transcript_data(transcript_id)
            if err:
                await ctx.send(f"❌ {err}")
                return

            # Search using AI-powered transcript processing
            tm = TranscriptPrepper(data["transcript"])
            regex = build_regex_word_boundary([phrase])
            results = search_tran_slurp(tm, regex)

            if results:
                embed = discord.Embed(
                    title=f"🔍 AI Transcript Search Results",
                    description=f"Found **{len(results)}** matches for '{phrase}' in transcript '{transcript_id}'",
                    color=discord.Color.green(),
                )

                # Show top 3 results
                for i, hit in enumerate(results[:3], 1):
                    embed.add_field(
                        name=f"� Match {i} - {hit['start']}s",
                        value=f"```{hit['text'][:150]}{'...' if len(hit['text']) > 150 else ''}```",
                        inline=False,
                    )

                if len(results) > 3:
                    embed.add_field(
                        name="📊 Additional Results",
                        value=f"... and {len(results) - 3} more matches found",
                        inline=False,
                    )

                embed.set_footer(
                    text="KnowTrails AI - Transcript Analysis Portfolio Demo"
                )

                await ctx.send(embed=embed)
            else:
                await ctx.send(
                    f"❌ Phrase '{phrase}' not found in transcript '{transcript_id}'"
                )

        except Exception as e:
            await ctx.send(f"❌ Search error: {str(e)}")

    @bot.command(name="list_transcripts")
    async def list_available_transcripts(ctx: commands.Context):
        """List available transcripts for search."""
        try:
            if not os.path.exists(TRANSCRIPTS_BASE_DIR):
                await ctx.send(
                    f"❌ Transcript directory not found: {TRANSCRIPTS_BASE_DIR}"
                )
                return

            # Find all JSON files
            transcript_files = [
                f
                for f in os.listdir(TRANSCRIPTS_BASE_DIR)
                if f.endswith(".json")
            ]

            if not transcript_files:
                await ctx.send("❌ No transcript files found")
                return

            embed = discord.Embed(
                title="📁 Available Transcripts",
                description=f"Found {len(transcript_files)} transcript files",
                color=discord.Color.blue(),
            )

            # Show first 10 files
            file_list = []
            for i, filename in enumerate(transcript_files[:10], 1):
                transcript_id = filename.replace(".json", "")
                file_list.append(f"{i}. `{transcript_id}`")

            embed.add_field(
                name="📋 Transcript IDs",
                value="\n".join(file_list),
                inline=False,
            )

            if len(transcript_files) > 10:
                embed.add_field(
                    name="📊 More Files",
                    value=f"... and {len(transcript_files) - 10} more transcripts available",
                    inline=False,
                )

            embed.add_field(
                name="🔍 Usage",
                value="Use `!search <transcript_id> <phrase>` to search",
                inline=False,
            )

            await ctx.send(embed=embed)

        except Exception as e:
            await ctx.send(f"❌ Error listing transcripts: {str(e)}")

    print("✅ Transcript search commands loaded")

except ImportError as e:
    print(f"⚠️ Could not load transcript search: {e}")

print("�📋 Unified bot ready with WORKING COMMANDS")
print("🚀 PORTFOLIO PROTOTYPE READY - WISDOM + TRANSCRIPTS")


def run_unified_bot():
    """Run the unified bot with all features."""
    if not DISCORD_BOT_TOKEN:
        print("❌ ERROR: DISCORD_BOT_TOKEN environment variable not set.")
        return

    try:
        print("🚀 Starting Unified KnowTrails Bot...")
        bot.run(DISCORD_BOT_TOKEN)
    except Exception as e:
        print(f"❌ Failed to start unified bot: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    run_unified_bot()
