"""
Discord Bot Launcher
Choose which bot to run: game bot or search bot.
"""

import os
import sys


def main():
    """Launch the appropriate Discord bot based on command line argument."""
    if len(sys.argv) < 2:
        print("🤖 Discord Bot Launcher")
        print("=" * 40)
        print("Usage: python bot_launcher.py <bot_type>")
        print()
        print("Available bots:")
        print("  unified  - Unified Bot (ALL FEATURES) - RECOMMENDED")
        print("  wisdom   - Wisdom Assessment Bot Only")
        print("  game     - Fallacy Card Game Bot Only")
        print("  search   - Transcript Search Bot Only")
        print()
        print("Examples:")
        print("  python bot_launcher.py unified   # Recommended")
        print("  python bot_launcher.py wisdom")
        print("  python bot_launcher.py game")
        print("  python bot_launcher.py search")
        return

    bot_type = sys.argv[1].lower()

    if bot_type == "unified":
        print("🤖 Starting Unified KnowTrails Bot (ALL FEATURES)...")
        from discord_trans.unified_bot import bot

        token = os.environ.get("DISCORD_BOT_TOKEN")
        if not token:
            print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
            return

        try:
            bot.run(token)
        except Exception as e:
            print(f"Failed to start unified bot: {e}")

    elif bot_type == "game":
        print("🎮 Starting Fallacy Card Game Bot...")
        from discord_trans.game_bot import bot

        token = os.environ.get("DISCORD_BOT_TOKEN")
        if not token:
            print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
            return

        try:
            bot.run(token)
        except Exception as e:
            print(f"Failed to start game bot: {e}")

    elif bot_type == "search":
        print("🔍 Starting Transcript Search Bot...")
        from discord_trans.search_bot import bot

        token = os.environ.get("DISCORD_BOT_TOKEN")
        if not token:
            print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
            return

        try:
            bot.run(token)
        except Exception as e:
            print(f"Failed to start search bot: {e}")

    elif bot_type == "wisdom":
        print("🧘 Starting Wisdom Assessment Bot...")
        from discord_trans.opinion_bot import bot

        token = os.environ.get("DISCORD_BOT_TOKEN")
        if not token:
            print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
            return

        try:
            bot.run(token)
        except Exception as e:
            print(f"Failed to start wisdom bot: {e}")

    else:
        print(f"❌ Unknown bot type: {bot_type}")
        print("Available options: unified, wisdom, game, search")
        print("Recommended: unified (includes all features)")


if __name__ == "__main__":
    main()
