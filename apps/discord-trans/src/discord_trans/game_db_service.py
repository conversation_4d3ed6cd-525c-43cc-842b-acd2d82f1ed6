"""
Game database service for Discord bot fallacy card mechanics.
Provides easy access to discord-trans-db models and services.
"""

import logging
import os
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Import from discord-trans-db package
try:
    from discord_trans_db.models import Base
    from discord_trans_db.services import DiscordDBService
except ImportError as e:
    logging.error(f"Failed to import discord-trans-db package: {e}")
    logging.error("Make sure discord-trans-db package is installed with: pip install -e ../../packages/discord-trans-db")
    raise

logger = logging.getLogger(__name__)


class GameDBManager:
    """
    Database manager for Discord bot game mechanics.
    Handles connection to discord-trans-db and provides service access.
    """
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize the game database manager."""
        # For development, use SQLite. For production, use PostgreSQL
        self.database_url = database_url or os.getenv(
            "DISCORD_BOT_DATABASE_URL",
            "sqlite+aiosqlite:///discord_trans_bot.db"
        )
        
        self.engine = create_async_engine(self.database_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )
        self._initialized = False
        logger.info(f"GameDBManager initialized with database: {self.database_url}")
    
    async def initialize(self):
        """Initialize database tables."""
        if self._initialized:
            return
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        self._initialized = True
        logger.info("Game database tables initialized")
    
    async def get_service(self) -> DiscordDBService:
        """Get a database service instance with a new session."""
        await self.initialize()
        session = self.async_session()
        return DiscordDBService(session)
    
    async def close(self):
        """Close the database connection."""
        if hasattr(self, 'engine'):
            await self.engine.dispose()
            logger.info("Game database connection closed")


# Global instance for the Discord bot
_game_db_manager: Optional[GameDBManager] = None


def get_game_db_manager() -> GameDBManager:
    """Get the singleton game database manager instance."""
    global _game_db_manager
    
    if _game_db_manager is None:
        _game_db_manager = GameDBManager()
    
    return _game_db_manager


class GameDBContext:
    """
    Async context manager for game database operations.
    Automatically handles session lifecycle.
    """
    
    def __init__(self):
        self.manager = get_game_db_manager()
        self.service = None
        self.session = None
    
    async def __aenter__(self) -> DiscordDBService:
        """Enter the context and return a database service."""
        await self.manager.initialize()
        self.session = self.manager.async_session()
        self.service = DiscordDBService(self.session)
        return self.service
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the context and close the session."""
        if self.session:
            if exc_type is not None:
                await self.session.rollback()
            else:
                await self.session.commit()
            await self.session.close()


# Convenience functions for Discord bot commands
async def get_game_service() -> DiscordDBService:
    """Get a game database service instance (remember to close session manually)."""
    manager = get_game_db_manager()
    return await manager.get_service()


async def with_game_db(func):
    """
    Decorator to automatically provide database service to Discord bot commands.
    Usage:
    
    @with_game_db
    async def my_command(ctx, db_service: DiscordDBService):
        # Use db_service here
        pass
    """
    async def wrapper(*args, **kwargs):
        async with GameDBContext() as db_service:
            return await func(*args, db_service=db_service, **kwargs)
    return wrapper
