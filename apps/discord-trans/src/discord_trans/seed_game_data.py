"""
<PERSON><PERSON><PERSON> to seed the Discord bot game database with fallacy cards.
Run this once to populate the database with initial game data.
"""

import asyncio
import logging

from game_db_service import GameDBContext

# Import seed functions from discord-trans-db
try:
    from discord_trans_db.seed_data import seed_all_data
except ImportError as e:
    logging.error(f"Failed to import discord-trans-db package: {e}")
    logging.error("Make sure discord-trans-db package is installed with: pip install -e ../../packages/discord-trans-db")
    raise

logger = logging.getLogger(__name__)


async def main():
    """Seed the game database with fallacy cards and examples."""
    logger.info("Starting database seeding...")
    
    try:
        async with GameDBContext() as db_service:
            # Use the session directly for seeding
            session = db_service.db
            
            # Check if we already have fallacies
            existing_fallacies = await db_service.get_all_fallacies()
            if existing_fallacies:
                logger.info(f"Database already has {len(existing_fallacies)} fallacies. Skipping seed.")
                return
            
            # Seed the data
            logger.info("Seeding fallacy cards and examples...")
            result = await seed_all_data(session)
            
            fallacies = result["fallacies"]
            logger.info(f"Successfully seeded {len(fallacies)} fallacy cards:")
            
            # Display what was created
            for name, fallacy in fallacies.items():
                logger.info(f"  - {name} ({fallacy.rarity}, level {fallacy.unlock_meta_level})")
            
            logger.info("Database seeding completed successfully!")
            
    except Exception as e:
        logger.error(f"Error during database seeding: {e}")
        raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
