import json
import os

# Path to the JSON file and Images directory
USER_DATA_FILE = "users.json"
IMAGES_DIR = "Images"


def load_user_data(file_path):
    """Load user data from a JSON file."""
    with open(file_path, "r") as file:
        return json.load(file)


def generate_user_card(user):
    """Generate a user card with user information."""
    username = user.get("username", "Unknown")
    skill_level = user.get("skill_level", "Unknown")
    temperament = user.get("temperament", "Unknown")
    humor = user.get("humor", "Unknown")
    trust_level = user.get("trust_level", "Unknown")

    # Find user picture
    picture_path = os.path.join(IMAGES_DIR, f"{username}.jpg")
    if not os.path.exists(picture_path):
        picture_path = os.path.join(
            IMAGES_DIR, "default.jpg"
        )  # Default picture if user picture doesn't exist

    # Generate and print the user card
    user_card = f"""
    Username: {username}
    Skill Level: {skill_level}
    Temperament: {temperament}
    Humor: {humor}
    Trust Level: {trust_level}
    Picture: {picture_path}
    """
    print(user_card)
    return user_card


# test this
def main():
    users = load_user_data(USER_DATA_FILE)
    for user in users:
        generate_user_card(user)


if __name__ == "__main__":
    main()
