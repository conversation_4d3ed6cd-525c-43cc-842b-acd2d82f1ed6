"""
Discord Bot for Fallacy Card Game
Dedicated bot for the fallacy card collection and progression system.
"""

import logging
import os

import discord
from discord.ext import commands

# Import game database service for fallacy card mechanics
from discord_trans.game_db_service import GameDBContext

# Set up logging
logger = logging.getLogger(__name__)

# --- Configuration ---
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")
COMMAND_PREFIX = "!"

# --- Bot Setup ---
intents = discord.Intents.default()
intents.message_content = True
intents.members = True

bot = commands.Bot(command_prefix=COMMAND_PREFIX, intents=intents)


@bot.event
async def on_ready():
    """Bot startup event."""
    print(f"🎮 Fallacy Card Game Bot logged in as {bot.user}")
    print(f"Bot is in {len(bot.guilds)} guilds")

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} slash commands")
    except Exception as e:
        print(f"Error syncing slash commands: {e}")


@bot.command(name="ping")
async def ping(ctx: commands.Context):
    """Checks bot responsiveness."""
    await ctx.send("🎮 Pong! Fallacy Game Bot is online!")


@bot.command(name="game_help")
async def help_command(ctx):
    """Display help menu for fallacy card game."""
    embed = discord.Embed(
        title="🎴 Fallacy Card Game - Help Menu", color=discord.Color.blue()
    )

    embed.add_field(
        name="🎴 Fallacy Card Game",
        value="`!fallacy` - Discover a random fallacy (+10 XP)\n"
        "`!fallacy list` - Show available fallacies by rarity\n"
        "`!fallacy <name>` - Get details about a specific fallacy\n"
        "`!collect <fallacy>` - Add fallacy to your collection (+25 XP)\n"
        "`!mycards` - View your collected fallacy cards\n"
        "`!progress` - Show your progression stats and level",
        inline=False,
    )

    embed.add_field(
        name="🎮 Game Mechanics",
        value="**XP System**: Discovery (+10), Collection (+25)\n"
        "**Levels**: 1-10 skill progression\n"
        "**Rarity**: Common → Rare → Epic → Legendary\n"
        "**Unlocks**: Higher levels unlock rarer fallacies",
        inline=False,
    )

    embed.add_field(
        name="🚀 Getting Started",
        value="1. Use `!fallacy` to discover your first fallacy\n"
        "2. Use `!collect <name>` to add it to your collection\n"
        "3. Use `!progress` to track your advancement\n"
        "4. Keep discovering to unlock rare fallacies!",
        inline=False,
    )

    embed.set_footer(text="Happy fallacy hunting! 🎯")
    await ctx.send(embed=embed)


# === Fallacy Card Game Commands ===


@bot.command(name="fallacy")
async def fallacy_command(ctx: commands.Context, *, args: str = ""):
    """
    Fallacy card discovery and information commands.
    Usage:
    - !fallacy                 # Discover a random fallacy
    - !fallacy list            # Show available fallacies by rarity
    - !fallacy <name>          # Get details about specific fallacy
    """
    try:
        async with GameDBContext() as db_service:
            # Get or create user
            user = await db_service.get_or_create_discord_user(
                str(ctx.author.id), ctx.author.display_name
            )

            args = args.strip().lower()

            if not args:
                # Discover random fallacy
                fallacy = await db_service.get_random_fallacy(
                    user.unlock_level
                )

                if not fallacy:
                    await ctx.send(
                        "🎴 No fallacies available at your current level! Keep exploring to unlock more."
                    )
                    return

                # Award XP for discovery
                await db_service.award_experience(str(ctx.author.id), 10)

                embed = discord.Embed(
                    title=f"🎴 Fallacy Discovered: {fallacy.name}",
                    description=fallacy.description,
                    color=_get_rarity_color(fallacy.rarity),
                )
                embed.add_field(
                    name="Category", value=fallacy.category, inline=True
                )
                embed.add_field(
                    name="Rarity", value=fallacy.rarity.title(), inline=True
                )
                embed.add_field(
                    name="Unlock Level",
                    value=fallacy.unlock_meta_level,
                    inline=True,
                )
                embed.set_footer(
                    text=f"💰 +10 XP earned! Use !collect {fallacy.name} to add to your collection."
                )

                await ctx.send(embed=embed)

            elif args == "list":
                # Show fallacies by rarity
                all_fallacies = await db_service.get_all_fallacies()

                if not all_fallacies:
                    await ctx.send(
                        "🎴 No fallacies found in the database. Run the seed script first!"
                    )
                    return

                # Group by rarity
                by_rarity = {}
                for fallacy in all_fallacies:
                    if fallacy.unlock_meta_level <= user.unlock_level:
                        rarity = fallacy.rarity
                        if rarity not in by_rarity:
                            by_rarity[rarity] = []
                        by_rarity[rarity].append(fallacy.name)

                embed = discord.Embed(
                    title="🎴 Available Fallacy Cards",
                    description=f"Showing fallacies available at your level ({user.unlock_level})",
                    color=discord.Color.blue(),
                )

                rarity_order = ["common", "rare", "epic", "legendary"]
                for rarity in rarity_order:
                    if rarity in by_rarity:
                        fallacy_list = ", ".join(by_rarity[rarity])
                        embed.add_field(
                            name=f"{_get_rarity_emoji(rarity)} {rarity.title()}",
                            value=fallacy_list,
                            inline=False,
                        )

                await ctx.send(embed=embed)

            else:
                # Get specific fallacy details
                fallacy = await db_service.get_fallacy_by_name(args)

                if not fallacy:
                    await ctx.send(
                        f"🔍 Fallacy '{args}' not found. Use `!fallacy list` to see available fallacies."
                    )
                    return

                if fallacy.unlock_meta_level > user.unlock_level:
                    await ctx.send(
                        f"🔒 '{fallacy.name}' is locked. Reach unlock level {fallacy.unlock_meta_level} to access it."
                    )
                    return

                embed = discord.Embed(
                    title=f"🎴 {fallacy.name}",
                    description=fallacy.description,
                    color=_get_rarity_color(fallacy.rarity),
                )
                embed.add_field(
                    name="Category", value=fallacy.category, inline=True
                )
                embed.add_field(
                    name="Rarity", value=fallacy.rarity.title(), inline=True
                )
                embed.add_field(
                    name="Unlock Level",
                    value=fallacy.unlock_meta_level,
                    inline=True,
                )

                embed.set_footer(
                    text=f"Use !collect {fallacy.name} to add to your collection."
                )

                await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in fallacy command: {e}")
        await ctx.send(
            "❌ An error occurred while processing the fallacy command."
        )


@bot.command(name="collect")
async def collect_fallacy(ctx: commands.Context, *, fallacy_name: str):
    """
    Collect a fallacy card to your personal collection.
    Usage: !collect <fallacy_name>
    """
    try:
        async with GameDBContext() as db_service:
            # Get the fallacy
            fallacy = await db_service.get_fallacy_by_name(
                fallacy_name.strip()
            )

            if not fallacy:
                await ctx.send(
                    f"🔍 Fallacy '{fallacy_name}' not found. Use `!fallacy list` to see available fallacies."
                )
                return

            # Check if user can access this fallacy
            user = await db_service.get_or_create_discord_user(
                str(ctx.author.id), ctx.author.display_name
            )

            if fallacy.unlock_meta_level > user.unlock_level:
                await ctx.send(
                    f"🔒 '{fallacy.name}' is locked. Reach unlock level {fallacy.unlock_meta_level} to access it."
                )
                return

            # Try to add to collection
            added = await db_service.add_fallacy_to_user_collection(
                str(ctx.author.id), fallacy.id
            )

            if added:
                # Award XP for collecting
                await db_service.award_experience(str(ctx.author.id), 25)

                embed = discord.Embed(
                    title="✅ Fallacy Collected!",
                    description=f"Added **{fallacy.name}** to your collection!",
                    color=_get_rarity_color(fallacy.rarity),
                )
                embed.add_field(
                    name="Rarity", value=fallacy.rarity.title(), inline=True
                )
                embed.add_field(name="XP Earned", value="25", inline=True)
                embed.set_footer(text="Use !mycards to view your collection.")

                await ctx.send(embed=embed)
            else:
                await ctx.send(
                    f"📚 You already have **{fallacy.name}** in your collection!"
                )

    except Exception as e:
        logger.error(f"Error in collect command: {e}")
        await ctx.send("❌ An error occurred while collecting the fallacy.")


@bot.command(name="mycards")
async def my_cards(ctx: commands.Context):
    """
    Display your collected fallacy cards.
    Usage: !mycards
    """
    try:
        async with GameDBContext() as db_service:
            collected_fallacies = (
                await db_service.get_user_collected_fallacies(
                    str(ctx.author.id)
                )
            )

            if not collected_fallacies:
                embed = discord.Embed(
                    title="📚 Your Fallacy Collection",
                    description="You haven't collected any fallacies yet! Use `!fallacy` to discover some.",
                    color=discord.Color.light_grey(),
                )
                embed.set_footer(text="Start your collection with !fallacy")
                await ctx.send(embed=embed)
                return

            # Group by rarity
            by_rarity = {}
            for fallacy in collected_fallacies:
                rarity = fallacy.rarity
                if rarity not in by_rarity:
                    by_rarity[rarity] = []
                by_rarity[rarity].append(fallacy.name)

            embed = discord.Embed(
                title="📚 Your Fallacy Collection",
                description=f"You have collected {len(collected_fallacies)} fallacies!",
                color=discord.Color.green(),
            )

            rarity_order = ["common", "rare", "epic", "legendary"]
            for rarity in rarity_order:
                if rarity in by_rarity:
                    fallacy_list = ", ".join(by_rarity[rarity])
                    embed.add_field(
                        name=f"{_get_rarity_emoji(rarity)} {rarity.title()} ({len(by_rarity[rarity])})",
                        value=fallacy_list,
                        inline=False,
                    )

            embed.set_footer(text="Use !progress to see detailed stats")
            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in mycards command: {e}")
        await ctx.send(
            "❌ An error occurred while retrieving your collection."
        )


@bot.command(name="progress")
async def progress(ctx: commands.Context):
    """
    Display your progression stats and achievements.
    Usage: !progress
    """
    try:
        async with GameDBContext() as db_service:
            stats = await db_service.get_user_progress_stats(
                str(ctx.author.id)
            )

            embed = discord.Embed(
                title=f"📊 Progress Report: {stats['discord_username']}",
                color=discord.Color.blue(),
            )

            # Main stats
            embed.add_field(
                name="🎯 Level & XP",
                value=f"**Skill Level:** {stats['skill_level']}/10\n"
                f"**XP:** {stats['experience_points']}\n"
                f"**Next Level:** {stats['xp_to_next_level']} XP",
                inline=True,
            )

            embed.add_field(
                name="🔓 Access",
                value=f"**Unlock Level:** {stats['unlock_level']}\n"
                f"**Available Cards:** {stats['available_count']}\n"
                f"**Collected:** {stats['collected_count']}",
                inline=True,
            )

            embed.add_field(
                name="📈 Collection",
                value=f"**Completion:** {stats['collection_percentage']:.1f}%\n"
                f"**Total Cards:** {stats['collected_count']}/{stats['available_count']}",
                inline=True,
            )

            # Rarity breakdown
            if stats["rarity_breakdown"]:
                rarity_text = []
                for rarity, count in stats["rarity_breakdown"].items():
                    emoji = _get_rarity_emoji(rarity)
                    rarity_text.append(f"{emoji} {rarity.title()}: {count}")

                embed.add_field(
                    name="🎴 Collection Breakdown",
                    value="\n".join(rarity_text),
                    inline=False,
                )

            # Progress bar
            progress_bar = _create_progress_bar(stats["collection_percentage"])
            embed.add_field(name="Progress", value=progress_bar, inline=False)

            embed.set_footer(text="Keep discovering fallacies to level up!")
            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in progress command: {e}")
        await ctx.send("❌ An error occurred while retrieving your progress.")


# === Helper Functions ===


def _get_rarity_color(rarity: str) -> discord.Color:
    """Get Discord color based on fallacy rarity."""
    colors = {
        "common": discord.Color.light_grey(),
        "rare": discord.Color.blue(),
        "epic": discord.Color.purple(),
        "legendary": discord.Color.gold(),
    }
    return colors.get(rarity, discord.Color.default())


def _get_rarity_emoji(rarity: str) -> str:
    """Get emoji based on fallacy rarity."""
    emojis = {"common": "⚪", "rare": "🔵", "epic": "🟣", "legendary": "🟡"}
    return emojis.get(rarity, "⚫")


def _create_progress_bar(percentage: float, length: int = 10) -> str:
    """Create a visual progress bar for collection completion."""
    filled = int(percentage / 100 * length)
    empty = length - filled
    bar = "█" * filled + "░" * empty
    return f"{bar} {percentage:.1f}%"


# --- Run the Bot ---
if __name__ == "__main__":
    import traceback

    if not DISCORD_BOT_TOKEN:
        print("ERROR: DISCORD_BOT_TOKEN environment variable not set.")
    else:
        try:
            bot.run(DISCORD_BOT_TOKEN)
        except Exception as e:
            print(f"Failed to start bot: {e}")
            print(traceback.format_exc())
