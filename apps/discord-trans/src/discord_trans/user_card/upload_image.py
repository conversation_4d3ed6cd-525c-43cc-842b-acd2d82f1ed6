import os

from discord_trans.user_card.generate_card import get_image_dir, load_user_data


async def upload_image(bot, ctx, username):
    user = load_user_data(username)

    if user is None:
        await ctx.send("User not found")
        return

    await ctx.send("Please upload a new image")

    def check(msg):
        return msg.author == ctx.author and msg.attachments

    msg = await bot.wait_for("message", check=check)
    attachment = msg.attachments[0]
    image_file = os.path.join(get_image_dir(), f"{username}.png")
    await attachment.save(image_file)
