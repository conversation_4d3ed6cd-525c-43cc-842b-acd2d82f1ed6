import json
import os
import random
import shutil
import sqlite3

import discord

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
# PROFILE_PATH = os.path.join(BASE_DIR, "profiles.json") # No longer needed
IMG_DIR = os.path.join(BASE_DIR, "img")

# Define DB_PATH relative to APP_ROOT, similar to trans_bot.py
# Assuming generate_card.py is in apps/discord-trans/src/discord_trans/user_card/
# and discord_bot_cache.db is in apps/discord-trans/
APP_ROOT = os.path.abspath(
    os.path.join(BASE_DIR, "..", "..")
)  # Should be apps/discord-trans/
DB_PATH = os.path.join(APP_ROOT, "discord_bot_cache.db")


def get_db_connection():
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn


def get_image_dir():
    return IMG_DIR


def load_user_data(discord_id: str):
    conn = get_db_connection()
    c = conn.cursor()
    c.execute(
        "SELECT * FROM discord_users WHERE discord_id = ?", (discord_id,)
    )
    user_data = c.fetchone()
    conn.close()
    if user_data:
        user_dict = dict(user_data)
        if user_dict.get("preferred_topics"):
            user_dict["preferred_topics"] = json.loads(
                user_dict["preferred_topics"]
            )
        else:
            user_dict["preferred_topics"] = []
        return user_dict
    return None


def update_user_profile(discord_id: str, discord_username: str, updates: dict):
    conn = get_db_connection()
    c = conn.cursor()

    # Prepare updates for DB insertion
    db_updates = {}
    for key, value in updates.items():
        if key == "preferred_topics":
            db_updates[key] = json.dumps(value)  # Store list as JSON string
        else:
            db_updates[key] = value

    # Check if user exists
    c.execute(
        "SELECT * FROM discord_users WHERE discord_id = ?", (discord_id,)
    )
    existing_user = c.fetchone()

    if existing_user:
        # Update existing user
        set_clauses = ", ".join([f"{key} = ?" for key in db_updates.keys()])
        values = list(db_updates.values())
        values.append(discord_id)
        c.execute(
            f"UPDATE discord_users SET {set_clauses} WHERE discord_id = ?",
            tuple(values),
        )
    else:
        # Create new user with default values and provided updates
        new_user_data = {
            "discord_id": discord_id,
            "discord_username": discord_username,
            "bot_profile_name": discord_username,  # Default to discord_username
            "skill_level": "unknown",
            "temperament": "unknown",
            "humor": "unknown",
            "trust_level": "unknown",
            "preferred_topics": "[]",
            "communication_style": "neutral",
            "sentiment_tendency": "neutral",
            "knowtrail_user_id": None,  # Default to None
        }
        new_user_data.update(db_updates)

        columns = ", ".join(new_user_data.keys())
        placeholders = ", ".join(["?"] * len(new_user_data))
        values = tuple(new_user_data.values())
        c.execute(
            f"INSERT INTO discord_users ({columns}) VALUES ({placeholders})",
            values,
        )

    conn.commit()
    conn.close()
    return True


def generate_user_card_ascii(discord_id: str):
    user = load_user_data(discord_id)

    username_display = (
        user.get("discord_username", "unknown") if user else "unknown"
    )
    bot_profile_name = user.get("bot_profile_name", "N/A") if user else "N/A"
    skill_level = user.get("skill_level", "unknown") if user else "unknown"
    temperament = user.get("temperament", "unknown") if user else "unknown"
    humor = user.get("humor", "unknown") if user else "unknown"
    trust_level = user.get("trust_level", "unknown") if user else "unknown"

    image_file = os.path.join(
        IMG_DIR, f"{discord_id}.png"
    )  # Use discord_id for image
    if not os.path.exists(image_file):
        image_file = os.path.join(IMG_DIR, "default.png")

    image_exists = "no"
    if os.path.exists(image_file):
        image_exists = "yes"

    card = f"""
    ┌───────────────────────────────┐
    │ Discord ID   : {discord_id}
    │ Username     : {username_display}
    │ Profile Name : {bot_profile_name}
    │ Skill Level  : {skill_level}
    │ Temperament  : {temperament}
    │ Humor        : {humor}
    │ Trust Level  : {trust_level}
    │ Image        : {image_exists}
    └───────────────────────────────┘
    """

    print(card)

    return card


def generate_user_card_embed(
    discord_id: str, discord_user_obj: discord.Member = None
):
    """Generate a Discord embed card for a user.

    Args:
        discord_id: The unique Discord ID of the user.
        discord_user_obj: The Discord member object containing role information and current username.
    """
    # Check user's roles if discord_user_obj is provided
    is_power_user = False
    discord_username = "unknown"
    if discord_user_obj:
        is_power_user = any(
            role.name.lower() == "poweruser" for role in discord_user_obj.roles
        )
        discord_username = (
            discord_user_obj.name
        )  # Get current Discord username

    user = load_user_data(discord_id)

    if user is None:
        print(f"Creating default user profile for Discord ID: {discord_id}")
        # Create a default profile in DB if not found
        default_profile_updates = {
            "discord_username": discord_username,
            "bot_profile_name": discord_username,
            "skill_level": "unknown",
            "temperament": "unknown",
            "humor": "unknown",
            "trust_level": "unknown",
            "preferred_topics": [],
            "communication_style": "neutral",
            "sentiment_tendency": "neutral",
            "knowtrail_user_id": None,
        }
        update_user_profile(
            discord_id, discord_username, default_profile_updates
        )
        user = load_user_data(discord_id)  # Reload the newly created user

    image_file = os.path.join(
        IMG_DIR, f"{discord_id}.png"
    )  # Use discord_id for image path

    if not os.path.exists(image_file):
        options_dir = "/home/<USER>/repo/knowtrails/apps/discord-trans/src/discord_trans/images/options/"
        options_files = [
            f for f in os.listdir(options_dir) if f.endswith(".png")
        ]
        if options_files:  # Ensure there are options to choose from
            random_option = random.choice(options_files)
            shutil.copy(os.path.join(options_dir, random_option), image_file)
        else:
            print(
                f"Warning: No image options found in {options_dir}. Using default.png if available."
            )

    if not os.path.exists(image_file):
        image_file = os.path.join(IMG_DIR, "default.png")
        if not os.path.exists(image_file):
            print(
                f"Error: Default image {image_file} not found. Embed might not display image."
            )

    embed = discord.Embed(
        title=f"{user.get('bot_profile_name', user.get('discord_username', 'Unknown'))}'s Profile",
        color=0x00FF00,
    )
    embed.set_thumbnail(
        url=f"attachment://{discord_id}.png"
    )  # Use discord_id for attachment URL
    embed.add_field(name="Discord ID", value=discord_id, inline=False)
    embed.add_field(
        name="Discord Username",
        value=user.get("discord_username", "unknown"),
        inline=False,
    )
    embed.add_field(
        name="Bot Profile Name",
        value=user.get("bot_profile_name", "N/A"),
        inline=False,
    )
    embed.add_field(
        name="Skill Level",
        value=user.get("skill_level", "unknown"),
        inline=False,
    )
    embed.add_field(
        name="Temperament",
        value=user.get("temperament", "unknown"),
        inline=False,
    )
    embed.add_field(
        name="Humor", value=user.get("humor", "unknown"), inline=False
    )
    embed.add_field(
        name="Trust Level",
        value=user.get("trust_level", "unknown"),
        inline=False,
    )
    embed.add_field(
        name="Preferred Topics",
        value=", ".join(user.get("preferred_topics", ["None"])),
        inline=False,
    )
    embed.add_field(
        name="Communication Style",
        value=user.get("communication_style", "neutral"),
        inline=False,
    )
    embed.add_field(
        name="Sentiment Tendency",
        value=user.get("sentiment_tendency", "neutral"),
        inline=False,
    )
    if user.get("knowtrail_user_id"):
        embed.add_field(
            name="Knowtrail User ID",
            value=user.get("knowtrail_user_id"),
            inline=False,
        )

    file = discord.File(
        image_file, filename=f"{discord_id}.png"
    )  # Use discord_id for filename
    embed.set_image(
        url=f"attachment://{discord_id}.png"
    )  # Use discord_id for attachment URL

    return file, embed


def main(discord_id: str):  # Changed to discord_id
    generate_user_card_ascii(discord_id)


if __name__ == "__main__":
    # This part needs to be updated to use a dummy discord_id for testing
    # Or removed if not intended for direct execution without a Discord context
    main("123456789012345678")  # Example Discord ID
