"""
Discord Bot for Wisdom-Centered Opinion Assessment
Dedicated bot for opinion assessment, pattern recognition, and wisdom cultivation.
"""

import logging
import os

import discord
from discord.ext import commands

# Import game database service and wisdom engine
from discord_trans.game_db_service import GameDBContext
from discord_trans.wisdom_engine import WisdomEngine

# Set up logging
logger = logging.getLogger(__name__)

# --- Configuration ---
DISCORD_BOT_TOKEN = os.environ.get("DISCORD_BOT_TOKEN")
COMMAND_PREFIX = "!"

# --- Bot Setup ---
intents = discord.Intents.default()
intents.message_content = True
# Note: members intent requires privileged access, removing for now
# intents.members = True

bot = commands.Bot(command_prefix=COMMAND_PREFIX, intents=intents)

# Initialize wisdom engine
wisdom_engine = WisdomEngine()


@bot.event
async def on_ready():
    """Bot startup event."""
    print(f"🧘 Wisdom Assessment Bot logged in as {bot.user}")
    print(f"Bot is in {len(bot.guilds)} guilds")
    print(f"Available prefix commands: {[cmd.name for cmd in bot.commands]}")

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"✅ Synced {len(synced)} slash commands")
    except Exception as e:
        print(f"❌ Error syncing slash commands: {e}")

    # Generate invite URL
    permissions = discord.Permissions(
        send_messages=True,
        embed_links=True,
        read_message_history=True,
        use_application_commands=True,
    )
    invite_url = discord.utils.oauth_url(bot.user.id, permissions=permissions)
    print(f"🔗 Invite URL: {invite_url}")

    print("💡 Available commands:")
    print("   Prefix: !help_wisdom, !opinion, !patterns, !reflect")
    print("   Slash: /opinion, /patterns, /reflect, /help_wisdom")


@bot.command(name="ping")
async def ping(ctx: commands.Context):
    """Checks bot responsiveness."""
    await ctx.send("🧘 Pong! Wisdom Assessment Bot is online!")


@bot.command(name="help_wisdom")
async def help_wisdom_command(ctx):
    """Display help menu for wisdom assessment system."""
    embed = discord.Embed(
        title="🧘 Wisdom Assessment System - Help Menu",
        color=discord.Color.purple(),
    )

    embed.add_field(
        name="🎯 Opinion Assessment",
        value="`!opinion <target> <text>` - Submit opinion on idea/thing/person\n"
        "`!reflect <opinion_id>` - Reflect on patterns in your opinion\n"
        "`!patterns` - View your logical pattern history\n"
        "`!profile` - View your complete wisdom profile",
        inline=False,
    )

    embed.add_field(
        name="📝 Opinion Management",
        value="`!my_opinions [limit]` - List your recent opinions with IDs\n"
        "`!browse_opinions [type]` - Browse community opinions\n"
        "`!reference <opinion_id> [comment]` - Reference another opinion\n"
        "`!points` - View point system rules and earning conditions",
        inline=False,
    )

    embed.add_field(
        name="🌟 Wisdom Trails & Profiles",
        value="`!wisdom_trail <topic>` - Explore wisdom progression on topic\n"
        "`!my_trails` - View your favorite wisdom trails\n"
        "`!vp [@user]` - View user profile (ASCII format)\n"
        "`!vudex [@user]` - View user profile (rich embed)",
        inline=False,
    )

    embed.add_field(
        name="🧠 Philosophy",
        value="**Merit over Popularity**: We value logical coherence and evidence\n"
        "**Pattern Recognition**: Fallacies are misalignment indicators\n"
        "**Wisdom Cultivation**: From opinions to insights to wisdom\n"
        "**Anti-Celebrity**: Fame doesn't equal truth",
        inline=False,
    )

    embed.add_field(
        name="🚀 Getting Started",
        value="1. Use `!opinion idea 'Climate change is...'` to submit an opinion\n"
        "2. Use `!patterns` to see what logical patterns were detected\n"
        "3. Use `!reflect <id>` to reflect on your thinking patterns\n"
        "4. Watch your wisdom grow through self-examination!",
        inline=False,
    )

    embed.set_footer(text="Wisdom through recognition, not collection 🧘✨")
    await ctx.send(embed=embed)


# === Opinion Assessment Commands ===


@bot.command(name="opinion")
async def submit_opinion(
    ctx: commands.Context, target_type: str, *, opinion_text: str
):
    """
    Submit an opinion for wisdom assessment.
    Usage: !opinion <target_type> <opinion_text>
    Target types: idea, thing, person, concept
    """
    try:
        # Validate target type
        valid_targets = ["idea", "thing", "person", "concept"]
        if target_type.lower() not in valid_targets:
            await ctx.send(
                f"🎯 Invalid target type. Use one of: {', '.join(valid_targets)}\n"
                f"Example: `!opinion idea Climate change is primarily caused by human activity`"
            )
            return

        async with GameDBContext() as db_service:
            # Get or create user
            user = await db_service.get_or_create_discord_user(
                str(ctx.author.id), ctx.author.display_name
            )

            # Analyze opinion with wisdom engine
            analysis = wisdom_engine.analyze_opinion(
                opinion_text, target_type.lower()
            )

            # Create opinion assessment record
            opinion_assessment = await db_service.create_opinion_assessment(
                discord_user_id=str(ctx.author.id),
                opinion_text=opinion_text,
                target_type=target_type.lower(),
                analysis_results=analysis,
            )

            # Update user stats
            await db_service.increment_user_opinion_stats(str(ctx.author.id))

            # Create response embed
            embed = discord.Embed(
                title="🧘 Opinion Assessment Complete",
                description=f"**Target**: {target_type.title()}\n**Opinion**: {opinion_text[:100]}{'...' if len(opinion_text) > 100 else ''}",
                color=discord.Color.blue(),
            )

            # Add analysis results
            embed.add_field(
                name="📊 Merit Scores",
                value=f"**Logical Coherence**: {analysis['logical_coherence_score']:.2f}\n"
                f"**Evidence Quality**: {analysis['evidence_quality_score']:.2f}\n"
                f"**Wisdom Potential**: {analysis['wisdom_potential_score']:.2f}",
                inline=True,
            )

            embed.add_field(
                name="🎯 Assessment",
                value=f"**Complexity Level**: {analysis['complexity_level']}/5\n"
                f"**Contributes to Wisdom**: {'Yes' if analysis['contributes_to_wisdom'] else 'No'}\n"
                f"**Wisdom Card Potential**: {'Yes' if analysis['wisdom_card_potential'] else 'No'}",
                inline=True,
            )

            # Add pattern summary
            patterns = analysis.get("logical_patterns", [])
            if patterns:
                pattern_summary = []
                for pattern in patterns[:3]:  # Show top 3 patterns
                    pattern_summary.append(
                        f"• {pattern['pattern'].replace('_', ' ').title()}"
                    )

                embed.add_field(
                    name="🔍 Patterns Detected",
                    value="\n".join(pattern_summary)
                    + (
                        f"\n... and {len(patterns) - 3} more"
                        if len(patterns) > 3
                        else ""
                    ),
                    inline=False,
                )

            # Add mentor insights if any
            mentor_insights = analysis.get("mentor_insights", [])
            if mentor_insights:
                embed.add_field(
                    name="🎓 Mentor Insights",
                    value=mentor_insights[0],  # Show first insight
                    inline=False,
                )

            embed.add_field(
                name="🔄 Next Steps",
                value=f"Use `!reflect {opinion_assessment.id[:8]}` to reflect on this opinion\n"
                f"Use `!patterns` to see your pattern history",
                inline=False,
            )

            embed.set_footer(
                text=f"Opinion ID: {opinion_assessment.id[:8]} | Wisdom through self-examination"
            )
            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in submit_opinion: {e}")
        await ctx.send(f"❌ Error processing opinion: {str(e)}")


@bot.command(name="patterns")
async def view_patterns(ctx: commands.Context):
    """View your logical pattern history."""
    try:
        async with GameDBContext() as db_service:
            # Get user's opinion assessments
            user_opinions = await db_service.get_user_opinion_assessments(
                str(ctx.author.id), limit=10
            )

            if not user_opinions:
                await ctx.send(
                    "🔍 No opinions assessed yet. Use `!opinion` to submit your first opinion!"
                )
                return

            embed = discord.Embed(
                title="🔍 Your Logical Pattern History",
                description="Recent patterns detected in your opinions",
                color=discord.Color.green(),
            )

            # Aggregate patterns
            pattern_counts = {}
            wisdom_indicators = 0
            fallacy_indicators = 0

            for opinion in user_opinions:
                if opinion.logical_patterns:
                    for pattern_data in opinion.logical_patterns:
                        pattern_name = pattern_data.get("pattern", "unknown")
                        pattern_counts[pattern_name] = (
                            pattern_counts.get(pattern_name, 0) + 1
                        )

                        # Count wisdom vs fallacy indicators
                        if pattern_name in [
                            "evidence_based",
                            "nuanced_thinking",
                            "acknowledges_uncertainty",
                            "considers_multiple_perspectives",
                            "builds_on_existing_knowledge",
                        ]:
                            wisdom_indicators += 1
                        elif pattern_name in [
                            "ad_hominem",
                            "strawman",
                            "false_dichotomy",
                            "appeal_to_authority",
                            "appeal_to_popularity",
                        ]:
                            fallacy_indicators += 1

            # Show top patterns
            sorted_patterns = sorted(
                pattern_counts.items(), key=lambda x: x[1], reverse=True
            )
            pattern_list = []
            for pattern, count in sorted_patterns[:5]:
                pattern_display = pattern.replace("_", " ").title()
                pattern_list.append(f"• {pattern_display}: {count}x")

            if pattern_list:
                embed.add_field(
                    name="📈 Most Common Patterns",
                    value="\n".join(pattern_list),
                    inline=False,
                )

            embed.add_field(
                name="🧘 Wisdom Balance",
                value=f"**Wisdom Indicators**: {wisdom_indicators}\n"
                f"**Misalignment Patterns**: {fallacy_indicators}\n"
                f"**Balance Ratio**: {wisdom_indicators / (fallacy_indicators + 1):.1f}:1",
                inline=True,
            )

            embed.add_field(
                name="📊 Assessment Stats",
                value=f"**Total Opinions**: {len(user_opinions)}\n"
                f"**Avg Complexity**: {sum(o.complexity_level for o in user_opinions) / len(user_opinions):.1f}/5\n"
                f"**Wisdom Contributions**: {sum(1 for o in user_opinions if o.contributes_to_wisdom)}",
                inline=True,
            )

            embed.set_footer(
                text="Patterns help you recognize thinking habits • Use !reflect to examine specific opinions"
            )
            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in view_patterns: {e}")
        await ctx.send(f"❌ Error retrieving patterns: {str(e)}")


@bot.command(name="reflect")
async def reflect_on_opinion(ctx: commands.Context, opinion_id: str):
    """
    Reflect on a specific opinion and its patterns.
    Usage: !reflect <opinion_id>
    """
    try:
        async with GameDBContext() as db_service:
            # Get the opinion assessment
            opinion = await db_service.get_opinion_assessment_by_id(opinion_id)

            if not opinion:
                await ctx.send(f"🔍 Opinion with ID '{opinion_id}' not found.")
                return

            # Check if user owns this opinion
            if opinion.discord_user_id != str(ctx.author.id):
                await ctx.send("🔒 You can only reflect on your own opinions.")
                return

            embed = discord.Embed(
                title="🧘 Opinion Reflection",
                description=f"**Original Opinion**: {opinion.opinion_text}",
                color=discord.Color.purple(),
            )

            # Show detailed patterns
            if opinion.logical_patterns:
                pattern_details = []
                for pattern_data in opinion.logical_patterns:
                    pattern_name = (
                        pattern_data.get("pattern", "unknown")
                        .replace("_", " ")
                        .title()
                    )
                    confidence = pattern_data.get("confidence", 0)
                    explanation = pattern_data.get(
                        "explanation", "No explanation"
                    )

                    pattern_details.append(
                        f"**{pattern_name}** ({confidence:.0%})\n{explanation}"
                    )

                embed.add_field(
                    name="🔍 Detected Patterns",
                    value="\n\n".join(pattern_details[:3]),  # Show top 3
                    inline=False,
                )

            # Show progression notes
            if opinion.progression_notes:
                embed.add_field(
                    name="📈 Growth Opportunities",
                    value=opinion.progression_notes,
                    inline=False,
                )

            # Reflection prompts
            embed.add_field(
                name="🤔 Reflection Questions",
                value="• What assumptions did I make?\n"
                "• What evidence supports my view?\n"
                "• What would someone who disagrees say?\n"
                "• How might I refine this opinion?",
                inline=False,
            )

            embed.set_footer(
                text=f"Opinion ID: {opinion_id} | Self-examination leads to wisdom"
            )
            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in reflect_on_opinion: {e}")
        await ctx.send(f"❌ Error retrieving opinion: {str(e)}")


@bot.command(name="profile")
async def view_profile(ctx: commands.Context):
    """View your wisdom profile and progression (prefix command)."""
    try:
        async with GameDBContext() as db_service:
            # Get user data
            user = await db_service.get_or_create_discord_user(
                str(ctx.author.id), ctx.author.display_name
            )

            # Get recent opinions for additional stats
            user_opinions = await db_service.get_user_opinion_assessments(
                str(ctx.author.id), limit=50
            )

            # Calculate wisdom stats
            wisdom_contributions = sum(
                1 for o in user_opinions if o.contributes_to_wisdom
            )
            avg_complexity = (
                sum(o.complexity_level for o in user_opinions)
                / len(user_opinions)
                if user_opinions
                else 0
            )

            # Create profile embed
            embed = discord.Embed(
                title=f"🧘 {ctx.author.display_name}'s Wisdom Profile",
                description="Your journey of wisdom cultivation and self-reflection",
                color=discord.Color.purple(),
            )

            # Basic info
            embed.add_field(
                name="👤 Profile Info",
                value=f"**Discord**: {ctx.author.display_name}\n"
                f"**Bot Profile**: {user.bot_profile_name or 'Not set'}\n"
                f"**Member Since**: {user.created_at.strftime('%Y-%m-%d')}",
                inline=True,
            )

            # Wisdom progression
            embed.add_field(
                name="🌟 Wisdom Progression",
                value=f"**Wisdom Level**: {user.wisdom_level}/5\n"
                f"**Experience Points**: {user.experience_points}\n"
                f"**Skill Level**: {user.skill_level}/10",
                inline=True,
            )

            # Assessment stats
            embed.add_field(
                name="📊 Assessment Statistics",
                value=f"**Opinions Assessed**: {user.total_opinions_assessed}\n"
                f"**Reflections Made**: {user.total_reflections_made}\n"
                f"**Wisdom Score**: {user.wisdom_score}",
                inline=True,
            )

            # Quality metrics
            embed.add_field(
                name="🎯 Quality Metrics",
                value=f"**Wisdom Contributions**: {wisdom_contributions}/{len(user_opinions)}\n"
                f"**Avg Complexity**: {avg_complexity:.1f}/5\n"
                f"**Contribution Rate**: {(wisdom_contributions / len(user_opinions) * 100):.1f}%"
                if user_opinions
                else "**No opinions yet**",
                inline=True,
            )

            # Personality traits
            embed.add_field(
                name="🎭 Personality Profile",
                value=f"**Temperament**: {user.temperament.title()}\n"
                f"**Humor**: {user.humor.title()}\n"
                f"**Trust Level**: {user.trust_level.title()}\n"
                f"**Communication**: {user.communication_style.title()}",
                inline=True,
            )

            # Progression guidance
            next_level_opinions = {
                1: 10,
                2: 25,
                3: 50,
                4: 100,
                5: float("inf"),
            }
            next_milestone = next_level_opinions.get(
                user.wisdom_level, float("inf")
            )

            if next_milestone != float("inf"):
                remaining = next_milestone - user.total_opinions_assessed
                embed.add_field(
                    name="🚀 Next Milestone",
                    value=f"**{remaining} more opinions** to reach Wisdom Level {user.wisdom_level + 1}\n"
                    f"Progress: {user.total_opinions_assessed}/{next_milestone}",
                    inline=False,
                )
            else:
                embed.add_field(
                    name="🏆 Wisdom Master",
                    value="You've reached the highest wisdom level! Continue growing through reflection.",
                    inline=False,
                )

            embed.set_footer(
                text="Wisdom grows through self-examination and thoughtful reflection"
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in view_profile: {e}")
        await ctx.send(f"❌ Error retrieving profile: {str(e)}")


@bot.command(name="vp")
async def view_user_profile_ascii(
    ctx: commands.Context, member: discord.Member = None
):
    """View user profile in ASCII format (legacy command)."""
    target_user = member or ctx.author

    try:
        async with GameDBContext() as db_service:
            user = await db_service.get_or_create_discord_user(
                str(target_user.id), target_user.display_name
            )

            # Create ASCII-style profile
            ascii_profile = f"""
    ┌───────────────────────────────────────┐
    │ 🧘 WISDOM PROFILE                     │
    ├───────────────────────────────────────┤
    │ User: {target_user.display_name:<25} │
    │ Wisdom Level: {user.wisdom_level}/5{" " * 18} │
    │ Experience: {user.experience_points:<21} │
    │ Opinions: {user.total_opinions_assessed:<23} │
    │ Reflections: {user.total_reflections_made:<18} │
    │ Temperament: {user.temperament.title():<18} │
    │ Trust Level: {user.trust_level.title():<18} │
    └───────────────────────────────────────┘
            """

            await ctx.send(f"```{ascii_profile}```")

    except Exception as e:
        logger.error(f"Error in view_user_profile_ascii: {e}")
        await ctx.send(f"❌ Error retrieving profile: {str(e)}")


@bot.command(name="vudex")
async def view_user_profile_rich(
    ctx: commands.Context, member: discord.Member = None
):
    """View user profile with rich embed (legacy command)."""
    target_user = member or ctx.author

    try:
        async with GameDBContext() as db_service:
            user = await db_service.get_or_create_discord_user(
                str(target_user.id), target_user.display_name
            )

            # Get user's recent opinions for stats
            user_opinions = await db_service.get_user_opinion_assessments(
                str(target_user.id), limit=20
            )

            wisdom_contributions = sum(
                1 for o in user_opinions if o.contributes_to_wisdom
            )

            embed = discord.Embed(
                title=f"🧘 {target_user.display_name}'s Wisdom Profile",
                description="Wisdom cultivation journey",
                color=discord.Color.purple(),
            )

            embed.set_thumbnail(url=target_user.display_avatar.url)

            embed.add_field(
                name="🌟 Progression",
                value=f"**Level**: {user.wisdom_level}/5\n"
                f"**XP**: {user.experience_points}\n"
                f"**Skill**: {user.skill_level}/10",
                inline=True,
            )

            embed.add_field(
                name="📊 Activity",
                value=f"**Opinions**: {user.total_opinions_assessed}\n"
                f"**Reflections**: {user.total_reflections_made}\n"
                f"**Wisdom**: {wisdom_contributions}",
                inline=True,
            )

            embed.add_field(
                name="🎭 Personality",
                value=f"**Temperament**: {user.temperament.title()}\n"
                f"**Humor**: {user.humor.title()}\n"
                f"**Trust**: {user.trust_level.title()}",
                inline=True,
            )

            embed.set_footer(
                text=f"Member since {user.created_at.strftime('%Y-%m-%d')}"
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in view_user_profile_rich: {e}")
        await ctx.send(f"❌ Error retrieving profile: {str(e)}")


@bot.command(name="wisdom_trail")
async def explore_wisdom_trail(ctx: commands.Context, *, topic: str):
    """Explore wisdom progression on a specific topic."""
    try:
        async with GameDBContext() as db_service:
            # Get user's opinions related to the topic
            user_opinions = await db_service.get_user_opinion_assessments(
                str(ctx.author.id), limit=50
            )

            # Filter opinions related to the topic (simple keyword matching)
            topic_opinions = [
                o
                for o in user_opinions
                if topic.lower() in o.opinion_text.lower()
            ]

            if not topic_opinions:
                embed = discord.Embed(
                    title=f"🌱 New Wisdom Trail: {topic.title()}",
                    description="No opinions found on this topic yet. Start your wisdom trail!",
                    color=discord.Color.green(),
                )

                embed.add_field(
                    name="🚀 Start Your Trail",
                    value=f"Use `/opinion` to submit your first opinion about **{topic}**\n"
                    f'Example: `/opinion idea "My thoughts on {topic}..."`',
                    inline=False,
                )

                await ctx.send(embed=embed)
                return

            # Analyze the wisdom trail progression
            trail_wisdom = sum(
                1 for o in topic_opinions if o.contributes_to_wisdom
            )
            avg_complexity = sum(
                o.complexity_level for o in topic_opinions
            ) / len(topic_opinions)

            embed = discord.Embed(
                title=f"🧘 Wisdom Trail: {topic.title()}",
                description=f"Your journey of understanding on **{topic}**",
                color=discord.Color.purple(),
            )

            embed.add_field(
                name="📈 Trail Progress",
                value=f"**Opinions**: {len(topic_opinions)}\n"
                f"**Wisdom Contributions**: {trail_wisdom}\n"
                f"**Avg Complexity**: {avg_complexity:.1f}/5\n"
                f"**Wisdom Rate**: {(trail_wisdom / len(topic_opinions) * 100):.1f}%",
                inline=True,
            )

            # Show recent opinions on this topic
            recent_opinions = sorted(
                topic_opinions, key=lambda x: x.created_at, reverse=True
            )[:3]

            trail_summary = []
            for i, opinion in enumerate(recent_opinions, 1):
                wisdom_indicator = (
                    "🌟" if opinion.contributes_to_wisdom else "💭"
                )
                trail_summary.append(
                    f"{wisdom_indicator} **Opinion {i}**: {opinion.opinion_text[:60]}{'...' if len(opinion.opinion_text) > 60 else ''}"
                )

            if trail_summary:
                embed.add_field(
                    name="🗂️ Recent Trail Entries",
                    value="\n\n".join(trail_summary),
                    inline=False,
                )

            embed.add_field(
                name="🔄 Continue Your Trail",
                value=f"Submit more opinions about **{topic}** to deepen your wisdom trail\n"
                f"Use `/opinion` to add new perspectives",
                inline=False,
            )

            embed.set_footer(
                text=f"Trail started: {recent_opinions[-1].created_at.strftime('%Y-%m-%d') if recent_opinions else 'Unknown'}"
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in explore_wisdom_trail: {e}")
        await ctx.send(f"❌ Error exploring wisdom trail: {str(e)}")


@bot.command(name="my_trails")
async def view_my_trails(ctx: commands.Context):
    """View your favorite wisdom trails and topics."""
    try:
        async with GameDBContext() as db_service:
            # Get all user opinions
            user_opinions = await db_service.get_user_opinion_assessments(
                str(ctx.author.id), limit=100
            )

            if not user_opinions:
                await ctx.send(
                    "🌱 No wisdom trails yet. Use `/opinion` to start your first trail!"
                )
                return

            # Advanced topic extraction algorithm
            topic_stats = {}

            # Define stop words to filter out
            stop_words = {
                "think",
                "really",
                "believe",
                "opinion",
                "something",
                "anything",
                "everything",
                "nothing",
                "someone",
                "everyone",
                "because",
                "however",
                "therefore",
                "although",
                "though",
                "while",
                "since",
                "during",
                "before",
                "after",
                "about",
                "above",
                "below",
                "between",
                "through",
                "should",
                "would",
                "could",
                "might",
                "maybe",
                "perhaps",
                "probably",
                "always",
                "never",
                "often",
                "sometimes",
                "usually",
                "generally",
                "important",
                "significant",
                "interesting",
                "obvious",
                "clear",
                "seems",
                "appears",
                "looks",
                "feels",
                "sounds",
                "means",
            }

            # Define topic categories for semantic grouping
            topic_groups = {
                "science": [
                    "science",
                    "scientific",
                    "research",
                    "study",
                    "evidence",
                    "data",
                    "experiment",
                    "theory",
                    "hypothesis",
                ],
                "climate": [
                    "climate",
                    "warming",
                    "environment",
                    "carbon",
                    "emissions",
                    "temperature",
                    "weather",
                    "greenhouse",
                ],
                "politics": [
                    "politics",
                    "political",
                    "government",
                    "democracy",
                    "election",
                    "voting",
                    "policy",
                    "legislation",
                ],
                "technology": [
                    "technology",
                    "digital",
                    "artificial",
                    "intelligence",
                    "computer",
                    "internet",
                    "software",
                    "algorithm",
                ],
                "society": [
                    "society",
                    "social",
                    "community",
                    "culture",
                    "people",
                    "human",
                    "civilization",
                    "humanity",
                ],
                "economics": [
                    "economics",
                    "economic",
                    "money",
                    "financial",
                    "market",
                    "business",
                    "trade",
                    "capitalism",
                ],
                "philosophy": [
                    "philosophy",
                    "philosophical",
                    "ethics",
                    "moral",
                    "truth",
                    "knowledge",
                    "wisdom",
                    "consciousness",
                ],
                "education": [
                    "education",
                    "learning",
                    "teaching",
                    "school",
                    "university",
                    "knowledge",
                    "student",
                    "academic",
                ],
                "health": [
                    "health",
                    "medical",
                    "medicine",
                    "disease",
                    "treatment",
                    "therapy",
                    "wellness",
                    "mental",
                ],
                "religion": [
                    "religion",
                    "religious",
                    "spiritual",
                    "faith",
                    "belief",
                    "church",
                    "prayer",
                    "sacred",
                ],
            }

            for opinion in user_opinions:
                # Advanced topic extraction
                text = opinion.opinion_text.lower()
                words = [w.strip('.,!?;:"()[]{}') for w in text.split()]

                # Extract meaningful keywords
                keywords = []
                for word in words:
                    if (
                        len(word) > 4
                        and word.isalpha()
                        and word not in stop_words
                        and not word.endswith("ing")  # Filter gerunds
                        and not word.endswith(
                            "tion"
                        )  # Filter some abstractions
                        and word.count(word[0]) < len(word) * 0.5
                    ):  # Filter repetitive words
                        keywords.append(word)

                # Group keywords into semantic categories
                detected_topics = set()

                # Check for topic group matches
                for topic_name, topic_words in topic_groups.items():
                    if any(
                        keyword in topic_words
                        or any(tw in keyword for tw in topic_words)
                        for keyword in keywords
                    ):
                        detected_topics.add(topic_name)

                # Add significant individual keywords not in groups
                for keyword in keywords[:5]:  # Top 5 keywords
                    if (
                        len(keyword) > 6
                    ):  # Longer words are more likely to be meaningful
                        detected_topics.add(keyword)

                # Update topic statistics
                for topic in detected_topics:
                    if topic not in topic_stats:
                        topic_stats[topic] = {
                            "count": 0,
                            "wisdom_count": 0,
                            "complexity_sum": 0,
                        }
                    topic_stats[topic]["count"] += 1
                    if opinion.contributes_to_wisdom:
                        topic_stats[topic]["wisdom_count"] += 1
                    topic_stats[topic]["complexity_sum"] += (
                        opinion.complexity_level
                    )

            # Advanced topic ranking algorithm
            def calculate_topic_score(topic_name, stats):
                """Calculate sophisticated topic score based on multiple factors."""
                count = stats["count"]
                wisdom_count = stats["wisdom_count"]
                complexity_sum = stats["complexity_sum"]

                # Base engagement score
                engagement_score = count * 2 + wisdom_count * 3

                # Wisdom quality bonus
                wisdom_rate = wisdom_count / count if count > 0 else 0
                wisdom_bonus = wisdom_rate * 10

                # Complexity bonus (higher complexity = more sophisticated topics)
                avg_complexity = complexity_sum / count if count > 0 else 0
                complexity_bonus = avg_complexity * 2

                # Topic category bonus (favor broader topics)
                category_bonus = 5 if topic_name in topic_groups else 0

                # Length penalty for very short words (favor meaningful terms)
                length_bonus = (
                    min(len(topic_name) - 4, 4) if len(topic_name) > 4 else 0
                )

                total_score = (
                    engagement_score
                    + wisdom_bonus
                    + complexity_bonus
                    + category_bonus
                    + length_bonus
                )
                return total_score

            # Sort topics by sophisticated scoring
            sorted_topics = sorted(
                topic_stats.items(),
                key=lambda x: calculate_topic_score(x[0], x[1]),
                reverse=True,
            )[:8]  # Top 8 topics

            embed = discord.Embed(
                title="🧘 Your Wisdom Trails",
                description="Topics you've explored through opinion assessment",
                color=discord.Color.purple(),
            )

            trail_list = []
            for topic, stats in sorted_topics:
                wisdom_rate = (
                    (stats["wisdom_count"] / stats["count"] * 100)
                    if stats["count"] > 0
                    else 0
                )
                avg_complexity = (
                    stats["complexity_sum"] / stats["count"]
                    if stats["count"] > 0
                    else 0
                )

                trail_list.append(
                    f"🌟 **{topic.title()}**: {stats['count']} opinions, "
                    f"{wisdom_rate:.0f}% wisdom, {avg_complexity:.1f} complexity"
                )

            if trail_list:
                embed.add_field(
                    name="🗂️ Your Active Trails",
                    value="\n".join(trail_list),
                    inline=False,
                )

            embed.add_field(
                name="🔍 Explore a Trail",
                value="Use `!wisdom_trail <topic>` to explore any of your trails in detail",
                inline=False,
            )

            embed.set_footer(
                text=f"Total opinions: {len(user_opinions)} | Wisdom contributions: {sum(1 for o in user_opinions if o.contributes_to_wisdom)}"
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in view_my_trails: {e}")
        await ctx.send(f"❌ Error retrieving trails: {str(e)}")


@bot.command(name="points")
async def view_point_system(ctx: commands.Context):
    """View the wisdom point system rules and earning conditions."""
    embed = discord.Embed(
        title="🏆 Wisdom Point System Rules",
        description="How to earn experience points and advance wisdom levels",
        color=discord.Color.gold(),
    )

    embed.add_field(
        name="📊 Point Earning Rules",
        value="**Opinion Submission**: 10 XP base\n"
        "**Wisdom Contribution**: +15 XP bonus\n"
        "**High Complexity (4-5)**: +5 XP bonus\n"
        "**Evidence-Based Pattern**: +10 XP bonus\n"
        "**Reflection Activity**: 5 XP per reflection\n"
        "**Pattern Recognition**: 3 XP per pattern detected",
        inline=False,
    )

    embed.add_field(
        name="🌟 Wisdom Level Requirements",
        value="**Level 1**: 0 XP (10 opinions minimum)\n"
        "**Level 2**: 250 XP (25 opinions minimum)\n"
        "**Level 3**: 750 XP (50 opinions minimum)\n"
        "**Level 4**: 1500 XP (100 opinions minimum)\n"
        "**Level 5**: 3000 XP (200 opinions minimum)",
        inline=False,
    )

    embed.add_field(
        name="🎯 Quality Multipliers",
        value="**Logical Coherence > 0.8**: 1.5x multiplier\n"
        "**Evidence Quality > 0.7**: 1.3x multiplier\n"
        "**Wisdom Potential > 0.6**: 1.2x multiplier\n"
        "**Multiple Patterns**: +2 XP per additional pattern",
        inline=False,
    )

    embed.add_field(
        name="⚖️ Point Deductions",
        value="**Ad Hominem Pattern**: -5 XP\n"
        "**Appeal to Authority**: -3 XP\n"
        "**False Dichotomy**: -3 XP\n"
        "**Low Evidence Quality**: -2 XP\n"
        "*Note: Minimum 1 XP per opinion*",
        inline=False,
    )

    embed.set_footer(
        text="Points encourage wisdom cultivation, not collection • Merit over popularity"
    )

    await ctx.send(embed=embed)


@bot.command(name="my_opinions")
async def list_my_opinions(ctx: commands.Context, limit: int = 10):
    """List your recent opinions with IDs for reference."""
    print(
        f"🔍 my_opinions command called by {ctx.author.display_name} (ID: {ctx.author.id})"
    )
    try:
        async with GameDBContext() as db_service:
            print(f"🔍 Database connection established")
            user_opinions = await db_service.get_user_opinion_assessments(
                str(ctx.author.id),
                limit=min(limit, 20),  # Cap at 20
            )
            print(
                f"🔍 Found {len(user_opinions) if user_opinions else 0} opinions for user"
            )

            if not user_opinions:
                await ctx.send(
                    "📝 No opinions submitted yet. Use `/opinion` to start your wisdom journey!"
                )
                return

            embed = discord.Embed(
                title=f"📝 {ctx.author.display_name}'s Recent Opinions",
                description=f"Your last {len(user_opinions)} opinion assessments",
                color=discord.Color.blue(),
            )

            for i, opinion in enumerate(user_opinions, 1):
                wisdom_indicator = (
                    "🌟" if opinion.contributes_to_wisdom else "💭"
                )

                embed.add_field(
                    name=f"{wisdom_indicator} Opinion {i} - ID: {opinion.id[:8]}",
                    value=f"**Target**: {opinion.target_type.title()}\n"
                    f"**Opinion**: {opinion.opinion_text[:80]}{'...' if len(opinion.opinion_text) > 80 else ''}\n"
                    f"**Complexity**: {opinion.complexity_level}/5 | **Wisdom**: {'Yes' if opinion.contributes_to_wisdom else 'No'}\n"
                    f"**Date**: {opinion.created_at.strftime('%Y-%m-%d %H:%M')}",
                    inline=False,
                )

            embed.add_field(
                name="🔄 Actions",
                value="Use `!reflect <opinion_id>` to reflect on any opinion\n"
                "Use `!reference <opinion_id>` to reference in new opinions\n"
                "Use `!my_opinions 20` to see more opinions",
                inline=False,
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in list_my_opinions: {e}")
        await ctx.send(f"❌ Error retrieving opinions: {str(e)}")


@bot.command(name="test_command")
async def test_command(ctx: commands.Context):
    """Simple test command to verify bot responsiveness."""
    print(f"🔍 test_command called by {ctx.author.display_name}")
    await ctx.send("✅ Bot is responding! Commands are working.")


@bot.command(name="browse_opinions")
async def browse_public_opinions(
    ctx: commands.Context, target_type: str = None, limit: int = 10
):
    """Browse recent public opinions from the community."""
    try:
        async with GameDBContext() as db_service:
            # Get recent public opinions (this would need a new DB method)
            # For now, let's show a placeholder implementation

            embed = discord.Embed(
                title="🌍 Community Wisdom Feed",
                description="Recent public opinions from the wisdom community",
                color=discord.Color.green(),
            )

            embed.add_field(
                name="🚧 Coming Soon",
                value="**Public Opinion Feed**: Browse community opinions\n"
                "**Filter by Topic**: `!browse_opinions idea` for ideas only\n"
                "**Wisdom Highlights**: See top wisdom contributions\n"
                "**Reference System**: Link to others' opinions",
                inline=False,
            )

            embed.add_field(
                name="🎯 Current Usage",
                value="Use `!my_opinions` to see your own opinions\n"
                "Use `!vudex @user` to see someone's profile\n"
                "Use `!wisdom_trail <topic>` to explore topic progression",
                inline=False,
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in browse_public_opinions: {e}")
        await ctx.send(f"❌ Error browsing opinions: {str(e)}")


@bot.command(name="reference")
async def reference_opinion(
    ctx: commands.Context, opinion_id: str, *, comment: str = None
):
    """Reference another opinion in your discussions."""
    try:
        async with GameDBContext() as db_service:
            # Get the referenced opinion
            referenced_opinion = await db_service.get_opinion_assessment_by_id(
                opinion_id
            )

            if not referenced_opinion:
                await ctx.send(f"🔍 Opinion with ID '{opinion_id}' not found.")
                return

            # Get the opinion author for display
            referenced_user = await db_service.get_discord_user_by_id(
                referenced_opinion.discord_user_id
            )

            embed = discord.Embed(
                title="🔗 Opinion Reference",
                description=f"Referencing opinion by {referenced_user.bot_profile_name if referenced_user else 'Unknown User'}",
                color=discord.Color.orange(),
            )

            embed.add_field(
                name=f"📝 Referenced Opinion (ID: {opinion_id})",
                value=f"**Target**: {referenced_opinion.target_type.title()}\n"
                f"**Opinion**: {referenced_opinion.opinion_text}\n"
                f"**Wisdom**: {'Yes' if referenced_opinion.contributes_to_wisdom else 'No'} | "
                f"**Complexity**: {referenced_opinion.complexity_level}/5",
                inline=False,
            )

            if comment:
                embed.add_field(
                    name=f"💬 Your Comment",
                    value=comment,
                    inline=False,
                )

            embed.add_field(
                name="🔄 Next Steps",
                value="Use `/opinion` and mention this reference in your opinion\n"
                'Example: "Building on opinion '
                + opinion_id[:8]
                + ', I think..."\n'
                "Use `!reflect " + opinion_id + "` to reflect on this opinion",
                inline=False,
            )

            embed.set_footer(
                text=f"Referenced: {referenced_opinion.created_at.strftime('%Y-%m-%d %H:%M')}"
            )

            await ctx.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in reference_opinion: {e}")
        await ctx.send(f"❌ Error referencing opinion: {str(e)}")


# === Slash Commands ===


@bot.tree.command(
    name="opinion", description="Submit an opinion for wisdom assessment"
)
@discord.app_commands.describe(
    target_type="What type of thing are you giving an opinion about?",
    opinion_text="Your opinion or statement to be analyzed",
)
@discord.app_commands.choices(
    target_type=[
        discord.app_commands.Choice(name="💡 Idea or Concept", value="idea"),
        discord.app_commands.Choice(name="🎯 Thing or Object", value="thing"),
        discord.app_commands.Choice(
            name="👤 Person or Individual", value="person"
        ),
        discord.app_commands.Choice(
            name="🧠 Abstract Concept", value="concept"
        ),
    ]
)
async def slash_opinion(
    interaction: discord.Interaction,
    target_type: discord.app_commands.Choice[str],
    opinion_text: str,
):
    """Slash command version of opinion submission."""
    # Defer the response since analysis might take time
    await interaction.response.defer()

    try:
        # Extract target type value from choice
        target_value = target_type.value

        async with GameDBContext() as db_service:
            # Get or create user
            user = await db_service.get_or_create_discord_user(
                str(interaction.user.id), interaction.user.display_name
            )

            # Analyze opinion with wisdom engine
            analysis = wisdom_engine.analyze_opinion(
                opinion_text, target_value
            )

            # Create opinion assessment record
            opinion_assessment = await db_service.create_opinion_assessment(
                discord_user_id=str(interaction.user.id),
                opinion_text=opinion_text,
                target_type=target_value,
                analysis_results=analysis,
            )

            # Update user stats
            await db_service.increment_user_opinion_stats(
                str(interaction.user.id)
            )

            # Create response embed (same as prefix command)
            embed = discord.Embed(
                title="🧘 Opinion Assessment Complete",
                description=f"**Target**: {target_value.title()}\n**Opinion**: {opinion_text[:100]}{'...' if len(opinion_text) > 100 else ''}",
                color=discord.Color.blue(),
            )

            # Add analysis results
            embed.add_field(
                name="📊 Merit Scores",
                value=f"**Logical Coherence**: {analysis['logical_coherence_score']:.2f}\n"
                f"**Evidence Quality**: {analysis['evidence_quality_score']:.2f}\n"
                f"**Wisdom Potential**: {analysis['wisdom_potential_score']:.2f}",
                inline=True,
            )

            embed.add_field(
                name="🎯 Assessment",
                value=f"**Complexity Level**: {analysis['complexity_level']}/5\n"
                f"**Contributes to Wisdom**: {'Yes' if analysis['contributes_to_wisdom'] else 'No'}\n"
                f"**Wisdom Card Potential**: {'Yes' if analysis['wisdom_card_potential'] else 'No'}",
                inline=True,
            )

            # Add pattern summary
            patterns = analysis.get("logical_patterns", [])
            if patterns:
                pattern_summary = []
                for pattern in patterns[:3]:  # Show top 3 patterns
                    pattern_summary.append(
                        f"• {pattern['pattern'].replace('_', ' ').title()}"
                    )

                embed.add_field(
                    name="🔍 Patterns Detected",
                    value="\n".join(pattern_summary)
                    + (
                        f"\n... and {len(patterns) - 3} more"
                        if len(patterns) > 3
                        else ""
                    ),
                    inline=False,
                )

            # Add mentor insights if any
            mentor_insights = analysis.get("mentor_insights", [])
            if mentor_insights:
                embed.add_field(
                    name="🎓 Mentor Insights",
                    value=mentor_insights[0],  # Show first insight
                    inline=False,
                )

            embed.add_field(
                name="🔄 Next Steps",
                value=f"Use `/reflect {opinion_assessment.id[:8]}` to reflect on this opinion\n"
                f"Use `/patterns` to see your pattern history",
                inline=False,
            )

            embed.set_footer(
                text=f"Opinion ID: {opinion_assessment.id[:8]} | Wisdom through self-examination"
            )

            # Add interactive buttons
            view = discord.ui.View()

            # Reflect button
            reflect_button = discord.ui.Button(
                label="🤔 Reflect on This",
                style=discord.ButtonStyle.secondary,
                custom_id=f"reflect_{opinion_assessment.id[:8]}",
            )

            # Patterns button
            patterns_button = discord.ui.Button(
                label="🔍 View Patterns",
                style=discord.ButtonStyle.primary,
                custom_id="view_patterns",
            )

            view.add_item(reflect_button)
            view.add_item(patterns_button)

            await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Error in slash_opinion: {e}")
        await interaction.followup.send(
            f"❌ Error processing opinion: {str(e)}"
        )


@bot.tree.command(
    name="patterns", description="View your logical pattern history"
)
async def slash_patterns(interaction: discord.Interaction):
    """Slash command version of patterns viewing."""
    await interaction.response.defer()

    try:
        async with GameDBContext() as db_service:
            # Get user's opinion assessments
            user_opinions = await db_service.get_user_opinion_assessments(
                str(interaction.user.id), limit=10
            )

            if not user_opinions:
                await interaction.followup.send(
                    "🔍 No opinions assessed yet. Use `/opinion` to submit your first opinion!"
                )
                return

            embed = discord.Embed(
                title="🔍 Your Logical Pattern History",
                description="Recent patterns detected in your opinions",
                color=discord.Color.green(),
            )

            # Aggregate patterns (same logic as prefix command)
            pattern_counts = {}
            wisdom_indicators = 0
            fallacy_indicators = 0

            for opinion in user_opinions:
                if opinion.logical_patterns:
                    for pattern_data in opinion.logical_patterns:
                        pattern_name = pattern_data.get("pattern", "unknown")
                        pattern_counts[pattern_name] = (
                            pattern_counts.get(pattern_name, 0) + 1
                        )

                        # Count wisdom vs fallacy indicators
                        if pattern_name in [
                            "evidence_based",
                            "nuanced_thinking",
                            "acknowledges_uncertainty",
                            "considers_multiple_perspectives",
                            "builds_on_existing_knowledge",
                        ]:
                            wisdom_indicators += 1
                        elif pattern_name in [
                            "ad_hominem",
                            "strawman",
                            "false_dichotomy",
                            "appeal_to_authority",
                            "appeal_to_popularity",
                        ]:
                            fallacy_indicators += 1

            # Show top patterns
            sorted_patterns = sorted(
                pattern_counts.items(), key=lambda x: x[1], reverse=True
            )
            pattern_list = []
            for pattern, count in sorted_patterns[:5]:
                pattern_display = pattern.replace("_", " ").title()
                pattern_list.append(f"• {pattern_display}: {count}x")

            if pattern_list:
                embed.add_field(
                    name="📈 Most Common Patterns",
                    value="\n".join(pattern_list),
                    inline=False,
                )

            embed.add_field(
                name="🧘 Wisdom Balance",
                value=f"**Wisdom Indicators**: {wisdom_indicators}\n"
                f"**Misalignment Patterns**: {fallacy_indicators}\n"
                f"**Balance Ratio**: {wisdom_indicators / (fallacy_indicators + 1):.1f}:1",
                inline=True,
            )

            embed.add_field(
                name="📊 Assessment Stats",
                value=f"**Total Opinions**: {len(user_opinions)}\n"
                f"**Avg Complexity**: {sum(o.complexity_level for o in user_opinions) / len(user_opinions):.1f}/5\n"
                f"**Wisdom Contributions**: {sum(1 for o in user_opinions if o.contributes_to_wisdom)}",
                inline=True,
            )

            embed.set_footer(
                text="Patterns help you recognize thinking habits • Use /reflect to examine specific opinions"
            )
            await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in slash_patterns: {e}")
        await interaction.followup.send(
            f"❌ Error retrieving patterns: {str(e)}"
        )


@bot.tree.command(
    name="reflect",
    description="Reflect on a specific opinion and its patterns",
)
@discord.app_commands.describe(
    opinion_id="The ID of the opinion to reflect on (8 characters)"
)
async def slash_reflect(interaction: discord.Interaction, opinion_id: str):
    """Slash command version of opinion reflection."""
    await interaction.response.defer()

    try:
        async with GameDBContext() as db_service:
            # Get the opinion assessment
            opinion = await db_service.get_opinion_assessment_by_id(opinion_id)

            if not opinion:
                await interaction.followup.send(
                    f"🔍 Opinion with ID '{opinion_id}' not found."
                )
                return

            # Check if user owns this opinion
            if opinion.discord_user_id != str(interaction.user.id):
                await interaction.followup.send(
                    "🔒 You can only reflect on your own opinions."
                )
                return

            embed = discord.Embed(
                title="🧘 Opinion Reflection",
                description=f"**Original Opinion**: {opinion.opinion_text}",
                color=discord.Color.purple(),
            )

            # Show detailed patterns
            if opinion.logical_patterns:
                pattern_details = []
                for pattern_data in opinion.logical_patterns:
                    pattern_name = (
                        pattern_data.get("pattern", "unknown")
                        .replace("_", " ")
                        .title()
                    )
                    confidence = pattern_data.get("confidence", 0)
                    explanation = pattern_data.get(
                        "explanation", "No explanation"
                    )

                    pattern_details.append(
                        f"**{pattern_name}** ({confidence:.0%})\n{explanation}"
                    )

                embed.add_field(
                    name="🔍 Detected Patterns",
                    value="\n\n".join(pattern_details[:3]),  # Show top 3
                    inline=False,
                )

            # Show progression notes
            if opinion.progression_notes:
                embed.add_field(
                    name="📈 Growth Opportunities",
                    value=opinion.progression_notes,
                    inline=False,
                )

            # Reflection prompts
            embed.add_field(
                name="🤔 Reflection Questions",
                value="• What assumptions did I make?\n"
                "• What evidence supports my view?\n"
                "• What would someone who disagrees say?\n"
                "• How might I refine this opinion?",
                inline=False,
            )

            embed.set_footer(
                text=f"Opinion ID: {opinion_id} | Self-examination leads to wisdom"
            )

            # Add interactive buttons for further actions
            view = discord.ui.View()

            patterns_button = discord.ui.Button(
                label="🔍 View All Patterns",
                style=discord.ButtonStyle.primary,
                custom_id="view_patterns",
            )

            new_opinion_button = discord.ui.Button(
                label="💭 Submit New Opinion",
                style=discord.ButtonStyle.secondary,
                custom_id="new_opinion",
            )

            view.add_item(patterns_button)
            view.add_item(new_opinion_button)

            await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Error in slash_reflect: {e}")
        await interaction.followup.send(
            f"❌ Error retrieving opinion: {str(e)}"
        )


@bot.tree.command(
    name="help_wisdom",
    description="Show the wisdom assessment system help menu",
)
async def slash_help_wisdom(interaction: discord.Interaction):
    """Slash command version of help."""
    embed = discord.Embed(
        title="🧘 Wisdom Assessment System - Help Menu",
        color=discord.Color.purple(),
    )

    embed.add_field(
        name="🎯 Opinion Assessment",
        value="`/opinion` - Submit opinion with dropdown selection\n"
        "`/reflect <opinion_id>` - Reflect on patterns in your opinion\n"
        "`/patterns` - View your logical pattern history\n"
        "`/profile` - View your wisdom profile and progression\n"
        "**Legacy**: `!opinion`, `!reflect`, `!patterns` also work",
        inline=False,
    )

    embed.add_field(
        name="🌟 Wisdom Cultivation",
        value="**Interactive Features**: Use buttons after submitting opinions\n"
        "**Pattern Recognition**: Automatic detection of logical patterns\n"
        "**Merit-Based Scoring**: Evidence quality over popularity\n"
        "**Self-Reflection**: Guided questions for growth",
        inline=False,
    )

    embed.add_field(
        name="🧠 Philosophy",
        value="**Merit over Popularity**: We value logical coherence and evidence\n"
        "**Pattern Recognition**: Fallacies are misalignment indicators\n"
        "**Wisdom Cultivation**: From opinions to insights to wisdom\n"
        "**Anti-Celebrity**: Fame doesn't equal truth",
        inline=False,
    )

    embed.add_field(
        name="🚀 Getting Started",
        value="1. Use `/opinion` and select target type from dropdown\n"
        "2. Use `/patterns` to see what logical patterns were detected\n"
        "3. Use `/reflect <id>` to reflect on your thinking patterns\n"
        "4. Click interactive buttons for quick actions!",
        inline=False,
    )

    embed.set_footer(text="Wisdom through recognition, not collection 🧘✨")

    # Add quick action buttons
    view = discord.ui.View()

    opinion_button = discord.ui.Button(
        label="💭 Submit Opinion",
        style=discord.ButtonStyle.primary,
        custom_id="new_opinion",
    )

    patterns_button = discord.ui.Button(
        label="🔍 View Patterns",
        style=discord.ButtonStyle.secondary,
        custom_id="view_patterns",
    )

    view.add_item(opinion_button)
    view.add_item(patterns_button)

    await interaction.response.send_message(embed=embed, view=view)


@bot.tree.command(
    name="profile", description="View your wisdom profile and progression"
)
async def slash_profile(interaction: discord.Interaction):
    """Show user's wisdom profile with stats and progression."""
    await interaction.response.defer()

    try:
        async with GameDBContext() as db_service:
            # Get user data
            user = await db_service.get_or_create_discord_user(
                str(interaction.user.id), interaction.user.display_name
            )

            # Get recent opinions for additional stats
            user_opinions = await db_service.get_user_opinion_assessments(
                str(interaction.user.id), limit=50
            )

            # Calculate wisdom stats
            wisdom_contributions = sum(
                1 for o in user_opinions if o.contributes_to_wisdom
            )
            avg_complexity = (
                sum(o.complexity_level for o in user_opinions)
                / len(user_opinions)
                if user_opinions
                else 0
            )

            # Create profile embed
            embed = discord.Embed(
                title=f"🧘 {interaction.user.display_name}'s Wisdom Profile",
                description="Your journey of wisdom cultivation and self-reflection",
                color=discord.Color.purple(),
            )

            # Basic info
            embed.add_field(
                name="👤 Profile Info",
                value=f"**Discord**: {interaction.user.display_name}\n"
                f"**Bot Profile**: {user.bot_profile_name or 'Not set'}\n"
                f"**Member Since**: {user.created_at.strftime('%Y-%m-%d')}",
                inline=True,
            )

            # Wisdom progression
            embed.add_field(
                name="🌟 Wisdom Progression",
                value=f"**Wisdom Level**: {user.wisdom_level}/5\n"
                f"**Experience Points**: {user.experience_points}\n"
                f"**Skill Level**: {user.skill_level}/10",
                inline=True,
            )

            # Assessment stats
            embed.add_field(
                name="📊 Assessment Statistics",
                value=f"**Opinions Assessed**: {user.total_opinions_assessed}\n"
                f"**Reflections Made**: {user.total_reflections_made}\n"
                f"**Wisdom Score**: {user.wisdom_score}",
                inline=True,
            )

            # Quality metrics
            embed.add_field(
                name="🎯 Quality Metrics",
                value=f"**Wisdom Contributions**: {wisdom_contributions}/{len(user_opinions)}\n"
                f"**Avg Complexity**: {avg_complexity:.1f}/5\n"
                f"**Contribution Rate**: {(wisdom_contributions / len(user_opinions) * 100):.1f}%"
                if user_opinions
                else "**No opinions yet**",
                inline=True,
            )

            # Personality traits
            embed.add_field(
                name="🎭 Personality Profile",
                value=f"**Temperament**: {user.temperament.title()}\n"
                f"**Humor**: {user.humor.title()}\n"
                f"**Trust Level**: {user.trust_level.title()}\n"
                f"**Communication**: {user.communication_style.title()}",
                inline=True,
            )

            # Progression guidance
            next_level_opinions = {
                1: 10,
                2: 25,
                3: 50,
                4: 100,
                5: float("inf"),
            }
            next_milestone = next_level_opinions.get(
                user.wisdom_level, float("inf")
            )

            if next_milestone != float("inf"):
                remaining = next_milestone - user.total_opinions_assessed
                embed.add_field(
                    name="🚀 Next Milestone",
                    value=f"**{remaining} more opinions** to reach Wisdom Level {user.wisdom_level + 1}\n"
                    f"Progress: {user.total_opinions_assessed}/{next_milestone}",
                    inline=False,
                )
            else:
                embed.add_field(
                    name="🏆 Wisdom Master",
                    value="You've reached the highest wisdom level! Continue growing through reflection.",
                    inline=False,
                )

            embed.set_footer(
                text="Wisdom grows through self-examination and thoughtful reflection"
            )

            # Add action buttons
            view = discord.ui.View()

            opinion_button = discord.ui.Button(
                label="💭 Submit Opinion",
                style=discord.ButtonStyle.primary,
                custom_id="new_opinion",
            )

            patterns_button = discord.ui.Button(
                label="🔍 View Patterns",
                style=discord.ButtonStyle.secondary,
                custom_id="view_patterns",
            )

            view.add_item(opinion_button)
            view.add_item(patterns_button)

            await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Error in slash_profile: {e}")
        await interaction.followup.send(
            f"❌ Error retrieving profile: {str(e)}"
        )


# === Button Interaction Handlers ===


@bot.event
async def on_interaction(interaction: discord.Interaction):
    """Handle button interactions."""
    if interaction.type == discord.InteractionType.component:
        custom_id = interaction.data.get("custom_id", "")

        if custom_id == "view_patterns":
            # Handle patterns button - call the patterns logic directly
            await interaction.response.defer()

            try:
                async with GameDBContext() as db_service:
                    # Get user's opinion assessments
                    user_opinions = (
                        await db_service.get_user_opinion_assessments(
                            str(interaction.user.id), limit=10
                        )
                    )

                    if not user_opinions:
                        await interaction.followup.send(
                            "🔍 No opinions assessed yet. Use `/opinion` to submit your first opinion!"
                        )
                        return

                    embed = discord.Embed(
                        title="🔍 Your Logical Pattern History",
                        description="Recent patterns detected in your opinions",
                        color=discord.Color.green(),
                    )

                    # Aggregate patterns (same logic as slash command)
                    pattern_counts = {}
                    wisdom_indicators = 0
                    fallacy_indicators = 0

                    for opinion in user_opinions:
                        if opinion.logical_patterns:
                            for pattern_data in opinion.logical_patterns:
                                pattern_name = pattern_data.get(
                                    "pattern", "unknown"
                                )
                                pattern_counts[pattern_name] = (
                                    pattern_counts.get(pattern_name, 0) + 1
                                )

                                # Count wisdom vs fallacy indicators
                                if pattern_name in [
                                    "evidence_based",
                                    "nuanced_thinking",
                                    "acknowledges_uncertainty",
                                    "considers_multiple_perspectives",
                                    "builds_on_existing_knowledge",
                                ]:
                                    wisdom_indicators += 1
                                elif pattern_name in [
                                    "ad_hominem",
                                    "strawman",
                                    "false_dichotomy",
                                    "appeal_to_authority",
                                    "appeal_to_popularity",
                                ]:
                                    fallacy_indicators += 1

                    # Show top patterns
                    sorted_patterns = sorted(
                        pattern_counts.items(),
                        key=lambda x: x[1],
                        reverse=True,
                    )
                    pattern_list = []
                    for pattern, count in sorted_patterns[:5]:
                        pattern_display = pattern.replace("_", " ").title()
                        pattern_list.append(f"• {pattern_display}: {count}x")

                    if pattern_list:
                        embed.add_field(
                            name="📈 Most Common Patterns",
                            value="\n".join(pattern_list),
                            inline=False,
                        )

                    embed.add_field(
                        name="🧘 Wisdom Balance",
                        value=f"**Wisdom Indicators**: {wisdom_indicators}\n"
                        f"**Misalignment Patterns**: {fallacy_indicators}\n"
                        f"**Balance Ratio**: {wisdom_indicators / (fallacy_indicators + 1):.1f}:1",
                        inline=True,
                    )

                    embed.add_field(
                        name="📊 Assessment Stats",
                        value=f"**Total Opinions**: {len(user_opinions)}\n"
                        f"**Avg Complexity**: {sum(o.complexity_level for o in user_opinions) / len(user_opinions):.1f}/5\n"
                        f"**Wisdom Contributions**: {sum(1 for o in user_opinions if o.contributes_to_wisdom)}",
                        inline=True,
                    )

                    embed.set_footer(
                        text="Patterns help you recognize thinking habits • Use /reflect to examine specific opinions"
                    )
                    await interaction.followup.send(embed=embed)

            except Exception as e:
                logger.error(f"Error in button patterns: {e}")
                await interaction.followup.send(
                    f"❌ Error retrieving patterns: {str(e)}"
                )

        elif custom_id == "new_opinion":
            # Send help for creating new opinion
            embed = discord.Embed(
                title="💭 Submit New Opinion",
                description="Use the `/opinion` slash command to submit a new opinion for assessment!",
                color=discord.Color.green(),
            )
            embed.add_field(
                name="How to use:",
                value="1. Type `/opinion` in the chat\n"
                "2. Select target type from dropdown\n"
                "3. Enter your opinion text\n"
                "4. Submit for wisdom assessment!",
                inline=False,
            )
            await interaction.response.send_message(
                embed=embed, ephemeral=True
            )

        elif custom_id.startswith("reflect_"):
            # Extract opinion ID and handle reflect button
            opinion_id = custom_id.replace("reflect_", "")
            await interaction.response.defer()

            try:
                async with GameDBContext() as db_service:
                    # Get the opinion assessment
                    opinion = await db_service.get_opinion_assessment_by_id(
                        opinion_id
                    )

                    if not opinion:
                        await interaction.followup.send(
                            f"🔍 Opinion with ID '{opinion_id}' not found."
                        )
                        return

                    # Check if user owns this opinion
                    if opinion.discord_user_id != str(interaction.user.id):
                        await interaction.followup.send(
                            "🔒 You can only reflect on your own opinions."
                        )
                        return

                    embed = discord.Embed(
                        title="🧘 Opinion Reflection",
                        description=f"**Original Opinion**: {opinion.opinion_text}",
                        color=discord.Color.purple(),
                    )

                    # Show detailed patterns
                    if opinion.logical_patterns:
                        pattern_details = []
                        for pattern_data in opinion.logical_patterns:
                            pattern_name = (
                                pattern_data.get("pattern", "unknown")
                                .replace("_", " ")
                                .title()
                            )
                            confidence = pattern_data.get("confidence", 0)
                            explanation = pattern_data.get(
                                "explanation", "No explanation"
                            )

                            pattern_details.append(
                                f"**{pattern_name}** ({confidence:.0%})\n{explanation}"
                            )

                        embed.add_field(
                            name="🔍 Detected Patterns",
                            value="\n\n".join(
                                pattern_details[:3]
                            ),  # Show top 3
                            inline=False,
                        )

                    # Show progression notes
                    if opinion.progression_notes:
                        embed.add_field(
                            name="📈 Growth Opportunities",
                            value=opinion.progression_notes,
                            inline=False,
                        )

                    # Reflection prompts
                    embed.add_field(
                        name="🤔 Reflection Questions",
                        value="• What assumptions did I make?\n"
                        "• What evidence supports my view?\n"
                        "• What would someone who disagrees say?\n"
                        "• How might I refine this opinion?",
                        inline=False,
                    )

                    embed.set_footer(
                        text=f"Opinion ID: {opinion_id} | Self-examination leads to wisdom"
                    )

                    await interaction.followup.send(embed=embed)

            except Exception as e:
                logger.error(f"Error in button reflect: {e}")
                await interaction.followup.send(
                    f"❌ Error retrieving opinion: {str(e)}"
                )


if __name__ == "__main__":
    if DISCORD_BOT_TOKEN:
        bot.run(DISCORD_BOT_TOKEN)
    else:
        print("❌ DISCORD_BOT_TOKEN environment variable not set")
