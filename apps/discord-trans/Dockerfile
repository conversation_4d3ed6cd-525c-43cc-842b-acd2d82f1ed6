# syntax=docker/dockerfile:1
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies if needed
RUN apt-get update && apt-get install -y --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Copy project files
COPY pyproject.toml uv.lock ./
COPY src/ ./src/

# Install dependencies with uv (fast, modern)
RUN pip install uv && uv pip install .[discord]

# Set environment variables (override in Compose as needed)
ENV PYTHONUNBUFFERED=1

# Default command (override in Compose for dev/test)
CMD ["python", "-m", "discord_trans.trans_dis"]
