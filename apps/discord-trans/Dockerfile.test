FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the entire monorepo from parent context (needed for local package dependencies)
COPY ../.. .

# Install uv and hatch
RUN pip install uv hatch

# Set working directory to discord-trans app
WORKDIR /app/apps/discord-trans

# Install local package dependencies manually (since we're in Docker)
RUN echo "Installing local packages..." && \
    pip install -e /app/packages/discord-trans-db && \
    pip install -e /app/packages/tran-hits && \
    pip install -e .

# Run tests
RUN echo "Testing discord-trans app..." && hatch run test

# Success message
RUN echo "🎉 Discord bot tests passed in Docker!"
