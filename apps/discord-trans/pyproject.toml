[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "discord-trans"
dynamic = ["version"]
description = ''
readme = "README.md"
requires-python = ">=3.8"
license = "MIT"
keywords = []
authors = [{ name = "Ideas", email = "<EMAIL>" }]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.8",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
]
dependencies = [
    # Local packages will be installed manually with pip install -e
    # "tran-hits",
    # "discord-trans-db",
]

[project.urls]
Documentation = "https://github.com/Ideas/discord-trans#readme"
Issues = "https://github.com/Ideas/discord-trans/issues"
Source = "https://github.com/Ideas/discord-trans"

# [tool.hatch.build.targets.wheel]
# packages = ["src/discord_trans", ???? "src/binary_trans", "src/search_trans_cli"]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.env]
requires = ["hatch-pip-compile"]

[tool.hatch.version]
path = "src/discord_trans/__about__.py"

[tool.hatch.envs.default]
# Simplified: Single environment for Discord bot project
features = ["dev", "test", "discord"]
type = "pip-compile"
pip-compile-resolver = "uv"
extra-dependencies = [
    "sqlalchemy[asyncio]>=2.0.0",
    "aiosqlite>=0.19.0",
]

[tool.hatch.envs.default.scripts]
# HATCH STANDARD COMMANDS - REQUIRED
test = "pytest {args}"
test-cov = "pytest --cov=src {args}"
test-v = "pytest -v {args}"

# Development commands
dev-editable = "pip install -e . && pip install -e ../../packages/discord-trans-db && pip install -e ../../packages/tran-hits"
start-bot = "python src/discord_trans/unified_bot.py"
start-game-bot = "python src/discord_trans/bot_launcher.py game"
start-search-bot = "python src/discord_trans/bot_launcher.py search"

[tool.hatch.envs.types]
extra-dependencies = ["mypy>=1.0.0"]

[tool.hatch.envs.types.scripts]
dev-editable = "pip install -e ."
check = "mypy --install-types --non-interactive {args:src/discord_trans tests}"

[tool.coverage.run]
source_pkgs = ["discord_trans", "tests"]
branch = true
parallel = true
omit = ["src/discord_trans/__about__.py"]

[tool.coverage.paths]
discord_trans = ["src/discord_trans", "*/discord-trans/src/discord_trans"]
tests = ["tests", "*/discord-trans/tests"]

[tool.coverage.report]
exclude_lines = ["no cov", "if __name__ == .__main__.:", "if TYPE_CHECKING:"]

[project.optional-dependencies]
test = ["pytest>=8.0.0", "pytest-asyncio>=0.21.0"]
dev = ["pytest>=8.0.0", "pytest-asyncio>=0.21.0"]
# toggle off when needed (instead of deleting)
discord = ["discord.py>=2.3.2"]
# discord = ["discord.py>=2.3.2"]
