{"name": "web", "version": "0.2.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3006", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.12.0", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@repo/ui": "workspace:*", "@tanstack/react-query": "^5.75.2", "@tanstack/react-query-devtools": "^5.75.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next": "^15.1.6", "next-themes": "^0.4.4", "react": "^19.1.0", "react-dom": "^19.1.0", "shadcn": "^2.5.0", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.14.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.25.0", "turbo": "^2.5.1", "typescript": "5.8.2"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}