# Common definitions for web service
services:
  web: # Generic service name to be extended
    build:
      context: ../../
      dockerfile: ./apps/web/Dockerfile
      # No target here, as it will be overridden by dev/prod files
    restart: unless-stopped
    networks:
      - shared_network
      - app_network

volumes:
  logs:
  meta_media:

networks:
  shared_network:
    external: true
  app_network:
    external: true
