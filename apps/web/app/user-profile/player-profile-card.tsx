"use client";
import { useState } from "react";
import { Card, CardContent, CardHeader } from "@repo/ui/components/card";
import { Button } from "@repo/ui/components/button";

const Dial = ({ label, value, max = 100, min = 0 }: { label: string; value: number; max: number; min: number }) => {
  const rotation = ((value - min) / (max - min)) * 180 - 90; // Convert value to a rotation angle
  
  return (
    <div className="relative flex items-center justify-center w-32 h-32">
      <div className="absolute w-full h-full rounded-full border-4 border-gray-300"></div>
      <div
        style={{
          transform: `rotate(${rotation}deg)`,
        }}
        className="absolute w-1/2 h-1/2 bg-blue-500 rounded-full origin-bottom"
      />
      <div className="absolute text-center text-xl">{label}</div>
      <div className="absolute bottom-2 text-lg">{value}</div>
    </div>
  );
};

const PlayerProfileCard = () => {
  const [scrutiny, setScrutiny] = useState(40);
  const [trust, setTrust] = useState(75);
  const [vudu, setVudu] = useState(50);

  return (
    <Card className="rounded-2xl shadow-lg border border-gray-200">
      <CardHeader className="text-center">
        <h2 className="text-xl font-bold">Vudology Player</h2>
      </CardHeader>
      <CardContent className="space-y-6">
        <Dial label="Scrutiny" value={scrutiny} max={100} min={0} />
        <Dial label="Trust" value={trust} max={100} min={0} />
        <Dial label="VuDu" value={vudu} max={100} min={0} />
        
        <div className="flex justify-evenly mt-4">
          <Button onClick={() => setScrutiny(scrutiny + 10)}>Increase Scrutiny</Button>
          <Button onClick={() => setTrust(trust + 10)}>Increase Trust</Button>
          <Button onClick={() => setVudu(vudu + 10)}>Increase VuDu</Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PlayerProfileCard;
