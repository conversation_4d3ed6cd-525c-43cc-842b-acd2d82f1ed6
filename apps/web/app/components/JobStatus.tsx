"use client"

import { useState, useEffect } from 'react';

interface JobStatusProps {
  jobId: string;
}

interface Job {
  jobId: string;
  jobDate: number;
  corpus: string;
  userId: string;
  trailId: string;
  redisStream: string | null;
  plEtag: string;
  kind: string;
  etag: string;
  channelId: string;
  videoId: string;
  videoUrl: string;
  videoDate: string;
  thumb: string;
  title: string;
  description: string;
  tags: string[];
  fileStatusFlag: number;
  langWant: string | null;
  langGot: string | null;
  createdAt: string;
}

const JobStatus: React.FC<JobStatusProps> = ({ jobId }) => {
  const [job, setJob] = useState<Job | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchJobStatus = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`http://localhost:8001/api/v1/jobs/${jobId}`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setJob(data);
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchJobStatus();
    const intervalId = setInterval(fetchJobStatus, 5000); // Poll every 5 seconds

    return () => clearInterval(intervalId); // Cleanup interval on unmount
  }, [jobId]);

  if (loading) {
    return <p>Loading job status...</p>;
  }

  if (error) {
    return <p>Error: {error}</p>;
  }

  if (!job) {
    return <p>Job not found.</p>;
  }

  return (
    <div>
      <h3>Job Status:</h3>
      <p>Job ID: {job.jobId}</p>
      <p>Title: {job.title}</p>
      <p>Status: {job.fileStatusFlag}</p>
      {job.redisStream && <p>Redis Stream: {job.redisStream}</p>}
    </div>
  );
};

export default JobStatus;
