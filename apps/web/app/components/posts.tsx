'use client'

import { useSession } from "@clerk/nextjs";
import { useQuery, useMutation } from "@tanstack/react-query";

type Post = {
  userId: number;
  id: number;
  title: string;
  body: string;
}

async function fetchPosts(): Promise<Post[]> {
  const res = await fetch("https://jsonplaceholder.typicode.com/posts");
  return res.json();
}

export function UserPosts() {
  const { isLoaded, session } = useSession();
  const { data, error, isLoading } = useQuery({ queryKey: ["posts"], queryFn: fetchPosts });

  return (
    <div className="shadow overflow-hidden sm:rounded-lg" style={{ boxShadow: `0px 20px 24px -4px rgba(16, 24, 40, 0.08)` }}>
      <div className="grid p-8 gap-16">
        <div className="text-xl font-semibold text-gray-900">Posts</div>
      </div>

      {error && (<div className="text-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">{error.message}</div>)}

      {!isLoading && isLoaded && session ? (
        <div className="pb-6 max-h-96 overflow-auto">
          {data?.map((post) => (

            <div className="px-8" key={post.id}>
              <div className="text-sm font-semibold py-1">{post.title}</div>
              <div className="mt-1 text-sm text-gray-600 sm:mt-0 sm:col-span-2 flex gap-2 py-2">
                {post.body}
              </div>
            </div>

          ))}
        </div>
      ) : (
        <div className="text-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">Loading posts ...</div>
      )}

    </div>
  );
}
