'use client'

import { Fragment } from "react";
import { useSession } from "@clerk/nextjs";
import { useQuery } from "@tanstack/react-query";
// import { useQuery, useMutation } from "@tanstack/react-query";
import { SearchResult } from "../types";

async function fetchTrails(sessionToken: string): Promise<SearchResult> {
  const res = await fetch(process.env.NEXT_PUBLIC_API_URL + "/api/v2/trail_sr_moch", { 
    headers: {
      'Authorization': `Bearer ${sessionToken}`,
      'Content-Type': 'application/json',
    },
  });

  return res.json();
}

export function UserTrails() {
  const { isLoaded, session } = useSession();

  const { data, error, isLoading } = useQuery({
    queryKey: ["trails"],
    queryFn: async () => {
      if (session) {
        const token = await session.getToken();
        if (token) {
          return fetchTrails(token); 
        }
      }
      throw new Error("Session or token is not available");
    },
    enabled: isLoaded && !!session,
  });

  // if (!session) {
  //   return (
  //     <div className="text-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
  //       Please Sign in
  //     </div>
  //   );
  // }

  return (
    <div className="shadow overflow-hidden sm:rounded-lg" style={{ boxShadow: `0px 20px 24px -4px rgba(16, 24, 40, 0.08)` }}>
      <div className="grid p-8 gap-16">
        <div className="text-xl font-semibold text-gray-900">Trails</div>
      </div>

      {error && (<div className="text-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">{error.message}</div>)}

      {!isLoading && isLoaded && session && data && data.media && data.media.search_hits && data.media.search_hits.clips ? (
        <div className="pb-6 max-h-96 overflow-auto">
          <h2>Media Record</h2>
          <ul>
            <li><strong>Title:</strong> {data.media.title}</li>
            <li><strong>Details:</strong> {data.media.details}</li>
            <li><strong>Kind:</strong> {data.media.kind}</li>
            <li><strong>Source:</strong> {data.media.source}</li>
            <li><strong>Pub Date:</strong> {data.media.pub_date}</li>
            <li><strong>Inertia:</strong> {data.media.inertia}</li>
            <li><strong>Likes:</strong> {data.media.likes}</li>
            <li><strong>Thumb:</strong> <img src={data.media.thumb} alt="Thumbnail" /></li>
          </ul>

          <h2>Search Hits</h2>
          <ul>

            {data.media.search_hits.clips.map((clip, index) => (
              <li key={index}>

                <strong>Clip {index + 1}:</strong>
                <ul>
                  {clip.hit && clip.link && (
                    <Fragment>
                      <li><strong>Link:</strong> <a href={clip.link} target="_blank" rel="noopener noreferrer">{clip.link}</a></li>
                      <li><strong>Start:</strong> {clip.hit.start}</li>
                      <li><strong>Duration:</strong> {clip.hit.duration}</li>
                      <li><strong>Text:</strong> {clip.hit.text}</li>
                    </Fragment>
                  )}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className="text-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">Loading trails ...</div>
      )}
    </div>
  );
}

// {
//   "media": {
//       "ver": "1",
//       "title": "Caller: Am I Using The Black Swan Fallacy?",
//       "details": "Check out my book, Was Hitler an Atheist? https://owenmorgan.com/whaa In this video, I answer a question from a caller. Are they using the Black Swan fallacy when they reject the idea that burning bushes or snakes can talk?\n\n\nMerch Shop: https://teespring.com/stores/telltaleatheist\n\nTwitter: https://twitter.com/telltaleatheist\n\nSubscribe to the Email list: https://telltaleatheist.com/subscribe\nPatreon: https://www.patreon.com/telltaleatheist\n\nAll links to my channel can be found at https://owenmorgan.com\n\nPodcast on iTunes: https://podcasts.apple.com/us/podcast/telltale-atheist-podcast/id1399333418\n\nPodcast on SoundCloud: https://soundcloud.com/user-450959803\n\nPodcast on Google Play: https://play.google.com/music/listen?u=0#/ps/I4syo4revixssvwypbxknebgapa\n\nPodcast on YouTube: \nhttps://www.youtube.com/channel/UCo6JSNp6SuUKf-yiaBQReNA\n\n\n\nSocial Media:\n\nSubscribe to the Email list: https://telltaleatheist.com/subscribe\nPatreon: https://www.patreon.com/telltaleatheist\n\nTwitter: https://twitter.com/telltaleatheist\n\nDiscord:  https://owenmorgan.com/discord\n\nPayPal:  http://paypal.me/telltaleatheist",
//       "kind": "youtube#playlistItem",
//       "source": "PN0jF1LuHi8",
//       "pub_date": "2020-01-13T21:29:46Z",
//       "inertia": "0",
//       "likes": "0",
//       "thumb": "https://i.ytimg.com/vi/PN0jF1LuHi8/default.jpg",
//       "search_hits": {
//           "video_id": "PN0jF1LuHi8",
//           "clips": [
//               {
//                   "ver": "3.9",
//                   "likes": 0,
//                   "hit": {
//                       "start": 148.93,
//                       "duration": 8.25,
//                       "text": "to me that a burning bush can talk or"
//                   },
//                   "link": "https://www.youtube.com/watch?v=PN0jF1LuHi8&t=148"
//               },
//               {
//                   "ver": "3.9",
//                   "likes": 0,
//                   "hit": {
//                       "start": 172.09,
//                       "duration": 7.649,
//                       "text": "came to earth and made this burning bush"
//                   },
//                   "link": "https://www.youtube.com/watch?v=PN0jF1LuHi8&t=172"
//               }
//           ]
//       },
//       "search_id": "87ce85db2a2f45a0acb3c330dd89aad5"
//   }
// }
