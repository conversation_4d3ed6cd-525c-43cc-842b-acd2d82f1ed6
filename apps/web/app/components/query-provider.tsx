"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"    
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { ReactNode, useState } from "react"

interface Props {
    children: ReactNode
}

export function QueryProvider({ children }: Props) {
    const [queryClient] = useState(() => new QueryClient()) // one to share for cache to work
    return <QueryClientProvider client={queryClient}>
        {children}
        <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
}