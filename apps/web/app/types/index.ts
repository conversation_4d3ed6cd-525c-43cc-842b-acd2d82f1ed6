// ============================================================================
// CORE DATA CONTRACTS - Frontend/Backend Alignment
// ============================================================================

// ----------------------------------------------------------------------------
// User & Profile Types
// ----------------------------------------------------------------------------

export interface UserProfile {
  id: string;
  discord_id?: string;
  clerk_id?: string;
  username: string;
  display_name?: string;
  skill_level: number;
  temperament?: string;
  humor?: string;
  trust_level: number;
  communication_style?: string;
  sentiment_tendency?: string;
  preferred_topics?: string[];
  experience_points: number;
  created_at: string;
  updated_at: string;
}

export interface WisdomDials {
  scrutiny: number;
  trust: number;
  patience: number;
  frustration: number;
}

// ----------------------------------------------------------------------------
// Opinion Assessment & Wisdom Types
// ----------------------------------------------------------------------------

export interface OpinionAssessment {
  id: string;
  discord_user_id: string;
  opinion_text: string;
  target_type: "idea" | "thing" | "person" | "concept";
  target_reference?: string;
  logical_patterns?: Record<string, any>;
  wisdom_indicators?: Record<string, any>;
  complexity_level: number; // 1-5
  experience_points_earned: number;
  created_at: string;
}

export interface WisdomTrail {
  id: string;
  topic: string;
  description?: string;
  meta_level: number; // 0-5
  trail_data: Record<string, any>;
  created_by_user_id: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface OpinionReflection {
  id: string;
  opinion_assessment_id: string;
  reflection_text: string;
  reflection_type: "self" | "peer" | "mentor";
  insights_gained?: Record<string, any>;
  created_at: string;
}

// ----------------------------------------------------------------------------
// Fallacy & Claim Card Types
// ----------------------------------------------------------------------------

export interface FallacyCard {
  id: string;
  name: string;
  description: string;
  category: string;
  rarity: "common" | "uncommon" | "rare" | "epic" | "legendary";
  meta_level: number;
  educational_value: string;
  created_at: string;
}

export interface ClaimCard {
  id: string;
  discord_user_id: string;
  claim_text: string;
  meta_level: number; // 0-5
  complexity_score: number;
  evidence_strength: number;
  created_at: string;
}

// ----------------------------------------------------------------------------
// Transcript & Search Types (Legacy + Enhanced)
// ----------------------------------------------------------------------------

export interface SearchHit {
  start: number | null;
  duration: number | null;
  text: string | null;
}

export interface Clip {
  ver?: string;
  likes?: number;
  hit?: SearchHit | null;
  link?: string | null;
}

export interface Clips {
  video_id?: string | null;
  clips?: Clip[];
}

export interface Media {
  ver: string;
  title: string;
  details: string;
  kind: string;
  source: string;
  pub_date: string;
  inertia: string;
  likes: string;
  thumb: string;
  search_hits: Clips;
  search_id: string;
}

export interface SearchResult {
  media?: Media | null;
}

export interface SearchResults {
  search_id: string;
  medis?: Media[];
}

export interface CachedTranscript {
  id: string;
  video_id: string;
  title?: string;
  transcript_json: Record<string, any>;
  slurp_text?: string;
  character_count: number;
  created_at: string;
}

// ----------------------------------------------------------------------------
// API Request/Response Types
// ----------------------------------------------------------------------------

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  has_next: boolean;
  has_prev: boolean;
}

// User API
export interface CreateUserRequest {
  discord_id?: string;
  clerk_id?: string;
  username: string;
  display_name?: string;
}

export interface UpdateUserProfileRequest {
  display_name?: string;
  temperament?: string;
  humor?: string;
  communication_style?: string;
  preferred_topics?: string[];
}

// Opinion API
export interface SubmitOpinionRequest {
  opinion_text: string;
  target_type: "idea" | "thing" | "person" | "concept";
  target_reference?: string;
}

export interface OpinionListRequest {
  user_id?: string;
  target_type?: string;
  limit?: number;
  offset?: number;
}

// Wisdom Trail API
export interface CreateWisdomTrailRequest {
  topic: string;
  description?: string;
  meta_level: number;
  trail_data: Record<string, any>;
  is_public: boolean;
}

export interface WisdomTrailSearchRequest {
  topic?: string;
  meta_level?: number;
  is_public?: boolean;
  created_by_user_id?: string;
}

// Transcript API
export interface TranscriptSearchRequest {
  query: string;
  video_ids?: string[];
  limit?: number;
}

export interface CacheTranscriptRequest {
  video_id: string;
  transcript_data: Record<string, any>;
  title?: string;
}

// ----------------------------------------------------------------------------
// User Clip Trails - NEW Integration Types
// ----------------------------------------------------------------------------

export interface ClipNode {
  id: string;
  video_id: string;
  start_time: number;
  duration: number;
  text_excerpt: string;

  // User annotations
  user_note?: string;
  wisdom_tag?: string;
  fallacy_flag?: string;

  // Trail context
  sequence_order: number;
  transition_note?: string;

  // Metadata
  created_at: string;
  created_by_user_id: string;
}

export interface UserClipTrail {
  id: string;
  created_by_user_id: string;
  title: string;
  description?: string;

  // Trail metadata
  topic: string;
  meta_level: number; // 0-5 philosophical depth
  trail_type: "personal" | "public" | "collaborative";

  // Clip sequence
  clips: ClipNode[];

  // Wisdom integration
  opinion_assessments: string[];
  fallacy_patterns: string[];
  wisdom_insights: Record<string, any>;

  // Social features
  is_public: boolean;
  fork_count: number;
  like_count: number;
  view_count: number;

  // Timestamps
  created_at: string;
  updated_at: string;
}

export interface UnifiedUser {
  // Core identity
  id: string;
  clerk_id?: string;
  discord_id?: string;
  username: string;

  // Profile data (from Discord bot)
  wisdom_dials: WisdomDials;
  skill_level: number;
  experience_points: number;

  // Trail preferences
  preferred_topics: string[];
  trail_style: "direct" | "serendipitous" | "meta";
  curation_level: number;
}

// Trail API Requests
export interface CreateTrailRequest {
  title: string;
  description?: string;
  topic: string;
  meta_level: number;
  trail_type: "personal" | "public" | "collaborative";
  is_public: boolean;
}

export interface AddClipToTrailRequest {
  video_id: string;
  start_time: number;
  duration: number;
  text_excerpt: string;
  user_note?: string;
  wisdom_tag?: string;
  sequence_order: number;
  transition_note?: string;
}

export interface TrailSearchRequest {
  topic?: string;
  meta_level?: number;
  trail_type?: string;
  created_by_user_id?: string;
  is_public?: boolean;
  limit?: number;
  offset?: number;
}

export interface ForkTrailRequest {
  original_trail_id: string;
  title: string;
  description?: string;
  modifications?: string; // Description of what changed
}

export interface ClipExtractionRequest {
  video_id: string;
  start_time: number;
  duration: number;
  context_before?: number; // Seconds of context before
  context_after?: number;  // Seconds of context after
}

export interface TrailFromOpinionRequest {
  opinion_assessment_id: string;
  title: string;
  search_query: string;
  max_clips?: number;
}
