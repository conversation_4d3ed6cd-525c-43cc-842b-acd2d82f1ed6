/**
 * Mock API Layer for Frontend Development
 * Provides mock responses that match the FE/BE contract while backend is being implemented
 */

import { 
  UserProfile, 
  WisdomDials, 
  OpinionAssessment, 
  WisdomTrail, 
  UserClipTrail, 
  ClipNode, 
  UnifiedUser,
  ApiResponse,
  PaginatedResponse,
  CreateTrailRequest,
  AddClipToTrailRequest,
  TrailSearchRequest,
  SubmitOpinionRequest
} from '../types';

// Mock data generators
const generateMockUser = (id: string): UnifiedUser => ({
  id,
  clerk_id: `clerk_${id}`,
  discord_id: `discord_${id}`,
  username: `user_${id}`,
  wisdom_dials: {
    scrutiny: Math.floor(Math.random() * 100),
    trust: Math.floor(Math.random() * 100),
    patience: Math.floor(Math.random() * 100),
    frustration: Math.floor(Math.random() * 100),
  },
  skill_level: Math.floor(Math.random() * 100) + 1,
  experience_points: Math.floor(Math.random() * 10000),
  preferred_topics: ['philosophy', 'science', 'logic'],
  trail_style: 'direct',
  curation_level: Math.floor(Math.random() * 10) + 1,
});

const generateMockClip = (index: number): ClipNode => ({
  id: `clip_${index}`,
  video_id: `video_${Math.floor(Math.random() * 100)}`,
  start_time: Math.random() * 3600,
  duration: Math.random() * 60 + 10,
  text_excerpt: `This is a sample text excerpt from clip ${index}. It contains meaningful content that demonstrates the concept being discussed.`,
  user_note: `User annotation for clip ${index}`,
  wisdom_tag: ['logic', 'fallacy', 'insight', 'question'][Math.floor(Math.random() * 4)],
  sequence_order: index,
  transition_note: `This clip follows naturally because...`,
  created_at: new Date().toISOString(),
  created_by_user_id: 'user_1',
});

const generateMockTrail = (id: string): UserClipTrail => ({
  id,
  created_by_user_id: 'user_1',
  title: `Sample Trail ${id}`,
  description: `This is a sample trail demonstrating the concept of ${id}`,
  topic: ['philosophy', 'science', 'logic', 'ethics'][Math.floor(Math.random() * 4)],
  meta_level: Math.floor(Math.random() * 6),
  trail_type: ['personal', 'public', 'collaborative'][Math.floor(Math.random() * 3)] as any,
  clips: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, (_, i) => generateMockClip(i)),
  opinion_assessments: [`opinion_${Math.floor(Math.random() * 100)}`],
  fallacy_patterns: ['ad_hominem', 'straw_man', 'false_dichotomy'],
  wisdom_insights: {
    complexity_score: Math.floor(Math.random() * 10) + 1,
    wisdom_level: Math.floor(Math.random() * 5) + 1,
  },
  is_public: Math.random() > 0.5,
  fork_count: Math.floor(Math.random() * 50),
  like_count: Math.floor(Math.random() * 200),
  view_count: Math.floor(Math.random() * 1000),
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
});

const generateMockOpinion = (id: string): OpinionAssessment => ({
  id,
  discord_user_id: 'user_1',
  opinion_text: `This is a sample opinion about ${id}. It demonstrates various logical patterns and reasoning structures.`,
  target_type: ['idea', 'thing', 'person', 'concept'][Math.floor(Math.random() * 4)] as any,
  target_reference: `Reference to ${id}`,
  logical_patterns: {
    fallacies_detected: ['confirmation_bias'],
    strengths: ['evidence_based', 'logical_structure'],
  },
  wisdom_indicators: {
    humility: Math.floor(Math.random() * 10) + 1,
    nuance: Math.floor(Math.random() * 10) + 1,
  },
  complexity_level: Math.floor(Math.random() * 5) + 1,
  experience_points_earned: Math.floor(Math.random() * 50) + 10,
  created_at: new Date().toISOString(),
});

// Mock API functions
export const mockApi = {
  // User endpoints
  async getUser(userId: string): Promise<ApiResponse<UnifiedUser>> {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
    return {
      success: true,
      data: generateMockUser(userId),
      message: 'User retrieved successfully',
    };
  },

  async getUserWisdomDials(userId: string): Promise<ApiResponse<WisdomDials>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return {
      success: true,
      data: {
        scrutiny: 65,
        trust: 80,
        patience: 45,
        frustration: 30,
      },
      message: 'Wisdom dials retrieved successfully',
    };
  },

  // Trail endpoints
  async createTrail(request: CreateTrailRequest): Promise<ApiResponse<UserClipTrail>> {
    await new Promise(resolve => setTimeout(resolve, 800));
    const newTrail = generateMockTrail(`trail_${Date.now()}`);
    newTrail.title = request.title;
    newTrail.description = request.description;
    newTrail.topic = request.topic;
    newTrail.meta_level = request.meta_level;
    newTrail.trail_type = request.trail_type;
    newTrail.is_public = request.is_public;
    
    return {
      success: true,
      data: newTrail,
      message: 'Trail created successfully',
    };
  },

  async getTrail(trailId: string): Promise<ApiResponse<UserClipTrail>> {
    await new Promise(resolve => setTimeout(resolve, 400));
    return {
      success: true,
      data: generateMockTrail(trailId),
      message: 'Trail retrieved successfully',
    };
  },

  async searchTrails(request: TrailSearchRequest): Promise<PaginatedResponse<UserClipTrail>> {
    await new Promise(resolve => setTimeout(resolve, 600));
    const trails = Array.from({ length: request.limit || 20 }, (_, i) => 
      generateMockTrail(`trail_${i}`)
    );
    
    return {
      items: trails,
      total: 100,
      page: Math.floor((request.offset || 0) / (request.limit || 20)) + 1,
      per_page: request.limit || 20,
      has_next: true,
      has_prev: (request.offset || 0) > 0,
    };
  },

  async addClipToTrail(trailId: string, request: AddClipToTrailRequest): Promise<ApiResponse<ClipNode>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newClip: ClipNode = {
      id: `clip_${Date.now()}`,
      video_id: request.video_id,
      start_time: request.start_time,
      duration: request.duration,
      text_excerpt: request.text_excerpt,
      user_note: request.user_note,
      wisdom_tag: request.wisdom_tag,
      sequence_order: request.sequence_order,
      transition_note: request.transition_note,
      created_at: new Date().toISOString(),
      created_by_user_id: 'user_1',
    };
    
    return {
      success: true,
      data: newClip,
      message: 'Clip added to trail successfully',
    };
  },

  async forkTrail(trailId: string, title: string): Promise<ApiResponse<UserClipTrail>> {
    await new Promise(resolve => setTimeout(resolve, 700));
    const forkedTrail = generateMockTrail(`forked_${trailId}`);
    forkedTrail.title = `${title} (Fork)`;
    forkedTrail.fork_count = 0;
    
    return {
      success: true,
      data: forkedTrail,
      message: 'Trail forked successfully',
    };
  },

  // Opinion endpoints
  async submitOpinion(request: SubmitOpinionRequest): Promise<ApiResponse<OpinionAssessment>> {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Longer delay for AI analysis
    const newOpinion = generateMockOpinion(`opinion_${Date.now()}`);
    newOpinion.opinion_text = request.opinion_text;
    newOpinion.target_type = request.target_type;
    newOpinion.target_reference = request.target_reference;
    
    return {
      success: true,
      data: newOpinion,
      message: 'Opinion submitted and analyzed successfully',
    };
  },

  async getUserOpinions(userId: string, limit = 20): Promise<PaginatedResponse<OpinionAssessment>> {
    await new Promise(resolve => setTimeout(resolve, 400));
    const opinions = Array.from({ length: limit }, (_, i) => 
      generateMockOpinion(`opinion_${i}`)
    );
    
    return {
      items: opinions,
      total: 50,
      page: 1,
      per_page: limit,
      has_next: true,
      has_prev: false,
    };
  },

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    await new Promise(resolve => setTimeout(resolve, 100));
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  },
};

// Environment flag to use mock API
export const USE_MOCK_API = process.env.NODE_ENV === 'development' && 
  process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';
