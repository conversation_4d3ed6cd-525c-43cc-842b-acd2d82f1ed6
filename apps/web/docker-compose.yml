services:
  web-prod:
    container_name: web-prod
    image: ${IMAGE}
    build:
      context: .
      dockerfile: Dockerfile
      target: prod-runner
      # tags:
      #  - ideas/udu:prod-0.4.2
    env_file:
      - /home/<USER>/repo/knowtrails/apps/web/.env.development.local
    ports:
      - 3000:3000
    networks:
      - app_network
      - shared_network # Added shared_network
    depends_on:
      - postgres-prod
    restart: always

  web-dev:
    container_name: web-dev
    image: ${IMAGE}
    build:
      context: .
      dockerfile: Dockerfile
      target: dev-runner
    env_file:
      - /home/<USER>/repo/knowtrails/apps/web/.env.development.local
    ports:
      - 3000:3000
    volumes:
      - .:/app # Mount the entire repository root for live code changes
      - /app/node_modules # Anonymous volume for node_modules to prevent host interference
      - /app/apps/web/.next # Anonymous volume for Next.js build output
    networks:
      - app_network
      - shared_network # Added shared_network
    depends_on:
      - postgres-dev
    restart: unless-stopped

# TODO: check:
# need to run this command to create docker network allows 
# containers to communicate with each other, 
# by using their container name as a hostname
#
#    docker network create app_network
# 
networks:
  app_network:
    external: true
  shared_network: # Added shared_network definition
    external: true

#LATER
  # duplicate start project
  # docs:
  #   container_name: docs
  #   build:
  #     context: .
  #     dockerfile: ./apps/docs/Dockerfile
  #   restart: always
  #   ports:
  #     - 3001:3000
  #   networks:
  #     - app_network
