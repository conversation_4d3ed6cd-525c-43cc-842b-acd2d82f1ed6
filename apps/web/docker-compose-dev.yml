# include:
#   - ./../../docker-compose.shared-services-dev.yml

# Development-specific overrides for web
services:
  web: # Extends the 'web' service from base.yml
    container_name: "web-dev"
    mem_limit: 200m
    cpu_shares: 306
    image: ${IMAGE} # IMAGE will be passed by the tooling script
    build:
      target: dev-runner # Override target for dev build
    # No .env-dev file found for web, so omitting env_file
    volumes:
      - .:/app # Mount apps/web (the current directory) to /app in container
      - ./logs:/app/logs:rw
      - ./meta_media:/app/meta_media:rw
    ports:
      - 3055:3000
