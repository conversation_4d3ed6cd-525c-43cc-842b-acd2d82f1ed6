# include:
#   - ./../../docker-compose.shared-services-prod.yml

# Production-specific overrides for web
services:
  web: # Extends the 'web' service from base.yml
    container_name: "web-prod"
    mem_limit: 200m
    cpu_shares: 306
    image: ${IMAGE} # IMAGE will be passed by the tooling script
    build:
      target: prod-runner # Override target for prod build
    # No .env-prod file found for web, so omitting env_file
    volumes:
      - ./app:/app/app
      - ./lib:/app/lib
      - ./public:/app/public
      - ./logs:/app/logs:rw
      - ./meta_media:/app/meta_media:rw
    ports:
      - "3050:3000"
