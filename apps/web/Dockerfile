## define the base image for developing the image stages.
# FROM node:alpine AS base
# FROM node:23.11.0-slim AS base

# ===========================================================================
# base image for preparing the dependencies stage
# note:
# "validators" can give different reports of image vulnerabilities
# ie : node:24-slim AS base get a score of 7/7 
#       node@sha256.. AS base flagging with score of 6/7, "base out of date"
FROM node:24-slim AS base
ARG DEBUG=false
# lock down in production by using the digest invariant syntax
# FROM node@sha256:f403f3b5054f8f35ebe8dd167e0c608945a8fd992f3d278d2a8652b58b80dc92 AS base
# ===========================================================================
RUN apt update && apt install -y libc6 && rm -rf /var/lib/apt/lists/*
RUN npm install turbo -g

# ===========================================================================
# Production image prep and dependencies stage
FROM base AS deps
# ===========================================================================
WORKDIR /app

# Repo root - docker ignores everything
COPY ./turbo.json /app/
COPY ./pnpm-workspace.yaml /app/
COPY ./pnpm-lock.yaml /app/
COPY ./apps/web/ /app/apps/web/
COPY ./package.json /app/
COPY ./packages/ /app/packages/
# COPY ./packages/eslint-config/ /app/packages/eslint-config/
# COPY ./packages/typescript-config/ /app/packages/typescript-config/
# COPY ./packages/ui/ /app/packages/ui/

# Note: could repeat pattern in other projects Apps to maintain this way ...
# COPY apps/docs/ /app/apps/docs/
# COPY apps/trans_api/ /app/apps/trans_api/

# Inspect to see what copied and security risks
# RUN find . -mindepth 1 -maxdepth 5 \( ! -path "./node_modules/*" -a ! -path "./.git/*" \) -print

# Note: docker prune operation in turbo!!
# [Prune --docker makes two directories](https://turbo.build/repo/docs/guides/tools/docker)
# 1: /app/out/json/ fo installing (deps stage)
# 2: /app/out/full/ for compiling source (install stage)
RUN turbo prune --scope=web --docker

# Installer stages
FROM base AS installer
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_d2FudGVkLXRpY2stNTIuY2xlcmsuYWNjb3VudHMuZGV2JA

WORKDIR /app

# COPY .gitignore .gitignore
COPY --from=deps /app/out/json/ .

# RUN find . -mindepth 1 -maxdepth 5 \( ! -path "./node_modules/*" -a ! -path "./.git/*" \) -print

# elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
# 

# ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Build the project
COPY --from=deps /app/out/full/ .
RUN echo "Running pnpm turbo build..."
RUN pnpm turbo build

# ===========================================================================
# Development image created in this runner stage
FROM base AS dev-runner
ARG DEBUG=true # Set DEBUG to true by default for dev
WORKDIR /app

# Install dependencies for dev-runner
COPY ./package.json /app/package.json
COPY ./pnpm-lock.yaml /app/pnpm-lock.yaml
COPY ./pnpm-workspace.yaml /app/pnpm-workspace.yaml
COPY ./turbo.json /app/turbo.json
COPY ./apps/web/package.json /app/apps/web/package.json
# Copy shared packages (e.g., packages/ui, packages/db) for local linking and dependency resolution
# This is necessary for pnpm to correctly resolve local workspace dependencies during development.
RUN mkdir -p /app/packages
COPY ./packages /app/packages/ 

RUN corepack enable pnpm && pnpm i --frozen-lockfile

EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# The actual application code will be mounted as a volume
CMD ["pnpm", "dev"]

# ===========================================================================
# Production image created in this runner stage 
FROM base AS prod-runner
# ===========================================================================
WORKDIR /app

ENV NODE_ENV=production

# Create a non-root user and group
RUN groupadd -r nextjs && useradd -r -g nextjs -d /app -s /usr/sbin/nologin nextjs

COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

USER nextjs
EXPOSE 3000
ENV PORT=3000

# apps/web/server.js: created by Hextjs build's standalone output
# https://nextjs.org/docs/pages/api-reference/config/next-config-js/output
ENV HOSTNAME="0.0.0.0"
CMD ["node", "apps/web/server.js"] 
# CMD ["npm", "start"]

# Note: consider if there should be a dev-runner docker image. 
# for now use 'turbo run dev' 
# see Makefile in repo root
