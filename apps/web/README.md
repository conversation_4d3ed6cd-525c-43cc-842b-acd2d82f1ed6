This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/create-next-app).

## Getting Started

First, run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

# Customizations

## Dark-mode

[install instructions](https://ui.shadcn.com/docs/dark-mode/next)

### warning installing next-themes

```
pnpm add next-themes
```
```
    knowtrails/apps/web on  main [!] 🎶 v20.18.0
    ➜  pnpm add next-themes
     WARN  4 deprecated subdependencies found: glob@7.2.3, inflight@1.0.6, lodash.get@4.4.2, rimraf@3.0.2
    ../..                                    |   +1 +
    ../..                                    | Progress: resolved 481, reused 454, downloaded 1, added 1, done

    dependencies:
    + next-themes 0.4.4

    Done in 4.5s
```

requirements needed: shadcn & tailwind (V4)
## tailwind v4
- [install instructions](https://tailwindcss.com/docs/installation/framework-guides/nextjs)
```
pnpm add tailwindcss @tailwindcss/postcss postcss
```
```
    knowtrails/apps/web on  main [!?] 🎶 v20.18.0 took 5s
    ➜  pnpm add tailwindcss @tailwindcss/postcss postcss
     WARN  4 deprecated subdependencies found: glob@7.2.3, inflight@1.0.6, lodash.get@4.4.2, rimraf@3.0.2
    ../..                                    |  +15 ++
    ../..                                    | Progress: resolved 513, reused 459, downloaded 11, added 15, done

    dependencies:
    + @tailwindcss/postcss 4.0.8
    + postcss 8.5.3
    + tailwindcss 4.0.8

    Done in 5.3s
```

## shadcn@canary
```
pnpm dlx shadcn@canary init
pnpm dlx shadcn@latest add button
pnpm dlx shadcn@latest add dropdown-menu
```

## Clerk

[install instructions](https://clerk.com/docs/quickstarts/nextjs)

```
pnpm add @clerk/nextjs
```


## Tanstack QueryProvider

- [course](https://query.gg/?s=tanstack)
- [install instructions](https://tanstack.com/query/latest/docs/framework/react/installation)

```
pnpm add @tanstack/react-query
```
```
     WARN  4 deprecated subdependencies found: glob@7.2.3, inflight@1.0.6, lodash.get@4.4.2, rimraf@3.0.2
    ../..                                    |   +2 +
    ../..                                    | Progress: resolved 579, reused 534, downloaded 2, added 2, done
```

- Not installed YET, confused about config edits
- [eslint for tanstack](https://tanstack.com/query/latest/docs/eslint/eslint-plugin-query)

```
### pnpm add -D @tanstack/eslint-plugin-query
```
