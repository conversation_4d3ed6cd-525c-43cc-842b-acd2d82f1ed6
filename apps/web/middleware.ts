import { clerkClient, clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";

// export default clerkMiddleware()

// const isPublicRoute = createRouteMatcher(["/", "/onboarding"])
const isPublicRoute = createRouteMatcher(["/"])

export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();
  // const { userId, sessionClaims, redirectToSignIn } = await auth();

  // If the user isn't signed in and the route is private, redirect to sign-in
  if (!userId && !isPublicRoute(req)) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }


  // if (userId) {
  //   // Initialize the Backend SDK
  //   const client = await clerkClient()
  //   // Get the user's full `Backend User` object
  //   const user = await client.users.getUser(userId)
  //   // console.log("user", user)
  // }
  // // Catch users who do not have `onboardingComplete: true` in their publicMetadata
  // // Redirect them to the /onboading route to complete onboarding
  // if (userId && !sessionClaims?.metadata?.onboardingComplete && req.nextUrl.pathname !== "/onboarding") {
  //   const onboardingUrl = new URL("/onboarding", req.url);
  //   return NextResponse.redirect(onboardingUrl);
  // }

  // If the user is logged in and the route is protected, let them view.
  if (userId && !isPublicRoute(req)) {
    return NextResponse.next();
  }


} // , { debug: process.env.NODE_ENV === 'development' }
);


export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}