def analyze_segment_sentiment(self, text: str, start_time: float, end_time: float) -> Dict[str, Any]:
        """Analyze sentiment for a single segment"""
        if not text.strip():
            return None
            
        # Get sentiment prediction
        result = self.sentiment_pipeline(text)[0]
        
        # Map model labels to our format
        label_mapping = {
            'LABEL_0': 'negative',
            'LABEL_1': 'neutral', 
            'LABEL_2': 'positive',
            'NEGATIVE': 'negative',
            'NEUTRAL': 'neutral',
            'POSITIVE': 'positive'
        }
        
        sentiment = label_mapping.get(result['label'], result['label'].lower())
        confidence = result['score']
        
        # Determine intensity based on keywords and confidence
        intensity = self._determine_intensity(text, confidence)
        
        # Extract trigger words
        trigger_words = self._extract_trigger_words(text, sentiment)
        
        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'start_time': start_time,
            'end_time': end_time,
            'intensity': intensity,
            'trigger_words': trigger_words,
            'text_analyzed': text
        }
    
    def _determine_intensity(self, text: str, confidence: float) -> str:
        """Determine sentiment intensity based on keywords and confidence"""
        text_lower = text.lower()
        
        # Check for intensity keywords
        for intensity, keywords in self.intensity_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                return intensity
        
        # Fallback to confidence-based intensity
        if confidence > 0.9:
            return 'strong'
        elif confidence > 0.7:
            return 'moderate'
        else:
            return 'mild'
    
    def _extract_trigger_words(self, text: str, sentiment: str) -> List[str]:
        """Extract words that likely triggered the sentiment"""
        triggers = self.trigger_keywords.get(sentiment, [])
        text_lower = text.lower()
        found_triggers = [word for word in triggers if word in text_lower]
        return found_triggers#!/usr/bin/env python3
import os
import time
import whisper
import json
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from transformers import pipeline
import torch
from dataclasses import dataclass
from typing import List, Optional, Dict, Any

@dataclass
class SentimentSpan:
    """Represents a single sentiment episode/spell with start and optional end"""
    sentiment: str          # 'positive', 'negative', 'neutral'
    confidence: float       # 0.0 to 1.0
    start_time: float      # When this spell begins (seconds)
    end_time: Optional[float] = None    # When this spell ends (seconds), None if ongoing
    trigger_words: List[str] = None     # Words that triggered this spell
    resolution_words: List[str] = None  # Words that ended/resolved this spell
    intensity: str = "moderate"         # 'mild', 'moderate', 'strong', 'extreme'
    spell_id: str = None               # Unique identifier for this spell
    
    def __post_init__(self):
        if self.trigger_words is None:
            self.trigger_words = []
        if self.resolution_words is None:
            self.resolution_words = []
        if self.spell_id is None:
            # Generate unique ID based on sentiment, start time, and confidence
            self.spell_id = f"{self.sentiment}_{int(self.start_time*100)}_{int(self.confidence*1000)}"
    
    @property
    def duration(self) -> Optional[float]:
        """Calculate duration if end time is known"""
        return self.end_time - self.start_time if self.end_time else None
    
    @property
    def is_ongoing(self) -> bool:
        """Check if this sentiment spell is still ongoing"""
        return self.end_time is None
    
    @property
    def is_resolved(self) -> bool:
        """Check if this sentiment spell has been resolved"""
        return self.end_time is not None

class SentimentTracker:
    """Tracks independent sentiment spells throughout audio, allowing overlaps and recurrence"""
    
    def __init__(self):
        self.spells: List[SentimentSpan] = []
        self.sentiment_pipeline = pipeline(
            "sentiment-analysis", 
            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
            device=0 if torch.cuda.is_available() else -1
        )
        
        # Resolution/compromise keywords that might end sentiment spells
        self.resolution_keywords = {
            'general': ['okay', 'alright', 'fine', 'whatever', 'moving on', 'anyway', 'but', 'however'],
            'positive_resolution': ['better now', 'feeling better', 'resolved', 'sorted out', 'fixed', 'good now'],
            'negative_resolution': ['giving up', 'done with', 'over it', 'enough', 'forget it', 'moving on']
        }
        
        # Trigger keywords for detecting new sentiment spells
        self.trigger_keywords = {
            'positive': ['love', 'great', 'amazing', 'wonderful', 'excellent', 'fantastic', 'excited', 'happy'],
            'negative': ['hate', 'terrible', 'awful', 'horrible', 'disgusting', 'annoying', 'angry', 'frustrated', 'upset']
        }
        
        # Intensity keywords
        self.intensity_keywords = {
            'extreme': ['furious', 'ecstatic', 'devastated', 'thrilled', 'enraged', 'overjoyed', 'absolutely', 'completely'],
            'strong': ['very', 'really', 'extremely', 'totally', 'definitely', 'seriously'],
            'moderate': ['quite', 'pretty', 'rather', 'fairly', 'somewhat'],
            'mild': ['slightly', 'a bit', 'kind of', 'sort of', 'maybe', 'perhaps']
        }
    
    def track_sentiment_spells(self, segments: List[Dict]) -> List[SentimentSpan]:
        """Track sentiment spells allowing for overlaps, recurrence, and resolution"""
        sentiment_spells = []
        active_spells = []  # Currently ongoing spells
        
        for segment in segments:
            analysis = self.analyze_segment_sentiment(
                segment['text'], 
                segment['start'], 
                segment['end']
            )
            
            if not analysis:
                continue
            
            current_time = analysis['start_time']
            current_sentiment = analysis['sentiment']
            
            # Check for resolution of existing active spells
            resolution_words = self._detect_resolution_words(segment['text'])
            if resolution_words:
                self._resolve_active_spells(active_spells, current_time, resolution_words)
            
            # Check if this is a new sentiment spell (not just continuation)
            is_new_spell = self._is_new_sentiment_spell(
                current_sentiment, 
                analysis, 
                active_spells
            )
            
            if is_new_spell:
                # Create new sentiment spell
                new_spell = SentimentSpan(
                    sentiment=current_sentiment,
                    confidence=analysis['confidence'],
                    start_time=current_time,
                    trigger_words=analysis['trigger_words'],
                    intensity=analysis['intensity']
                )
                
                sentiment_spells.append(new_spell)
                active_spells.append(new_spell)
                print(f"🆕 New {current_sentiment} spell started at {current_time:.1f}s")
            
            else:
                # Update existing spell of same sentiment (boost confidence, add triggers)
                existing_spell = self._find_active_spell(active_spells, current_sentiment)
                if existing_spell:
                    existing_spell.confidence = max(existing_spell.confidence, analysis['confidence'])
                    existing_spell.trigger_words.extend(analysis['trigger_words'])
                    # Remove duplicates
                    existing_spell.trigger_words = list(set(existing_spell.trigger_words))
        
        return sentiment_spells
    
    def _is_new_sentiment_spell(self, sentiment: str, analysis: Dict, active_spells: List[SentimentSpan]) -> bool:
        """Determine if this is a new sentiment spell or continuation of existing one"""
        
        # Check if there's already an active spell of this sentiment
        existing_spell = self._find_active_spell(active_spells, sentiment)
        
        if not existing_spell:
            return True  # No existing spell, definitely new
        
        # Check time gap - if significant gap, it's a new spell
        time_gap = analysis['start_time'] - existing_spell.start_time
        if time_gap > 30:  # 30 second gap threshold
            return True
        
        # Check for strong trigger words indicating new episode
        strong_triggers = self.trigger_keywords.get(sentiment, [])
        if any(trigger in analysis['trigger_words'] for trigger in strong_triggers):
            return True
        
        # Check confidence jump - significant increase might indicate new spell
        confidence_jump = analysis['confidence'] - existing_spell.confidence
        if confidence_jump > 0.3:  # 30% confidence increase
            return True
        
        return False  # Likely continuation of existing spell
    
    def _find_active_spell(self, active_spells: List[SentimentSpan], sentiment: str) -> Optional[SentimentSpan]:
        """Find the most recent active spell of given sentiment"""
        matching_spells = [spell for spell in active_spells if spell.sentiment == sentiment]
        return matching_spells[-1] if matching_spells else None
    
    def _detect_resolution_words(self, text: str) -> List[str]:
        """Detect words that might indicate resolution of sentiment"""
        text_lower = text.lower()
        resolution_words = []
        
        for category, keywords in self.resolution_keywords.items():
            found_words = [word for word in keywords if word in text_lower]
            resolution_words.extend(found_words)
        
        return resolution_words
    
    def _resolve_active_spells(self, active_spells: List[SentimentSpan], current_time: float, resolution_words: List[str]):
        """Resolve active spells that show signs of ending"""
        spells_to_resolve = []
        
        for spell in active_spells:
            # Only resolve spells that have been active for at least 5 seconds
            if current_time - spell.start_time > 5:
                spell.end_time = current_time
                spell.resolution_words = resolution_words
                spells_to_resolve.append(spell)
                print(f"✅ {spell.sentiment} spell resolved at {current_time:.1f}s (lasted {spell.duration:.1f}s)")
        
        # Remove resolved spells from active list
        for spell in spells_to_resolve:
            active_spells.remove(spell)
class WhisperBatchProcessor:
    def __init__(self):
        self.model = whisper.load_model(os.getenv('WHISPER_MODEL', 'base'))
        self.sentiment_tracker = SentimentTracker()
        self.input_dir = Path('/app/input')
        self.output_dir = Path('/app/output')
        self.processing_dir = Path('/app/processing')
        self.sleep_time = int(os.getenv('SLEEP_BETWEEN_FILES', '30'))
        
    def process_file(self, audio_file):
        print(f"Processing: {audio_file.name}")
        
        # Move to processing directory
        processing_file = self.processing_dir / audio_file.name
        audio_file.rename(processing_file)
        
        try:
            # Transcribe with Whisper
            print("🎵 Transcribing audio...")
            result = self.model.transcribe(str(processing_file))
            
            # Analyze sentiment spells
            print("😊 Analyzing sentiment spells...")
            sentiment_spells = self.sentiment_tracker.track_sentiment_spells(result.get('segments', []))
            
            # Create comprehensive output data
            output_data = {
                'filename': audio_file.name,
                'transcript': result['text'],
                'language': result.get('language', 'unknown'),
                'processed_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                
                # Original segments with timing
                'segments': result.get('segments', []),
                
                # Sentiment analysis results
                'sentiment_analysis': {
                    'total_spells': len(sentiment_spells),
                    'sentiment_spells': [
                        {
                            'spell_id': spell.spell_id,
                            'sentiment': spell.sentiment,
                            'confidence': round(spell.confidence, 3),
                            'start_time': round(spell.start_time, 2),
                            'end_time': round(spell.end_time, 2) if spell.end_time else None,
                            'duration': round(spell.duration, 2) if spell.duration else None,
                            'is_ongoing': spell.is_ongoing,
                            'is_resolved': spell.is_resolved,
                            'intensity': spell.intensity,
                            'trigger_words': spell.trigger_words,
                            'resolution_words': spell.resolution_words
                        }
                        for spell in sentiment_spells
                    ],
                    
                    # Summary statistics
                    'summary': self._generate_sentiment_summary(sentiment_spells)
                }
            }
            
            # Save transcription with sentiment analysis
            output_file = self.output_dir / f"{audio_file.stem}_analysis.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            # Also save a simplified CSV for easy analysis
            self._save_csv_summary(audio_file.stem, sentiment_spells)
            
            print(f"✅ Completed: {audio_file.name}")
            print(f"📊 Found {len(sentiment_spells)} sentiment spells")
            
            # Clean up processed file
            processing_file.unlink()
            
        except Exception as e:
            print(f"❌ Error processing {audio_file.name}: {e}")
            # Move back to input for retry
            processing_file.rename(audio_file)
    
    def _generate_sentiment_summary(self, spells: List[SentimentSpan]) -> Dict[str, Any]:
        """Generate summary statistics for sentiment analysis"""
        if not spells:
            return {}
        
        sentiment_counts = {}
        resolved_spells = []
        ongoing_spells = []
        total_resolved_duration = 0
        
        for spell in spells:
            sentiment_counts[spell.sentiment] = sentiment_counts.get(spell.sentiment, 0) + 1
            
            if spell.is_resolved:
                resolved_spells.append(spell)
                total_resolved_duration += spell.duration
            else:
                ongoing_spells.append(spell)
        
        return {
            'spell_distribution': sentiment_counts,
            'total_spells': len(spells),
            'resolved_spells': len(resolved_spells),
            'ongoing_spells': len(ongoing_spells),
            'total_resolved_duration': round(total_resolved_duration, 2),
            'ongoing_spell_types': [spell.sentiment for spell in ongoing_spells],
            'average_spell_duration': round(total_resolved_duration / len(resolved_spells), 2) if resolved_spells else 0,
            'dominant_sentiment': max(sentiment_counts, key=sentiment_counts.get) if sentiment_counts else None
        }
    
    def _save_csv_summary(self, filename_stem: str, spells: List[SentimentSpan]):
        """Save a CSV summary for easy spreadsheet analysis"""
        import csv
        
        csv_file = self.output_dir / f"{filename_stem}_sentiment_spells.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Spell_ID', 'Sentiment', 'Confidence', 'Start_Time', 'End_Time', 
                'Duration', 'Is_Ongoing', 'Is_Resolved', 'Intensity', 'Trigger_Words', 'Resolution_Words'
            ])
            
            for spell in spells:
                writer.writerow([
                    spell.spell_id,
                    spell.sentiment,
                    round(spell.confidence, 3),
                    round(spell.start_time, 2),
                    round(spell.end_time, 2) if spell.end_time else '',
                    round(spell.duration, 2) if spell.duration else '',
                    spell.is_ongoing,
                    spell.is_resolved,
                    spell.intensity,
                    '; '.join(spell.trigger_words),
                    '; '.join(spell.resolution_words)
                ])
        
        print(f"📈 CSV summary saved: {csv_file.name}")
    
    def process_existing_files(self):
        """Process any files already in the input directory"""
        audio_extensions = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}
        
        for audio_file in self.input_dir.iterdir():
            if audio_file.suffix.lower() in audio_extensions:
                self.process_file(audio_file)
                
                # Throttle between files
                print(f"💤 Sleeping for {self.sleep_time} seconds...")
                time.sleep(self.sleep_time)
    
    def watch_for_new_files(self):
        """Watch for new files being added"""
        class AudioFileHandler(FileSystemEventHandler):
            def __init__(self, processor):
                self.processor = processor
                
            def on_created(self, event):
                if not event.is_directory:
                    file_path = Path(event.src_path)
                    if file_path.suffix.lower() in {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}:
                        # Wait a bit to ensure file is fully copied
                        time.sleep(5)
                        self.processor.process_file(file_path)
                        time.sleep(self.processor.sleep_time)
        
        event_handler = AudioFileHandler(self)
        observer = Observer()
        observer.schedule(event_handler, str(self.input_dir), recursive=False)
        observer.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            observer.stop()
        observer.join()

if __name__ == "__main__":
    processor = WhisperBatchProcessor()
    
    # Process existing files first
    processor.process_existing_files()
    
    # Then watch for new files
    print("👁️ Watching for new audio files...")
    processor.watch_for_new_files()