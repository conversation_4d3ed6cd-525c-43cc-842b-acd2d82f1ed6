#!/usr/bin/env python3
import json
import os
import time
from pathlib import Path

import whisper
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer


class WhisperBatchProcessor:
    def __init__(self):
        self.model = whisper.load_model(os.getenv("WHISPER_MODEL", "base"))
        self.input_dir = Path("/app/input")
        self.output_dir = Path("/app/output")
        self.processing_dir = Path("/app/processing")
        self.sleep_time = int(os.getenv("SLEEP_BETWEEN_FILES", "30"))

    def process_file(self, audio_file):
        print(f"Processing: {audio_file.name}")

        # Move to processing directory
        processing_file = self.processing_dir / audio_file.name
        audio_file.rename(processing_file)

        try:
            # Transcribe with Whisper
            result = self.model.transcribe(str(processing_file))

            # Create your custom format output
            output_data = {
                "filename": audio_file.name,
                "transcript": result["text"],
                "segments": result.get("segments", []),
                "language": result.get("language", "unknown"),
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            }

            # Save transcription
            output_file = (
                self.output_dir / f"{audio_file.stem}_transcript.json"
            )
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Completed: {audio_file.name}")

            # Clean up processed file
            processing_file.unlink()

        except Exception as e:
            print(f"❌ Error processing {audio_file.name}: {e}")
            # Move back to input for retry
            processing_file.rename(audio_file)

    def process_existing_files(self):
        """Process any files already in the input directory"""
        audio_extensions = {".mp3", ".wav", ".m4a", ".flac", ".ogg"}

        for audio_file in self.input_dir.iterdir():
            if audio_file.suffix.lower() in audio_extensions:
                self.process_file(audio_file)

                # Throttle between files
                print(f"💤 Sleeping for {self.sleep_time} seconds...")
                time.sleep(self.sleep_time)

    def watch_for_new_files(self):
        """Watch for new files being added"""

        class AudioFileHandler(FileSystemEventHandler):
            def __init__(self, processor):
                self.processor = processor

            def on_created(self, event):
                if not event.is_directory:
                    file_path = Path(event.src_path)
                    if file_path.suffix.lower() in {
                        ".mp3",
                        ".wav",
                        ".m4a",
                        ".flac",
                        ".ogg",
                    }:
                        # Wait a bit to ensure file is fully copied
                        time.sleep(5)
                        self.processor.process_file(file_path)
                        time.sleep(self.processor.sleep_time)

        event_handler = AudioFileHandler(self)
        observer = Observer()
        observer.schedule(event_handler, str(self.input_dir), recursive=False)
        observer.start()

        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            observer.stop()
        observer.join()


if __name__ == "__main__":
    processor = WhisperBatchProcessor()

    # Process existing files first
    processor.process_existing_files()

    # Then watch for new files
    print("👁️ Watching for new audio files...")
    processor.watch_for_new_files()
