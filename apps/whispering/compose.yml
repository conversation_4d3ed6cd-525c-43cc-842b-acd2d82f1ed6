version: '3.8'

services:
  whisper-transcriber:
    build: .
    container_name: whisper-batch
    restart: unless-stopped
    
    # Resource limits - adjust these to throttle performance
    deploy:
      resources:
        limits:
          cpus: '0.5'      # Limit to 50% of one CPU core
          memory: 2G       # Limit to 2GB RAM
        reservations:
          cpus: '0.25'     # Reserve 25% of one CPU core
          memory: 512M     # Reserve 512MB RAM
    
    # CPU throttling via cgroups
    cpus: 0.5
    mem_limit: 2g
    
    # Volume mounts for your audio files
    volumes:
      - ./audio_input:/app/input      # Drop MP3s here
      - ./transcripts:/app/output     # Transcripts appear here
      - ./processing:/app/processing  # Temp processing folder
    
    environment:
      - WHISPER_MODEL=base           # or 'tiny' for faster/less accurate
      - SLEEP_BETWEEN_FILES=60       # Seconds between files
      - PYTHONUNBUFFERED=1
    
    # Run with lower priority
    privileged: false
    
    # Optional: CPU scheduling priority
    sysctls:
      - kernel.sched_rt_priority=1

  # Optional: Add a simple web UI to monitor progress
  whisper-monitor:
    image: nginx:alpine
    container_name: whisper-monitor
    ports:
      - "8080:80"
    volumes:
      - ./transcripts:/usr/share/nginx/html/transcripts:ro
      - ./monitor.html:/usr/share/nginx/html/index.html:ro