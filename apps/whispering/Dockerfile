FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install Whisper and dependencies
RUN pip install --no-cache-dir \
    openai-whisper \
    watchdog

# Create working directories
WORKDIR /app
RUN mkdir -p /app/input /app/output /app/processing

# Copy batch processing script
COPY batch_whisper.py /app/

# Set environment variables for throttling
ENV WHISPER_MODEL=base
ENV BATCH_SIZE=1
ENV SLEEP_BETWEEN_FILES=30
ENV CPU_NICE=19

# Run with low priority
CMD ["nice", "-n", "19", "python", "batch_whisper.py"]