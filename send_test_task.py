import os
import sys
from celery import Celery
sys.path.append("/home/<USER>/repo/knowtrails")
from packages.shared_tasks.test_tasks import add

# Configure Celery to connect to the running Redis instance
# Assuming Redis is exposed on localhost:6779 as per docker-compose.shared-services-dev.yml
# and using the credentials from .env-prod-celery
os.environ.setdefault('CELERY_BROKER_URL', 'redis://angels:ThisIsABetterOne@localhost:6779/0')
os.environ.setdefault('CELERY_RESULT_BACKEND', 'redis://angels:ThisIsABetterOne@localhost:6779/0')

app = Celery('test_celery_worker',
             broker=os.environ['CELERY_BROKER_URL'],
             backend=os.environ['CELERY_RESULT_BACKEND'],
             include=['packages.shared_tasks.test_tasks'])

if __name__ == '__main__':
    print("Sending add task to Celery worker...")
    result = add.delay(10, 5)
    print(f"Task ID: {result.id}")
    print("Waiting for task to complete (this might take a moment)...")
    try:
        # Get the result, with a timeout
        task_result = result.get(timeout=10)
        print(f"Task completed. Result: {task_result}")
    except Exception as e:
        print(f"Error getting task result: {e}")
        print("Ensure the Celery worker is running and connected to Redis.")
