# --------------------------------
# docker-compose.yml (Base)

version: '3.8'
services:
  web:
    build: ./web
    environment:
      - NODE_ENV=${NODE_ENV}
    ports:
      - 3000:3000
    volumes:
      - ./web:/app

# --------------------------------
# docker-compose.override.yml (Dev)

version: '3.8'
services:
  web:
    environment:
      - NODE_ENV=development
    volumes:
      - ./web:/app
    ports:
      - 3005:3000


# --------------------------------
# docker-compose.prod.yml (Prod)

version: '3.8'
services:
  web:
    environment:
      - NODE_ENV=production
    volumes:
      - ./web:/app
    ports:
      - 3000:3000

# 
# turbo.json
