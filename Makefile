#!/usr/bin/make
##################################################################################
# Makefile for the repo
# Author: Angels Meta
# Usage: make [target]
# Description: Makefile for the repo
#    Run a turbo command with the given filters and target
#    Manage logs
#	 See help with 'make usage'
#
# History: 2023-05-22: Angels Meta: Initial version.
#          2023-05-30: Angels Meta: Add testing targets, spawns playwrite
#
# I hope I can showcase this extended tooling for turborepo.  how would I 
# reach out to see if I have something useful.
##################################################################################

SHELL := /bin/bash

# Ensure the shell stops on errors
.SHELLFLAGS = -e -c

# Define a macro to run shared services
# 1 = buildMode (e.g., prod, dev)
# 2 = clean (e.g., true, false)
# 3 = up (e.g., true, false)
define shared_services_macro
	./repo-scripts/run-shared-services.sh "$(1)" "$(2)" "$(3)"
endef

# Define a macro to build with docker bake and then compose up
# 1 = appName (e.g., trans_api, web)
# 2 = buildMode (e.g., prod, dev)
# 3 = optional bake args (e.g., --set target.no-cache=true)
define bake_and_compose_macro
	./repo-scripts/run-bake-and-compose.sh "$(1)" "$(2)" "$(3)"
endef

# TODO add test container with playwright	./repo-scripts/run-bake-and-compose.sh "$(1)"
# Define the recipe for testing prod with
# - playwright testing the web (not implemented yet)
# - pytest for the back end code (not implemented yet)
# - something for the FastApi tests (not implemented yet)
define system_test_macro
	shared_services_macro prod
	./repo-scripts/run-bake-and-compose.sh "trans_api" "prod" "$(1)"
	./repo-scripts/run-bake-and-compose.sh "web" "prod" "$(1)"
	./repo-scripts/run-bake-and-compose.sh "test" "prod" 
endef

# $(call bake_and_compose_macro,trans_api,$1)
# $(call bake_and_compose_macro,web,$1)

define turbo_macro
	./repo-scripts/run-turbo.sh "$(1)" "$(2)" "$(3)"
endef


#  2873  docker-compose -f docker-compose.shared-services-prod.yml down
#  2874  docker-compose -f docker-compose.shared-services-prod.yml up -d redis-prod
#  2875  docker-compose -f docker-compose.shared-services-prod.yml ps            
#  2878  docker-compose -f docker-compose.shared-services-prod.yml up -d celery-prod

# shared-services ==================================================================================================================================
# shared-services-prod-down:
# 	@echo "Shutting down shared services (prod)..."
# 	docker-compose -f docker-compose.shared-services-prod.yml down

# shared-services-prod-up:
# 	@echo "Bringing up shared services (prod)..."
# 	docker compose -f docker-compose.shared-services-prod.yml up -d

# shared-services-dev-down:
# 	@echo "Shutting down shared services (dev)..."	
# 	docker-compose -f docker-compose.shared-services-dev.yml down

# shared-services-dev-up:	
# 	@echo "Bringing up shared services (dev)..."
# 	docker compose -f docker-compose.shared-services-dev.yml up -d


shared-services-dev-up:
	@echo "Bringing up shared services (dev)..."
	$(call shared_services_macro,dev,false,true)

shared-services-dev-up-clean:
	@echo "Bringing up shared services (dev)..."
	$(call shared_services_macro,dev,true,true)

shared-services-dev-down:
	@echo "Shutting down shared services (dev)..."	
	$(call shared_services_macro,dev,true,false)

shared-services-prod-up:
	@echo "Bringing up shared services (prod)..."
	$(call shared_services_macro,prod,false,true)

shared-services-prod-up-clean:
	@echo "Bringing up shared services (prod)..."
	$(call shared_services_macro,prod,true,true)

shared-services-prod-down:
	@echo "Shutting down shared services (prod)..."
	$(call shared_services_macro,prod,true,false)


# trans_api ========================================================================================================================================
# independent of turbo
# bake_and_compose_macro trans_api prod
# bake_and_compose_macro trans_api dev
#
.PHONY: trans_api-prod trans_api-prod-force trans_api-dev trans_api-dev-force 
trans_api-prod: shared-services-prod-up
	$(call bake_and_compose_macro,trans_api,prod)
trans_api-prod-force: shared-services-prod-down shared-services-prod-up
	$(call bake_and_compose_macro,trans_api,prod,--set trans_api-prod-runner.no-cache=true)
trans_api-dev: shared-services-dev-up
	$(call bake_and_compose_macro,trans_api,dev)
trans_api-dev-force: shared-services-dev-down shared-services-dev-up
	$(call bake_and_compose_macro,trans_api,dev,--set trans_api-dev-runner.no-cache=true)
#
# turbo run chain versions
# turbo_macro trans_api prod
# turbo_macro trans_api dev
#
.PHONY: turbo_trans_api-prod turbo_trans_api-prod-force turbo_trans_api-dev turbo_trans_api-dev-force test-trans_api-dev
turbo_trans_api-prod: shared-services-prod-up
	$(call turbo_macro,trans_api,build)
turbo_trans_api-prod-force: shared-services-prod-down shared-services-prod-up
	$(call turbo_macro,trans_api,build,force)
turbo_trans_api-dev: shared-services-dev-up
	$(call turbo_macro,trans_api,dev)
turbo_trans_api-dev-force: shared-services-dev-down shared-services-dev-up
	$(call turbo_macro,trans_api,dev,force)
turbo-test-trans_api-dev: shared-services-dev-up
	$(call turbo_macro,tooling-trans_api,test)
turbo-test-trans_api-dev-force: shared-services-dev-down shared-services-dev-up
	$(call turbo_macro,tooling-trans_api,test,force)	
#	# $(call turbo_macro,tooling-trans_api,test-dev)

# web =============================================================================================================================================
# independent of turbo
# bake_and_compose_macro web prod
# bake_and_compose_macro web dev
#
.PHONY: web-dev web-dev-force web-prod web-prod-force
web-dev: shared-services-dev-up
	$(call bake_and_compose_macro,web,dev)
web-dev-force: shared-services-dev-down shared-services-dev-up
	$(call bake_and_compose_macro,web,dev,--set web-dev-runner.no-cache=true)
web-prod: shared-services-prod-up
	$(call bake_and_compose_macro,web,prod)
web-prod-force: shared-services-prod-down shared-services-prod-up
	$(call bake_and_compose_macro,web,prod,--set web-prod-runner.no-cache=true)
#
# turbo run chain versions
# turbo_macro web prod
# turbo_macro web dev
#
.PHONY: turbo_web-dev turbo_web-dev-force turbo_web-prod turbo_web-prod-force
turbo_web-dev: shared-services-dev-up
	$(call turbo_macro,web,dev)
turbo_web-dev-force: shared-services-dev-down shared-services-dev-up
	$(call turbo_macro,web,dev,force)
turbo_web-prod: shared-services-prod-up
	$(call turbo_macro,web,build)
turbo_web-prod-force: shared-services-prod-down shared-services-prod-up
	$(call turbo_macro,web,build,force)

# discord-trans ===================================================================================================================================
.PHONY: discord_trans-dev discord_trans-dev-force discord_trans-prod discord_trans-prod-force

discord_trans-dev: shared-services-dev-up
	$(call bake_and_compose_macro,discord-trans,dev)
discord_trans-dev-force: shared-services-dev-down shared-services-dev-up
	$(call bake_and_compose_macro,discord-trans,dev,--set discord_trans-dev-runner.no-cache=true)
discord_trans-prod: shared-services-prod-up
	$(call bake_and_compose_macro,discord-trans,prod)
discord_trans-prod-force: shared-services-prod-down shared-services-prod-up
	$(call bake_and_compose_macro,discord-trans,prod,--set discord_trans-prod-runner.no-cache=true)

.PHONY: turbo_discord_trans-dev turbo_discord_trans-dev-force turbo_discord_trans-prod turbo_discord_trans-prod-force
turbo_discord_trans-dev: shared-services-dev-up
	$(call turbo_macro,discord-trans,dev)
turbo_discord_trans-dev-force: shared-services-dev-down shared-services-dev-up
	$(call turbo_macro,discord-trans,dev,force)
turbo_discord_trans-prod: shared-services-prod-up
	$(call turbo_macro,discord-trans,build)
turbo_discord_trans-prod-force: shared-services-prod-down shared-services-prod-up
	$(call turbo_macro,discord-trans,build,force)

# tran-hits =======================================================================================================================================
.PHONY: tran_hits-dev tran_hits-dev-force tran_hits-prod tran_hits-prod-force

tran_hits-dev: shared-services-dev-up
	$(call bake_and_compose_macro,tran-hits,dev)
tran_hits-dev-force: shared-services-dev-down shared-services-dev-up
	$(call bake_and_compose_macro,tran-hits,dev,--set tran_hits-dev-runner.no-cache=true)
tran_hits-prod: shared-services-prod-up
	$(call bake_and_compose_macro,tran-hits,prod)
tran_hits-prod-force: shared-services-prod-down shared-services-prod-up
	$(call bake_and_compose_macro,tran-hits,prod,--set tran_hits-prod-runner.no-cache=true)

.PHONY: turbo_tran_hits-dev turbo_tran_hits-dev-force turbo_tran_hits-prod turbo_tran_hits-prod-force
turbo_tran_hits-dev: shared-services-dev-up
	$(call turbo_macro,tran-hits,dev)
turbo_tran_hits-dev-force: shared-services-dev-down shared-services-dev-up
	$(call turbo_macro,tran-hits,dev,force)
turbo_tran_hits-prod: shared-services-prod-up
	$(call turbo_macro,tran-hits,build)
turbo_tran_hits-prod-force: shared-services-prod-down shared-services-prod-up
	$(call turbo_macro,tran-hits,build,force)

# system =============================================================================================================================================
# independent of turbo
# system_test_macro prod
# system_test_macro dev
#
.PHONY: system-prod system-prod-force system-dev system-dev-force 
system-prod: trans_api-prod web-prod
system-prod-force: trans_api-prod-force web-prod-force
system-test-prod: system-prod-force
	$(call system_test_macro,prod)

system-dev: trans_api-dev web-dev
system-dev-force: trans_api-dev-force web-dev-force
system-test-dev: system-dev-force
	$(call system_test_macro,dev)
#
# turbo run chain versions
# turbo_macro system prod
# turbo_macro system dev
#
.PHONY: turbo_system-prod turbo_system-prod-force turbo_system-dev turbo_system-dev-force 
turbo_system-prod: shared-services-prod-up
	turbo run build
turbo_system-prod-force: shared-services-prod-down shared-services-prod-up
	turbo run build --force
turbo_system-dev: shared-services-dev-up
	turbo run dev
turbo_system-dev-force: shared-services-dev-down shared-services-dev-up
	turbo run dev --force

## Logging targets ===================================================================================================================================
define GET_LATEST_LOG
$(shell ls -t $(REPO_LOG_DIR)/*.log 2>/dev/null | head -n 1)
endef

.PHONY: print-vars
print-vars:
	@echo "Turbo repo!"
	@echo "Turbo env vars: 1 $(REPO_LOG_DIR) 2 $(REPO_DEBUG) 3  $(TURBO_ADD_DEBUG_TIMESTAMP)"

.PHONY: log-lastone
log-lastone:
	@latest="$(call GET_LATEST_LOG)"; \
	if [ -z "$$latest" ]; then \
		printf "⚫ No logs found.\n"; \
	else \
		printf "Showing latest log:\n📊 %s\n" "$$latest"; \
		grep -E "(⏰|MAKEFILE|MAKEFILE DURATION|Turbo command finished)" "$$latest" || cat "$$latest"; \
	fi

.PHONY: log-list
log-list:
	@printf "Logs in %s:\n" "$(REPO_LOG_DIR)"; \
	ls -lt $(REPO_LOG_DIR)/*.log 2>/dev/null || printf "⚫ No logs found.\n"

.PHONY: log-clean
log-clean:
	@printf " Deleting all l*-ogs in %s...\n" "$(REPO_LOG_DIR)"; \
	rm -fv $(REPO_LOG_DIR)/*.log 2>/dev/null || printf "⚫ No logs to delete.\n"

.PHONY: log-trim
log-trim:
	@printf " Cleaning logs in %s...\n" "$(REPO_LOG_DIR)"; \
	if [ "$(MODE)" = "all" ]; then \
		rm -fv $(REPO_LOG_DIR)/*.log 2>/dev/null || printf "⚫ No logs to delete.\n"; \
	else \
		find $(REPO_LOG_DIR) -name '*.log' | sort | head -n -10 | xargs -r rm -fv || printf "⚫ Fewer than 10 logs, none deleted.\n"; \
	fi	

.PHONY: usage help
usage help:
	@printf "\nUsage: make [target]\n\n"; \
	printf "Common Targets:\n"; \
	printf "  web-dev                  # Run 'dev' for web\n"; \
	printf "  web-prod                 # Run 'build' for web\n"; \
	printf "  trans_api-dev            # Run 'dev' for trans_api\n"; \
	printf "  trans_api-prod           # Run 'build' for trans_api\n"; \
	printf "  discord_trans-dev        # Run 'dev' for discord-trans\n"; \
	printf "  discord_trans-prod       # Run 'build' for discord-trans\n"; \
	printf "  tran_hits-dev            # Run 'dev' for tran-hits\n"; \
	printf "  tran_hits-prod           # Run 'build' for tran-hits\n"; \
	printf "  web-dev-force            # Force dev for web\n"; \
	printf "  web-prod-force           # Force build for web\n"; \
	printf "  trans_api-dev-force      # Force dev for trans_api\n"; \
	printf "  trans_api-prod-force     # Force build for trans_api\n"; \
	printf "  discord_trans-dev-force  # Force dev for discord-trans\n"; \
	printf "  discord_trans-prod-force # Force build for discord-trans\n"; \
	printf "  tran_hits-dev-force      # Force dev for tran-hits\n"; \
	printf "  tran_hits-prod-force     # Force build for tran-hits\n"; \
	printf "  system-dev               # Run 'dev' for the entire system\n"; \
	printf "  system-prod              # Run 'build' for the entire system\n"; \
	printf "  system-dev-force         # Force dev for the entire system\n"; \
	printf "  system-prod-force        # Force build for the entire system\n\n"; \
	printf "  turbo_web-dev            # Run 'dev' for web with Turbo\n"; \
	printf "  turbo_web-prod           # Run 'build' for web with Turbo\n"; \
	printf "  turbo_trans_api-dev      # Run 'dev' for trans_api with Turbo\n"; \
	printf "  turbo_trans_api-prod     # Run 'build' for trans_api with Turbo\n"; \
	printf "  turbo_discord_trans-dev  # Run 'dev' for discord-trans with Turbo\n"; \
	printf "  turbo_discord_trans-prod # Run 'build' for discord-trans with Turbo\n"; \
	printf "  turbo_tran_hits-dev      # Run 'dev' for tran-hits with Turbo\n"; \
	printf "  turbo_tran_hits-prod     # Run 'build' for tran-hits with Turbo\n"; \
	printf "  turbo_web-dev-force      # Force dev for web with Turbo\n"; \
	printf "  turbo_web-prod-force     # Force build for web with Turbo\n"; \
	printf "  turbo_trans_api-dev-force# Force dev for trans_api with Turbo\n"; \
	printf "  turbo_trans_api-prod-force# Force build for trans_api with Turbo\n"; \
	printf "  turbo_discord_trans-dev-force  # Force dev for discord-trans with Turbo\n"; \
	printf "  turbo_discord_trans-prod-force # Force build for discord-trans with Turbo\n"; \
	printf "  turbo_tran_hits-dev-force      # Force dev for tran-hits with Turbo\n"; \
	printf "  turbo_tran_hits-prod-force     # Force build for tran-hits with Turbo\n"; \
	printf "  turbo_system-dev         # Run 'dev' for the entire system with Turbo\n"; \
	printf "  turbo_system-prod        # Run 'build' for the entire system with Turbo\n"; \
	printf "  turbo_system-dev-force   # Force dev for the entire system with Turbo\n"; \
	printf "  turbo_system-prod-force  # Force build for the entire system with Turbo\n\n"; \
	printf "Log Admin:\n"; \
	printf "  log-list                 # List all recent logs\n"; \
	printf "  log-lastone              # Print summary of latest log\n"; \
	printf "  log-clean                # Remove all logs\n"; \
	printf "  log-trim                 # Trim logs\n\n"; \
	printf "  help, usage              # Show this help message\n"; \
	printf "  check-ai-exclusion <path># Check if path should be excluded from AI analysis\n\n"


## Todo: New Check these

.PHONY: status
status:
	@echo "Turbo Status:"; \
	turbo run build --dry-run --filter tooling-web

.PHONY: log-ci-tail
log-ci-tail:
	@cat $(shell ls -t $(REPO_LOG_DIR)/*.log | head -n1)

.PHONY: config-check
config-check:
	@sha256sum turbo.json | diff - expected-turbo.hash || echo "⚠️ turbo.json changed!"

.PHONY: check-ai-exclusion
check-ai-exclusion:
	@if [ -z "$(PATH_TO_CHECK)" ]; then \
		echo "Usage: make check-ai-exclusion PATH_TO_CHECK=<path>"; \
		echo "Example: make check-ai-exclusion PATH_TO_CHECK=apps/trans_api"; \
	else \
		./repo-scripts/check-ai-exclusion.sh "$(PATH_TO_CHECK)"; \
	fi
