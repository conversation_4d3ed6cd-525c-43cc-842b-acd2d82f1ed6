{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": true, "persistent": true}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db::studio": {"cache": false}, "tooling-web#test": {"inputs": [], "outputs": [], "cache": false, "persistent": false}, "tooling-web#build": {"dependsOn": ["^build"], "inputs": ["./../run-turbo.sh", "./../tooling/.env.web.build", "./../tooling/build-docker-compose.mjs", "./../../turbo.json", "./../../<PERSON><PERSON>le", "./../../apps/web/docker-compose-dev.yml", "./../../apps/web/docker-compose-prod.yml", "./../../apps/web/docker-compose-base.yml", "./../../apps/web/Dockerfile", "./../../apps/web/package.json", "./../../apps/web/dummy-file-web.txt", "./../../apps/web/app/**", "./../../apps/web/lib/**", "./../../apps/web/public/**", "./../../packages/ui/src/**", "./../../packages/tooling/.env.web.build", "!**/*.md", "!**/.next/**", "!**/.turbo/**", "!**/node_modules/**", "!/tooling-build-logs/**"], "outputs": [], "cache": true, "persistent": false}, "tooling-trans_api#build": {"dependsOn": ["^build"], "inputs": ["./../run-turbo.sh", "./../tooling/.env.trans_api.build", "./../tooling/build-docker-compose.mjs", "./../../turbo.json", "./../../<PERSON><PERSON>le", "./../../apps/trans_api/.env-dev", "./../../apps/trans_api/.env-dev-build", "./../../apps/trans_api/.env-dev-redis", "./../../apps/trans_api/.env-prod", "./../../apps/trans_api/.env-prod-build", "./../../apps/trans_api/.env-prod-redis", "./../../apps/trans_api/docker-compose-dev.yml", "./../../apps/trans_api/docker-compose-prod.yml", "./../../apps/trans_api/docker-compose-base.yml", "./../../apps/trans_api/Dockerfile", "./../../apps/trans_api/package.json", "./../../apps/trans_api/pyproject.toml", "./../../apps/trans_api/uv.lock", "./../../apps/trans_api/log_configs/**", "./../../apps/trans_api/api/**", "./../../packages/tooling/.env.trans_api.build", "!/tooling-build-logs/**"], "outputs": [], "cache": true, "persistent": false}, "tooling-trans_api#test": {"inputs": [], "outputs": [], "cache": false, "persistent": false}, "docs-tran-hits#build": {"cache": true, "outputs": ["site/**"]}, "docs-tran-hits#serve": {"persistent": true, "cache": false}}}