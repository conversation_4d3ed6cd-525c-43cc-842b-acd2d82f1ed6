[{"type": "File", "name": "/home/<USER>/repo/knowtrails/apps/trans_api/models/drizzle_models.py", "parent_id": null, "workspaceRoot": "/home/<USER>/repo/knowtrails", "workspacePath": "apps/trans_api/models/drizzle_models.py", "id": "ozG06Ij8ViPmekZq"}, {"type": "File", "name": "/home/<USER>/repo/knowtrails/apps/trans_api/models/database.py", "parent_id": null, "workspaceRoot": "/home/<USER>/repo/knowtrails", "workspacePath": "apps/trans_api/models/database.py", "id": "cE4jzsinWad0ZqHr"}, {"type": "File", "name": "/home/<USER>/repo/knowtrails/packages/db/src/schema/py-gens.ts", "parent_id": null, "workspaceRoot": "/home/<USER>/repo/knowtrails", "workspacePath": "packages/db/src/schema/py-gens.ts", "id": "73jCOGPma6RV9THl"}]