[pytest]
# Directories to search for tests
testpaths = tests

# Patterns for test files, classes, and functions
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Add directories to sys.path for imports
# This is key for 'from trans_api.models.database import Base' to work
# if 'trans_api' is inside the 'apps' directory.
pythonpath = . apps

# Default command-line options
# addopts = -v --cov=apps --cov-report=html
# Example: addopts = -v

# Configure logging for tests (optional)
log_cli = true
log_cli_level = INFO
# You can also configure log format, date format, etc.
