# Assuming you already have a [project] section and potentially other sections

[tool.hatch.envs.script_env]
dependencies = [
  "Pillow",
  "Typer",
]
# You might also want to add dependencies needed by the script from other parts of the monorepo
# if it imports them, e.g., if it needed something from tran-hits or discord-trans.
# For this specific script, it seems standalone, so <PERSON><PERSON> and Type<PERSON> are enough.

# Define a script entry to make it easy to run
[tool.hatch.envs.script_env.scripts]
shrink = "python scripts/shrink_images.py"

# Example of a default environment (often implicitly exists or explicitly defined)
[tool.hatch.envs.default]
# Add core dependencies for your main project/library here

# Example of a dev environment (see below)
[tool.hatch.envs.dev]
dependencies = [
  # Add common development tools here
  "pytest",         # Testing framework
  "pytest-cov",     # Test coverage
  "ruff",           # Linter/Formatter
  "mypy",           # Type checking
  "build",          # Building packages
  "twine",          # Uploading packages
  # Add dependencies from your main apps/libraries if you need them available
  # in the dev environment for testing/development purposes.
  # e.g., ".[discord-trans]", ".[tran-hits]" if defined as optional dependencies
]

# You might also define dev scripts here, e.g., lint, test, build
[tool.hatch.envs.dev.scripts]
lint = "ruff check ."
format = "ruff format ."
test = "pytest"
cov = "pytest --cov=./src" # Adjust module path as needed
build = "python -m build"


# Activate the dev environment
#   cd /home/<USER>/repo/knowtrails/
#   hatch shell dev

# Run dev scripts directly from the monorepo root
#   cd /home/<USER>/repo/knowtrails/
#   hatch run dev+lint
#   hatch run dev+test


# Run command using script entry in toml
#   hatch run scripts+shrink /path/to/your/images 50

# Run command w/o using script entry in toml
#   hatch run scripts -- python scripts/shrink_images.py /path/to/your/images 50

# hatch run scripts+shrink \home\angels\repo\knowtrails\apps\discord-trans\src\discord_trans\biggies 50
# hatch run script_env+shrink /home/<USER>/repo/knowtrails/apps/discord-trans/src/discord_trans/biggies 50

# No setuptools package discovery needed for scripts directory.
# Use only Hatch envs and scripts for utility scripts.

#  python scripts/shrink_images.py /home/<USER>/repo/knowtrails/apps/discord-trans/src/discord_trans/biggies 50