services:
  postgres-prod:
    container_name: postgres-prod
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: knowledgetrails
    volumes:
      - postgres_data_driz_tut:/var/lib/postgresql/data
    networks:
      - shared_network

  redis-prod:
    container_name: redis-prod
    mem_limit: 200m
    cpu_shares: 102
    image: redis:latest
    restart: unless-stopped
    volumes:
      - redis_data_volume:/data
      - ./apps/trans_api/users.acl:/usr/local/etc/redis/users.acl
    networks:
      - shared_network
    ports:
      - "6379:6379"
    command: redis-server --protected-mode no --aclfile /usr/local/etc/redis/users.acl
    # ✅ Better Variant (if externally exposed):
    # TLS + ACL + controlled bind = a much stronger, production-ready Redis.
    # redis-server \
    #   --protected-mode no \
    #   --bind 0.0.0.0 \
    #   --aclfile /usr/local/etc/redis/users.acl \
    #   --tls-port 6379 \
    #   --tls-cert-file /path/to/cert.pem \
    #   --tls-key-file /path/to/key.pem \
    #   --tls-ca-cert-file /path/to/ca.pem

  celery-prod:
    container_name: celery_worker-prod
    # mem_limit: 200m
    # cpu_shares: 102
    build:
      context: .
      dockerfile: apps/celery_worker/Dockerfile-celery
      target: prod-runner
    env_file:
      - apps/celery_worker/.env-prod-celery
    restart: unless-stopped
    networks:
      - shared_network
    depends_on:
      - redis-prod
    command: ["celery", "-A", "worker", "worker", "-l", "info"]

volumes:
  postgres_data_driz_tut:
  redis_data_volume:

networks:
  shared_network:
    external: true
