# AI Analysis Exclusion File
# This file lists directories and files that AI analysis tools should exclude
# from their preview and agency operations.

# Stashed Code Areas - Require explicit confirmation before interaction
apps/__Cherries__/
apps/_FUTURE_/

# Future legacy areas that started but may not be aligned when they exist
apps/trans_api/
packages/db/
apps/websocket-server/
apps/playwright_docker/

# utility not directly related to project code
gen-scripts/
tooling-build-logs-old/

# Build artifacts and logs
**/node_modules/
**/.next/
**/dist/
**/build/
**/__pycache__/
**/.pytest_cache/
**/coverage/
**/.coverage
**/logs/
**/*.log

# Database files
*.db
*.sqlite
*.sqlite3
migration.db
test.db

# Environment and config files with sensitive data
.env
.env.local
.env.production
.env.staging
**/.env*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Lock files (usually auto-generated)
pnpm-lock.yaml
package-lock.json
yarn.lock
uv.lock

# Temporary files
temp_*
*.tmp
*.temp
