#!/bin/bash

# =============================================================================
# run-bake-and-compose.sh
# Author: Angels Meta
# Usage: Helper script to build and compose Docker images.
# Description: Encapsulates the logic for docker buildx bake and docker compose up.
#
# Arguments:
#   $1: appName (e.g., trans_api, web)
#   $2: buildMode (e.g., prod, dev)
#   $3: optional bake args (e.g., --set target.no-cache=true)
#
# History: 2025-06-04: Angels Meta: Initial version.
# =============================================================================

source "$(dirname "$0")/repo-env-setup.sh"
source "$(dirname "$0")/log_utils.sh"

# Function to display usage
usage() {
    logLabError "Usage: $0 <appName> <buildMode> [optional_bake_args]"
    exit 1
}

# Check for correct number of arguments
if [ "$#" -lt 2 ]; then
    usage
fi

APP_NAME="$1"
BUILD_MODE="$2"
OPTIONAL_BAKE_ARGS="$3"

# Define log file and redirect all output
LOG_FILE="${REPO_LOG_DIR}/$(date -u +%Y%m%d-%H%M%S)-${APP_NAME}-${BUILD_MODE}-bake-compose.log"
mkdir -p "$(dirname "$LOG_FILE")" || { logLabError "Failed to create log directory."; exit 1; }
exec > >(tee -a "$LOG_FILE") 2>&1

logLabInfo "Starting docker build and compose for ${APP_NAME}-${BUILD_MODE}"
_log_swallow_err_ "Saving current shell options"
save_shell_opts

# Change to the tooling directory to run bake
(
    cd packages/tooling || { logLabError "Failed to change directory to packages/tooling."; exit 1; }
    logLabInfo "Changed directory to packages/tooling for bake operations."

    local_bakeManifest=$(docker buildx bake --allow=fs.read=${GIT_REPO_DIR} -f docker-bake.hcl "${APP_NAME}-${BUILD_MODE}-runner" ${OPTIONAL_BAKE_ARGS} --print) ;
    
    if [[ -n "${local_bakeManifest}" ]]  then
        if [[ -n "${REPO_DEBUG_MODE:-}" ]]; then 
            logLabDebug "Bake manifest: ${local_bakeManifest}"
        fi
        # Export IMAGE_TAG so it's available to the parent shell
        export IMAGE_TAG=$(echo "$local_bakeManifest" | jq -r ".target[\"${APP_NAME}-${BUILD_MODE}-runner\"].tags[0]")
        logLabInfo "Determined image tag: ${IMAGE_TAG}"

        # Now, actually build the image using bake
        logLabExecute "docker buildx bake for ${APP_NAME}-${BUILD_MODE}-runner"
        BAKE_COMMAND="docker buildx bake --allow=fs.read=${GIT_REPO_DIR} -f docker-bake.hcl \"${APP_NAME}-${BUILD_MODE}-runner\" ${OPTIONAL_BAKE_ARGS}"
        logLabExecute "${BAKE_COMMAND}"

        if ! docker buildx bake --allow=fs.read=${GIT_REPO_DIR} -f docker-bake.hcl "${APP_NAME}-${BUILD_MODE}-runner" ${OPTIONAL_BAKE_ARGS}; then
            logLabError "Failed to build ${APP_NAME}-${BUILD_MODE}-runner with docker buildx bake."
            exit 1 # Exit subshell
        fi
        logLabComplete "docker buildx bake successful."
        logLabInfo "Buildx caching contributes to build speed. Look for 'CACHED' layers in the build output (above in this log file) to see cache hits."
    else
        logLabError "Failed to generate bake manifest for ${APP_NAME}-${BUILD_MODE}-runner."
        exit 1 # Exit subshell
    fi

    # The rest of the script (docker compose) remains outside the subshell
    COMPOSE_FILES="../../apps/${APP_NAME}/docker-compose-base.yml:../../apps/${APP_NAME}/docker-compose-${BUILD_MODE}.yml"

    COMPOSE_UP_COMMAND="COMPOSE_FILE=\"${COMPOSE_FILES}\" IMAGE=\"${IMAGE_TAG}\" docker compose up -d --force-recreate \"${APP_NAME}\""
    logLabInfo "Executing docker compose for ${APP_NAME}-${BUILD_MODE}"
    logLabExecute "${COMPOSE_UP_COMMAND}"
    logLabDebug "Determined image tag: ${IMAGE_TAG}"

    if COMPOSE_FILE="${COMPOSE_FILES}" IMAGE="${IMAGE_TAG}" docker compose up -d --force-recreate "${APP_NAME}"; then
        logLabComplete "${APP_NAME} docker build and compose up"
    else
        logLabError "Failed to bring up ${APP_NAME} with docker compose."
        restore_shell_opts # Restore before exiting on error
        exit 1
    fi

) # End of subshell, automatically returns to original directory

_log_swallow_err_ "Restoring previous shell options"
restore_shell_opts

logLabComplete "Docker build and compose operation complete for ${APP_NAME}-${BUILD_MODE}."
