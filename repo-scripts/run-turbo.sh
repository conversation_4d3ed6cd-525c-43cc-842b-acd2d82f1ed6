#!/bin/bash
# =============================================================================
# run-turbo.sh
# Author: Angels Meta
# Usage: ./run-turbo.sh "filter1 filter2" "target" "force"
# Description:
#     Runs turbo with the given TOOLING_TARGETS
#     Makefile dependency for turborepo system engeneering.
#     Orchastrates system builds for turbo and docker hybrid.
# 
# History: 2025-05-22: Angels Meta: Initial version.     
# =============================================================================

# TODO: review https://turborepo.com/docs/reference/run#--cache-options

# --- CRITICAL: Source the main environment setup script ---
# This ensures all necessary functions (like check_env_ready, log_repo_error)
# and environment variables (like REPO_LOG_DIR, REPO_LOG_TIMESTAMP) are available.
# Use the same relative path logic as repo-env-setup.sh
source "$(dirname "${BASH_SOURCE[0]}")/repo-env-setup.sh"

_log_swallow_err_ "Saving current shell options"
save_shell_opts
check_env_ready

TOOLING_TARGETS="$1"
TOOLING_MODE="$2"
FORCE_FLAG="$3" # optional

if [ $# -lt 2 ]; then
  log_repo_error "Integration error"
  echo "Usage: $0 \"filter1 filter2\" \"target\" [force]" | tee /dev/stderr
  exit 1
fi

if [[ "$TOOLING_TARGETS" == "" ]]; then
  log_repo_error "No TOOLING_TARGETS provided"
  exit 1
fi

if [[ "$TOOLING_MODE" == "" ]]; then
  log_repo_error "No TOOLING_MODE provided"
  exit 1
fi

if [[ "$FORCE_FLAG" == "force" ]]; then
  echo "Forcing rebuild"
  FORCE_FLAG="--force"
else
  FORCE_FLAG=""
fi

allowed_tooling_modes=(
  "build"
  "dev"
  "test"
)
# Function to check if an element is in the allowed list
# This function is highly robust.
is_item_allowed() {
  local search_mode="$1"
  local -a allowed_list=("${@:2}") # Capture remaining args as the list
  echo "🔍 Checking if [$search_mode] is in the allowed list: ${allowed_list[@]}"
  # Convert the array to a newline-separated string for grep
  # -q: quiet (no output)
  # -w: whole word match (ensures 'test' doesn't match 'testing')
  # -F: fixed strings (no regex interpretation in the search string)
  if printf "%s\n" "${allowed_list[@]}" | grep -qFwx -- "$search_mode"; then
    echo true
  else
    echo false
  fi
}

if ! is_item_allowed "$TOOLING_MODE" "${allowed_tooling_modes[@]}"; then
  log_repo_error "Unknown mode: [$TOOLING_MODE]"
  echo "Error: Unknown mode: $TOOLING_MODE"
  exit 1
fi

mkdir -p "$REPO_LOG_DIR"

TIMESTAMP=$(date +%s)
[[ "$REPO_LOG_TIMESTAMP" != "yes" ]] && TIMESTAMP=""

SAFE_FILTER=$(echo "$TOOLING_TARGETS" | sed 's/[[:space:]]\+/-/g')
LOG_FILE="$REPO_LOG_DIR/$TIMESTAMP-turbo-$SAFE_FILTER-$TOOLING_MODE.txt"

TURBO_LOG_DEBUG=""
if [[ "$REPO_DEBUG_MODE" == "yes" ]]; then
  TURBO_LOG_DEBUG="env TURBO_LOG_LEVEL=debug"
fi

# Extended turborepo tooling task targets sanity check 
allowed_turbo_proj_target_filters=(
  "tooling-web"
  "tooling-trans_api"
  "web"
  "trans_api"
)

COMMAND="$TURBO_LOG_DEBUG turbo run $TOOLING_MODE"
# Check if $tt is in the allowed list
for tt in $TOOLING_TARGETS; do
  if ! is_item_allowed "$tt" "${allowed_turbo_proj_target_filters[@]}"; then
    log_repo_error "Unknown filter: $tt"
    exit 1
  fi
  COMMAND="$COMMAND --filter=$tt"
done  

COMMAND="$COMMAND $FORCE_FLAG"
echo "🧬 Turbo filters : $TOOLING_TARGETS" >> "$LOG_FILE" 
echo "🎯 Target mode : $TOOLING_MODE" >> "$LOG_FILE"
echo "🐞 TURBO_LOG_DEBUG: $TURBO_LOG_DEBUG" >> "$LOG_FILE"
echo "🚀 executing: $COMMAND" >> "$LOG_FILE"
start_time=$(date +%s)

$COMMAND 2>&1 | tee "$LOG_FILE"

status=$?
end_time=$(date +%s)    
duration=$((end_time - start_time))
echo "Turbo command finished in $duration seconds."
echo "MAKEFILE DURATION: Turbo command finished in $duration seconds" >> "$LOG_FILE"

if [[ $status -eq 0 ]]; then
  echo "🌲 MAKEFILE COMPLETED" >> "$LOG_FILE"
else
  echo "💥 MAKEFILE FAILED" >> "$LOG_FILE"
  log_repo_error "MAKEFILE FAILED"
fi

if grep -q "FULL TURBO" "$LOG_FILE"; then
  NEW_FILE="${LOG_FILE%.txt}-🎇.txt"
  mv "$LOG_FILE" "$NEW_FILE"
  LOG_FILE="$NEW_FILE"
fi
echo "📑 saved to: $LOG_FILE"

_log_swallow_err_ "Restoring shell options"
restore_shell_opts

exit $status
