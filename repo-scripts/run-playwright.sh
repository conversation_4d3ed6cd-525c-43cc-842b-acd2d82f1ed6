#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Source the repository environment setup script
source "$(dirname "$0")/repo-env-setup.sh"

echo "Running Playwright tests via build-docker-compose.mjs..."

# Set environment variables for build-docker-compose.mjs
# SERVICES should include 'web' and 'playwright' to ensure web-prod and playwright services are considered.
# buildMode should be 'dev' for testing.
# IS_COMP should be true to run composeUp.
# IS_BUILD should be false as we are not building images here, just composing.
# IS_TEST_MODE should be true to trigger the composeTest function in build-docker-compose.mjs.

# Ensure web-prod and postgres are up before running playwright tests
# This is handled by build-docker-compose.mjs when IS_TEST_MODE is true
# and services include 'web' and 'playwright'.

# Execute build-docker-compose.mjs with test mode enabled
# We pass 'web' as appName and 'dev' as buildMode, as playwright tests typically run against dev environment
# The 'services' variable will be picked up from the environment.
SERVICES="web playwright" IS_COMP=true IS_BUILD=false IS_TEST_MODE=true node packages/tooling/build-docker-compose.mjs web dev

echo "Playwright test orchestration completed."
