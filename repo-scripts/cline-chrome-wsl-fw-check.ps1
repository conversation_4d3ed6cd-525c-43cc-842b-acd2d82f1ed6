$RuleName = "WSL Chrome Debug"
$LocalPort = 9222
$Protocol = "TCP"
$Direction = "Inbound"

# Check if a rule with the exact DisplayName exists
$existingRule = Get-NetFirewallRule -DisplayName $RuleName -ErrorAction SilentlyContinue

# Check if a rule with the specific parameters (port, protocol, direction) exists
# This is more robust as DisplayName isn't strictly unique
$existingRuleByParams = Get-NetFirewallRule | Where-Object {
    $_.LocalPort -eq $LocalPort -and
    $_.Protocol -eq $Protocol -and
    $_.Direction -eq $Direction -and
    $_.DisplayName -eq $RuleName # Optional: also match display name for this check
}

if ($existingRule) {
    Write-Host "A firewall rule with DisplayName '$RuleName' already exists."
    # You might want to enable it if it's disabled, or modify it if needed
    # Example: Enable-NetFirewallRule -DisplayName $RuleName
} elseif ($existingRuleByParams) {
    Write-Host "A firewall rule with matching port, protocol, and direction already exists (DisplayName: $($existingRuleByParams.DisplayName))."
    # This catches cases where the DisplayName might be slightly different but the functional rule is the same
} else {
    Write-Host "Firewall rule '$RuleName' does not exist. Creating it now..."
    New-NetFirewallRule -DisplayName $RuleName -Direction $Direction -LocalPort $LocalPort -Protocol $Protocol -Action Allow
    Write-Host "Firewall rule '$RuleName' created successfully."
}