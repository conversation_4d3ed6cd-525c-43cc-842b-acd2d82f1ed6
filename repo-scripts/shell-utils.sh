#!/bin/bash
# =============================================================================
# shell-utils.sh
# Author: Angels Meta
# Usage: dependency of repo-env-setup.sh setup process
# Description: Utility functions
# Enforce that script is sourced (must be called from the outer script)
#
# # usage of the toggle functions to target specific code block
#
# save_shell_opts
#
# toggle_debug on
# toggle_trap on
#
# # Your risky code here
# false
#
# toggle_trap off
# toggle_debug off
#
# restore_shell_opts
#
# History: 2025-05-22: Angels Meta: Initial version.
# =============================================================================

# # --- UNCOMMENT LINE to force debug mode ---
# set -x

# Enforce that script is sourced (must be called from the outer script)
# enforce_sourced() {
#   (return 0) 2>/dev/null || {
#     echo "❗ This script must be sourced, not executed directly."
#     return 1
#   }

#   [[ -n "${BASH_VERSION:-}" ]] || {
#     echo "❗ This script requires a Bash-compatible shell."
#     return 1
#   }
# }

# # Save current shell options
# save_shell_opts() {
#   # --- TEMPORARY: SIMPLIFY THIS FUNCTION ---
#   echo "save_shell_opts called - TEMPORARY DIAGNOSTIC"
#   # SHELL_OPTS_BEFORE=()
#   # while read -r line; do
#   #   SHELL_OPTS_BEFORE+=("$line")
#   # done < <(set +o)
# }

# # Restore previously saved shell options
# restore_shell_opts() {
#   # --- TEMPORARY: SIMPLIFY THIS FUNCTION ---
#   echo "restore_shell_opts called - TEMPORARY DIAGNOSTIC"
#   # echo "Restoring shell options ..."
#   # for opt in "${SHELL_OPTS_BEFORE[@]}"; do
#   #   echo " 	→ $opt"
#   #   eval "$opt"
#   # done
#   # unset SHELL_OPTS_BEFORE
# }

# restore after resting : TEMP ABOVE and commented below

# Enforce that script is sourced (must be called from the outer script)
enforce_sourced() {
  (return 0) 2>/dev/null || {
    echo "❗ This script must be sourced, not executed directly."
    return 1
  }

  [[ -n "${BASH_VERSION:-}" ]] || {
    echo "❗ This script requires a Bash-compatible shell."
    return 1
  }
}

# Save current shell options
save_shell_opts() {
  SHELL_OPTS_BEFORE=()
  while read -r line; do
    SHELL_OPTS_BEFORE+=("$line")
  done < <(set +o)
}

# Restore previously saved shell options
restore_shell_opts() {
  logLabDebug "in restore_shell_opts ( REPO_DEBUG_MODE: $REPO_DEBUG_MODE )"
  for opt in "${SHELL_OPTS_BEFORE[@]}"; do
    [[ "${REPO_DEBUG_MODE:-}" == "true" ]] && echo "  → $opt"
    eval "$opt"
  done
  unset SHELL_OPTS_BEFORE
}

##############################################^^^^
 
# Error trap function
trap_err() {
  if declare -F log_repo_error >/dev/null; then
    log_repo_error "Error at line $LINENO: command \`$BASH_COMMAND\` failed."
  else
    echo "Error at line $LINENO: command \`$BASH_COMMAND\` failed." >&2
  fi
}

  _log_swallow_err_() {
  # [[ "${REPO_DEBUG_MODE:-}" == "yes" ]] && echo "🔧 [toggle] $*" >&2
  if [ -t 2 ]; then
    echo "🔧 [toggle] $*" >&2
  fi
}

# Set strict error handling
set_common_safety_flags() {
  _log_swallow_err_ "Enabling strict mode: set -euo pipefail"
  set -euo pipefail
}

# Toggle debug mode (tracing)
toggle_debug() {
  local mode="${1:-on}"
  case "$mode" in
  on)
    _log_swallow_err_ "Debug mode ON"
    export REPO_DEBUG_MODE=yes
    set -o xtrace
    ;;
  off)
    _log_swallow_err_ "Debug mode OFF"
    export REPO_DEBUG_MODE=no
    set +o xtrace
    ;;
  *)
    echo "Invalid debug mode: $mode (use on/off)" >&2
    return 1
    ;;
  esac
}

# Toggle error trapping
toggle_trap() {
  local mode="${1:-on}"
  case "$mode" in
  on)
    _log_swallow_err_ "Error trap ON"
    export REPO_DEBUG_TRAP=yes
    trap trap_err ERR
    ;;
  off)
    _log_swallow_err_ "Error trap OFF"
    export REPO_DEBUG_TRAP=no
    trap - ERR
    ;;
  *)
    echo "Invalid trap mode: $mode (use on/off)" >&2
    return 1
    ;;
  esac
}

# Set strict error handling flags safely (only once)
repo_set_strict_mode() {
  if [[ -n "${_REPO_STRICT_MODE_SET:-}" ]]; then
    return 0
  fi

  set -euo pipefail
  _REPO_STRICT_MODE_SET=1
}

# gen utils

convert_epoch() {
  local epoch="${1:-}"
  if [[ -z "${epoch}" ]]; then
    date +%s # current epoch
    date
  else
    date -d "@$1" # convert provided epoch to date
  fi
}

ep_conv() {
  local epoch="${1:-}"

  if [[ -z "${epoch}" ]]; then
    epoch="$(date +%s)"
  fi

  local epoch_str="$(date -d "@$epoch")"
  echo -e "$epoch -> $epoch_str"
}
