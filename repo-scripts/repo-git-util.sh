#!/bin/bash

# =============================================================================
# repo-git-util.sh
# Author: Angels Meta
# Usage: git and GitHub workflow and convenience functions
# Description: repo_help or repo_usage
#
# History: 2025-05-22: Angels Meta: Initial version.
# =============================================================================

enforce_sourced || return 1

# Command dispatcher (could enhance dynamic with the repo_help() output)
# usage: repo grbo
if ! declare -F kt > /dev/null; then
  repo() {
    case "$1" in
    gfo) repo_gfo ;;
    grbo) repo_grbo ;;
    grboi) repo_grboi ;;
    grbost) repo_grbost ;;
    gfp-safe) repo_gbofp_safe ;;
    set-gh-token) repo_ghtoken ;;
    *)
      echo "Unknown kt command"
      return 1
      ;;
    esac
  }
fi

ensure_not_main_branch() {
  local branch
  branch=$(git symbolic-ref --short HEAD)
  if [[ "$branch" == "main" ]]; then
    log_repo_error "Operation aborted: you're on main."
    return 1
  fi
}

if ! declare -F repo_grbo >/dev/null; then
  repo_grbo() {
    ensure_not_main_branch || return 1
    git stash
    git fetch origin
    git rebase origin/main
    git stash pop
    echo "💎 git stash rebase pop"
  }
fi

if ! declare -F repo_grboi >/dev/null; then
  repo_grboi() {
    git rebase -i origin/main
    echo "💎 git rebase -i origin/main"
  }
fi

if ! declare -F repo_gbofp_safe >/dev/null; then
  repo_gbofp_safe() {
    ensure_not_main_branch || return 1
    git fetch origin
    git rebase -i origin/main
    git push --force-with-lease
    echo "💎 git fetch origin, rebased force pushed with lease"
  }
fi

if ! declare -F repo_gfo >/dev/null; then
  repo_gfo() {
    git fetch origin
    echo "💎 git fetch origin"
  }
fi

if ! declare -F repo_usage >/dev/null; then
  repo_usage() {

    echo -e "\nUsage:"
    echo -e "To use debug mode: REPO_DEBUG_MODE=yes source repo-env-setup.sh"
    echo -e "Check ~/.repo-error.log for errors"
    echo -e "repo_help will list available commands" 

    echo -e "\ngit function aliases:\n"
    echo -e "\trepo_gfo             fetch origin"
    echo -e "\trepo_grbo            rebase origin/main"
    echo -e "\trepo_grbost          rebase origin/main with stashing and restore"
    echo -e "\trepo_grboi           rebase origin/main interactive"
    echo -e "\trepo_gbofp_safe      rebase and force push to protect main"
    echo -e "\trepo_ghtoken         set your GitHub PAT"

    echo -e "\nNotes:"
    echo -e "🔓 The command set-gh-token sets your GitHub personal access token 🐙 for use in workflows"
    echo -e "✨ Edits need to be sourced in freah shell. 'source repo-scripts/repo-env-setup.sh'"

    echo -e "\nExperimental:\n  repo gfo style commands for repo_gfo\n  considering a repo command dispatcher"
  }
fi

if ! declare -F repo_help >/dev/null; then
  repo_help() {
    echo "Available kt commands:"
    declare -F | awk '{print $3}' | grep '^repo_' | sort
  }
fi
