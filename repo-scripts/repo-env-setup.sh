#!/bin/bash
# =============================================================================
# repo-env-setup.sh
# Author: Angels Meta
# Usage: Main entry script to set up the environment.
# Description: Sets up the environment for the repo scripts.
#
# History: 2025-05-22: Angels Meta: Initial version.
# =============================================================================
export REPO_ENV_VERSION="0.6.1"
echo "🧭 Repo Tooling Version: $REPO_ENV_VERSION"

# Moved to shared services env
# SQLITE config for early development
export DATABASE_URL="sqlite+aiosqlite:///test.db"
echo "DATABASE_URL: $DATABASE_URL"

# Once defined, repo scripts can use enforce_sourced function to check file is
# sourced and not executed directly.
(return 0) 2>/dev/null || {
  echo "❗ This script must be sourced, not executed directly."
  return 1
}

REPO_DEBUG_MODE=${REPO_DEBUG_MODE:-}
  
# Resolve the directory of the current script
REPO_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "REPO_SCRIPT_DIR *************: ${REPO_SCRIPT_DIR}"
source "${REPO_SCRIPT_DIR}/shell-utils.sh"

_log_swallow_err_ "Saving current shell options"
save_shell_opts
repo_set_strict_mode

source "${REPO_SCRIPT_DIR}/repo-logging.sh"
source "${REPO_SCRIPT_DIR}/log_utils.sh"
source "${REPO_SCRIPT_DIR}/repo-set-dir.sh"
set_repo_dir || return 1
echo "Set discord env"
source "${REPO_SCRIPT_DIR}/.env-repo-discord"
source "${REPO_SCRIPT_DIR}/repo-doctor.sh"
source "${REPO_SCRIPT_DIR}/repo-git-util.sh"

export REPO_LOG_DIR="${GIT_REPO_DIR}/tooling-build-logs"
export REPO_LOG_TIMESTAMP=yes

echo
echo -e " REPO_LOG_DIR       : ${REPO_LOG_DIR}"
echo -e " REPO_DEBUG_MODE    : ${REPO_DEBUG_MODE:-no}"
echo -e " REPO_LOG_TIMESTAMP : ${REPO_LOG_TIMESTAMP}"

export REPO_ENV_READY="loaded-v1" # signals environment ready

# Set up global log file for all script output
if [[ -n "${REPO_LOG_DIR:-}" && "${REPO_LOG_TIMESTAMP:-}" == "yes" ]]; then
    # Use a unique log file name for each script execution
    # Get the base name of the script being executed
    SCRIPT_NAME=$(basename "${BASH_SOURCE[0]}")
    # Remove extension for cleaner log file names
    SCRIPT_NAME_NO_EXT="${SCRIPT_NAME%.*}"
    
    GLOBAL_LOG_FILE="${REPO_LOG_DIR}/$(date -u +%Y%m%d-%H%M%S)-${SCRIPT_NAME_NO_EXT}.log"
    mkdir -p "$(dirname "$GLOBAL_LOG_FILE")" 2>/dev/null # Ensure log directory exists, suppress error if already exists
    exec > >(tee -a "$GLOBAL_LOG_FILE") 2>&1
    echo "📑 All script output being logged to: $GLOBAL_LOG_FILE"
fi

if ! declare -F check_env_ready &>/dev/null; then
  check_env_ready() {
    if [[ "${REPO_ENV_READY:-}" != "loaded-v1" ]]; then
      echo "❗ Environment needs to be initialized with 'source repo-scripts/repo-env-setup.sh'"
      (return 1) 2>/dev/null || exit 1
    fi
  }
fi

_log_swallow_err_ "Restoring previous shell options"
restore_shell_opts

repo_doctor

echo "🧙 Repo tooling complete."
echo
echo "Use 'repo_usage' to see available git and GitHub commands."
echo "Use 'make usage' to see available make commands."
