#!/bin/bash
###############################################################################
# repo-doctor.sh
# Author: Angels Meta
# Usage: setup health checks for repo-env-setup.sh setup process
# Description: repo doctor
#
# History: 2025-05-22: Angels Meta: Initial version.
###############################################################################

# Detect if script is sourced or run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  repo_doctor "$@"
  exit $? # safe to exit here because script is run directly
fi

repo_doctor() {
  echo -e "\n🩺 Running repo doctor checks ..."

  # 1. Check GIT_REPO_DIR exists
  # TODO consider checking matches git rev-parse
  if [[ -z "$GIT_REPO_DIR" ]]; then
    echo "💥 GIT_REPO_DIR not set"
    exit 1
  fi

  if [[ "$(git -C "$GIT_REPO_DIR" rev-parse --is-inside-work-tree 2>/dev/null)" != "true" ]]; then
    echo "💥 $GIT_REPO_DIR is not a valid Git working tree"
    exit 1
  else
    echo "🌲 valid Git working tree found: $GIT_REPO_DIR."
  fi

  # 2. Check required tools
  local required_tools=("git" "gh" "pnpm" "node" "docker")
  check_tool() {
    if ! command -v "$1" >/dev/null 2>&1; then
      echo "💥 Missing tool: $1. Try installing via: brew/npm/apt as appropriate."
      return 1
    fi
  }
  for tool in "${required_tools[@]}"; do
    echo -n "   - $tool: "; command -v "$tool" || echo "missing"
    check_tool "$tool" || {
      log_repo_error "Missing tool: $tool. Install it before continuing."
      exit 1;
    }
    done

  echo "🌲 All required tools present"

  # 3. Check GH_PAT_PATH (optional)
  # if [[ -n "$GH_PAT_PATH" ]]; then
  #   if [[ ! -f "$GH_PAT_PATH" ]]; then
  #     echo "💥 GH_PAT_PATH is set but file not found: $GH_PAT_PATH"
  #     exit 1
  #   fi
  #   if ! grep -q '^ghp_' "$GH_PAT_PATH"; then
  #     echo "⚠️  **unwired 4 now ** GH_PAT_PATH does not appear to contain a GitHub token"
  #   else
  #     echo "🌲 GH_PAT_PATH valid"
  #   fi
  # fi

  echo "🌲 Environment setup complete."

  return 0
}
