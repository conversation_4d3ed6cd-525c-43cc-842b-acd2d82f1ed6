#!/bin/bash
logLabCreate() {
    echo "👼 created $1"
}
logLabExecute() {
    echo "🚀 executing: $1"
}
logLabComplete() {
    echo "🌲 completed $1"
}
logLabSkip() {
    echo "⏩ skipping $1"
}
logLabDebug() {
    echo "🐞 $1"
}

logLabError() {
    echo "💥 BOOM $1"
}

logLabInfo() {
    echo "🔘 $1"
}


export -f logLabCreate
export -f logLabExecute
export -f logLabComplete
export -f logLabSkip
export -f logLabDebug
export -f logLabError
