#!/bin/bash

# =============================================================================
# run-shared-services.sh
# Author: Angels Meta
# Description: compose manager for shared services
# 1 = buildMode (prod, dev)	
# 2 = remove images before starting default false (true, false)
# 3 = compose images up: default true (true, false)
# History: 2025-06-04: Angels Meta: Initial version.
# =============================================================================
# Todo clean so idempotent works 100%
source "$(dirname "$0")/repo-env-setup.sh"

_log_swallow_err_ "Saving current shell options"
save_shell_opts
check_env_ready # ??????????
# repo_set_strict_mode      # ??????????

usage() {
    echo "Usage: $0 <dev|prod> [<true|false] [true|false]"
    exit 1
}

# Check for correct number of arguments
if [ "$#" -lt 1 ]; then
    usage
fi

BUILD_MODE="$1"

if [ "$BUILD_MODE" != "dev" ] && [ "$BUILD_MODE" != "prod" ]; then
    logLabError "Invalid build mode: ${BUILD_MODE}. Must be 'dev' or 'prod'."
    usage
fi

SHARED_SERVICES_COMPOSE_FILE="docker-compose.shared-services-${BUILD_MODE}.yml"

REMOVE_BEFORE_START="${2:-false}"
if [ "$REMOVE_BEFORE_START" = "true" ]; then
    logLabInfo "Removing shared services before starting (${BUILD_MODE})..."
    if docker compose -f "${SHARED_SERVICES_COMPOSE_FILE}" down --remove-orphans; then
        logLabComplete "Shared services removed successfully."
    else
        logLabError "Could not remove shared services (they might not be running or an error occurred)."
    fi
fi

PREFORM_COMPOSE_UP="${3:-true}"
if [ "$PREFORM_COMPOSE_UP" = "true" ]; then
    logLabExecute "Bringing up shared services (${BUILD_MODE})..."
    if docker compose -f "${SHARED_SERVICES_COMPOSE_FILE}" up -d; then
        logLabComplete "Shared services brought up successfully."
    else
        logLabError "Failed to bring up shared services."
        exit 1
    fi
fi

logLabComplete "Shared services operation complete."

_log_swallow_err_ "Restoring previous shell options"
restore_shell_opts

# repo_doctor

echo "🧙 Repo tooling complete."
echo
echo "Use 'repo_usage' to see available git and GitHub commands."
echo "Use 'make usage' to see available make commands."
