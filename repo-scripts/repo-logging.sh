#!/bin/bash
###############################################################################
# repo-logging.sh
# Author: Angels Meta
# Usage: dependency of repo-env-setup.sh setup process
# Description: Logging functions for the repo scripts.
#
# History: 2025-05-22: Angels Meta: Initial version.
###############################################################################

if ! declare -F log_repo_error >/dev/null; then
  log_repo_error() {
    local log_file="${REPO_LOG_DIR:-.}/repo-error.log"
    local project_dir="${REPO_LOG_DIR:-NOT_SET}"
    local size
    size=$(wc -c <"$log_file" 2>/dev/null || echo 0)
    if [[ $size -gt 1000000 ]]; then
      >"$log_file"
    fi
    echo -e "💥 $(date -Is) $*" >&2
    echo -e "💥 [$project_dir] $(date -Is) $*" >>"$log_file"
  }
fi
