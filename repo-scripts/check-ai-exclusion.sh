#!/bin/bash

# Check if a path should be excluded from AI analysis
# Usage: ./check-ai-exclusion.sh <path>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(dirname "$SCRIPT_DIR")"

# Check if path argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <path>"
    echo "Example: $0 apps/trans_api"
    exit 1
fi

TARGET_PATH="$1"

# Legacy/Stashed code areas that should be excluded
LEGACY_PATHS=(
    "apps/trans_api"
    "apps/websocket-server" 
    "apps/playwright_docker"
    "apps/__Cherries__"
    "apps/_FUTURE_"
    "packages/db"
    "tooling-build-logs-old"
    "gen-scripts"
)

# Check if the path matches any legacy pattern
is_legacy=false
for legacy_path in "${LEGACY_PATHS[@]}"; do
    if [[ "$TARGET_PATH" == "$legacy_path"* ]]; then
        is_legacy=true
        break
    fi
done

# Output result
if [ "$is_legacy" = true ]; then
    echo -e "${RED}🚫 LEGACY CODE AREA${NC}"
    echo -e "Path: ${YELLOW}$TARGET_PATH${NC}"
    echo -e "Status: ${RED}Should be excluded from AI analysis${NC}"
    echo -e "Action: ${YELLOW}Requires explicit confirmation before interaction${NC}"
    exit 1
else
    echo -e "${GREEN}✅ ACTIVE DEVELOPMENT AREA${NC}"
    echo -e "Path: ${YELLOW}$TARGET_PATH${NC}"
    echo -e "Status: ${GREEN}Safe for AI analysis and interaction${NC}"
    exit 0
fi
