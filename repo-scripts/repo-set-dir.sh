#!/bin/bash
###############################################################################
# repo-set-dir.sh
# Author: Angels Meta
# Usage: dependency of repo-env-setup.sh setup process
# Description: set the repo directory and export GIT_REPO_DIR
# Option: call with force to reinitialize (override)
#
# History: 2025-05-22: Angels Meta: Initial version.
###############################################################################

enforce_sourced || return 1

if ! declare -F set_repo_dir >/dev/null; then

  set_repo_dir() {

    command -v git >/dev/null 2>&1 || {
      log_repo_error "git command not found. Please install git."
      return 1
    }

    local force=${1:-no}  # Pass "force" to override

    if [[ "${force}" != "force" && -n "${GIT_REPO_DIR:-}" ]]; then
      echo "GIT_REPO_DIR already set to: $GIT_REPO_DIR (not overridden)"
      return 0
    fi

    if git rev-parse --show-toplevel >/dev/null 2>&1; then
      ## export GIT_REPO_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
      export GIT_REPO_DIR="$(git rev-parse --show-toplevel)"
      echo -e "\n🏗️  Setting up repo environment in ${GIT_REPO_DIR:-}"
      return 0
    else
      log_repo_error "Not in a Git repository. GIT_REPO_DIR not set."
      return 1
    fi
  }

fi
