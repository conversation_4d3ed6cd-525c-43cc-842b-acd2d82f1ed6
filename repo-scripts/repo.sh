################################################################################
# repo.sh
# Author: Angels Meta
# Usage: ./repo.sh [command]
# Description: Main entry point for the repo scripts.
# History: 2025-05-22: Angels Meta: Initial version.
################################################################################
main() {

  # 🧭 Parse command or mode
  local cmd="$1"
  shift || true
  echo "🚀 Running command: $cmd"
  case "$cmd" in
    doctor)
      source "$(dirname "${BASH_SOURCE[0]}")/repo-doctor.sh"
      ;;

    help|"")
      echo "Usage: ./repo-scripts/repo.sh [command]"
      echo "Commands:"
      echo "  doctor     Run system checks"
      echo "  help       Show this message"
      ;;
    *)
      echo "Unknown command: $cmd" >&2
      exit 1
      ;;
  esac
}

# Only run main if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  main "$@"
fi